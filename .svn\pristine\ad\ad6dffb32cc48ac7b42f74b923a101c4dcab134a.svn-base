# Repository Monitor - Development Guide

## Quick Start

### Prerequisites
- <PERSON><PERSON> and <PERSON>er Compose
- Git

### Development Setup

1. **<PERSON><PERSON> and navigate to the project**
   ```bash
   git clone <repository-url>
   cd svn_monitor_server
   ```

2. **Start development environment**
   ```bash
   # Using PowerShell script (Windows)
   .\start-repository-monitor.ps1

   # Or using batch script (Windows)
   start-repository-monitor.bat

   # Or manually with Docker Compose
   docker-compose -f docker-compose.dev.yml up -d
   ```

3. **Access the application**
   - Web Interface: http://localhost:5001
   - Logs: `docker-compose -f docker-compose.dev.yml logs -f`

## Development Features

### Hot Reload
The development setup includes automatic hot reload for:
- **Python files**: Any `.py` file changes trigger application restart
- **Templates**: Jinja2 templates reload automatically
- **Static files**: CSS, JS, and other static assets reload immediately

### Volume Mounting Strategy
The development configuration uses a **single volume mount** approach:
```yaml
volumes:
  - .:/app  # Mount entire project directory
```

This is much more maintainable than individual file mounts because:
- ✅ **Automatic**: New files are immediately available
- ✅ **Simple**: No need to update docker-compose.yml for new files
- ✅ **Complete**: All source code changes are reflected instantly
- ✅ **Efficient**: Uses `.dockerignore` to exclude unnecessary files

### Environment Configuration
Development environment variables are managed in `.env.dev`:
- `REPOSITORY_MONITOR_ENV=development`
- `FLASK_DEBUG=1`
- `PYTHONUNBUFFERED=1`
- And more...

## File Structure

```
svn_monitor_server/
├── repository_monitor_app.py      # Main application entry point
├── repository_backends/           # Plugin-based repository backends
│   ├── __init__.py               # Backend manager
│   ├── base.py                   # Abstract base class
│   ├── svn_backend.py            # SVN implementation
│   └── git_backend.py            # Git implementation (placeholder)
├── templates/                    # Jinja2 templates
├── static/                       # CSS, JS, images
├── docker-compose.dev.yml        # Development Docker configuration
├── Dockerfile.dev                # Development Docker image
├── .env.dev                      # Development environment variables
├── .dockerignore                 # Docker build exclusions
└── requirements.txt              # Python dependencies
```

## Common Development Tasks

### Adding New Python Files
Simply create the file - no docker-compose.yml changes needed!

### Debugging
- **Logs**: `docker-compose -f docker-compose.dev.yml logs -f repository-monitor`
- **Shell Access**: `docker-compose -f docker-compose.dev.yml exec repository-monitor bash`
- **Python Debugger**: Set breakpoints with `import pdb; pdb.set_trace()`

### Testing
```bash
# Run tests inside container
docker-compose -f docker-compose.dev.yml exec repository-monitor python -m pytest

# Or run tests locally (if you have Python environment)
python -m pytest
```

### Stopping Development Environment
```bash
docker-compose -f docker-compose.dev.yml down
```

## Architecture

### Plugin System
Repository backends are implemented as plugins:
- **Base Class**: `repository_backends.base.RepositoryBackend`
- **Manager**: `repository_backends.RepositoryBackendManager`
- **Implementations**: SVN (complete), Git (placeholder)

### Adding New Repository Backend
1. Create new file in `repository_backends/`
2. Inherit from `RepositoryBackend`
3. Implement required methods
4. Register in the backend manager

## Troubleshooting

### Container Won't Start
```bash
# Check logs
docker-compose -f docker-compose.dev.yml logs repository-monitor

# Rebuild if needed
docker-compose -f docker-compose.dev.yml build --no-cache
```

### Hot Reload Not Working
- Ensure you're using `docker-compose.dev.yml`
- Check that files are being mounted: `docker-compose -f docker-compose.dev.yml exec repository-monitor ls -la`

### Port Already in Use
```bash
# Stop any existing containers
docker-compose -f docker-compose.dev.yml down

# Check what's using port 5001
netstat -ano | findstr :5001  # Windows
lsof -i :5001                 # macOS/Linux
```
