{% extends "base.html" %}

{% block title %}Dashboard - RepoSense AI{% endblock %}

{% block content %}
<div class="page-header">
    <div>
        <h1 class="page-title">Dashboard</h1>
        <p class="page-subtitle">AI-powered repository intelligence and monitoring dashboard</p>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-tachometer-alt"></i>System Status</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <strong>Monitoring Status:</strong>
                            <span class="status-indicator {% if status.running %}status-running{% else %}status-stopped{% endif %}"></span>
                            <span id="monitoring-status">{% if status.running %}Running{% else %}Stopped{% endif %}</span>
                        </div>
                        <div class="mb-3">
                            <strong>Configuration:</strong>
                            <span class="status-indicator {% if status.config_valid %}status-running{% else %}status-warning{% endif %}"></span>
                            {% if status.config_valid %}Valid{% else %}Incomplete{% endif %}
                        </div>
                        <div class="mb-3">
                            <strong>Ollama Connection:</strong>
                            <span class="status-indicator {% if status.ollama_connected %}status-running{% else %}status-stopped{% endif %}"></span>
                            <span id="ollama-status">{% if status.ollama_connected %}Connected{% else %}Disconnected{% endif %}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <strong>Repositories:</strong>
                            <span class="badge bg-primary">{{ status.enabled_repositories_count }}</span> enabled /
                            <span class="badge bg-secondary">{{ status.total_repositories_count }}</span> total
                        </div>
                        <div class="mb-3">
                            <strong>Email Service:</strong>
                            <span class="status-indicator {% if status.email_configured %}status-running{% else %}status-warning{% endif %}"></span>
                            {% if status.email_configured %}Configured{% else %}Not configured{% endif %}
                        </div>
                        <div class="mb-3">
                            <strong>Last Check:</strong>
                            <small class="text-muted" id="last-check">
                                {% if status.last_check %}{{ status.last_check }}{% else %}Never{% endif %}
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-code-branch"></i>Repositories</h5>
                <a href="{{ url_for('repositories_page') }}" class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-cog"></i> Manage
                </a>
            </div>
            <div class="card-body">
                {% if status.repositories %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Status</th>
                                    <th>Last Revision</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for repo in status.repositories %}
                                <tr>
                                    <td>{{ repo.name }}</td>
                                    <td>
                                        <span class="status-indicator {% if repo.enabled %}status-running{% else %}status-stopped{% endif %}"></span>
                                        {% if repo.enabled %}Enabled{% else %}Disabled{% endif %}
                                    </td>
                                    <td><span class="badge bg-info">{{ repo.last_revision }}</span></td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-3">
                        <i class="fas fa-code-branch fa-2x text-muted mb-2"></i>
                        <p class="text-muted mb-2">No repositories configured</p>
                        <a href="{{ url_for('repositories_page') }}" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> Add Repository
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-cogs"></i>Current Configuration</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-2"><strong>Check Interval:</strong> {{ config.check_interval }} seconds</div>
                        <div class="mb-2"><strong>Ollama Model:</strong> {{ config.ollama_model }}</div>
                        <div class="mb-2"><strong>Generate Docs:</strong> 
                            {% if config.generate_docs %}
                                <span class="badge bg-success">Enabled</span>
                            {% else %}
                                <span class="badge bg-secondary">Disabled</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-2"><strong>Send Emails:</strong> 
                            {% if config.send_emails %}
                                <span class="badge bg-success">Enabled</span>
                            {% else %}
                                <span class="badge bg-secondary">Disabled</span>
                            {% endif %}
                        </div>
                        <div class="mb-2"><strong>Email Recipients:</strong> {{ config.email_recipients|length }} configured</div>
                        <div class="mb-2"><strong>SMTP Host:</strong> {{ config.smtp_host }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-play-circle"></i>Controls</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-success" id="start-btn" {% if status.running %}disabled{% endif %}>
                        <i class="fas fa-play"></i> Start Monitoring
                    </button>
                    <button class="btn btn-danger" id="stop-btn" {% if not status.running %}disabled{% endif %}>
                        <i class="fas fa-stop"></i> Stop Monitoring
                    </button>
                    <button class="btn btn-info" id="check-btn">
                        <i class="fas fa-sync"></i> Check Now
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh status every 30 seconds
setInterval(function() {
    fetch('/api/status')
        .then(response => response.json())
        .then(data => {
            // Update monitoring status
            const monitoringStatus = document.getElementById('monitoring-status');
            if (monitoringStatus) {
                monitoringStatus.textContent = data.running ? 'Running' : 'Stopped';
            }

            // Update Ollama status
            const ollamaStatus = document.getElementById('ollama-status');
            if (ollamaStatus) {
                ollamaStatus.textContent = data.ollama_connected ? 'Connected' : 'Disconnected';
            }

            // Update last check time
            const lastCheck = document.getElementById('last-check');
            if (lastCheck && data.last_check) {
                lastCheck.textContent = data.last_check;
            }

            // Update buttons
            const startBtn = document.getElementById('start-btn');
            const stopBtn = document.getElementById('stop-btn');
            if (startBtn) startBtn.disabled = data.running;
            if (stopBtn) stopBtn.disabled = !data.running;

            // Update repository count badges
            const enabledBadges = document.querySelectorAll('.badge.bg-primary');
            const totalBadges = document.querySelectorAll('.badge.bg-secondary');
            if (enabledBadges.length > 0) {
                enabledBadges[0].textContent = data.enabled_repositories_count || 0;
            }
            if (totalBadges.length > 0) {
                totalBadges[0].textContent = data.total_repositories_count || 0;
            }
        })
        .catch(error => console.error('Error fetching status:', error));
}, 30000);

// Control buttons
const startBtn = document.getElementById('start-btn');
const stopBtn = document.getElementById('stop-btn');
const checkBtn = document.getElementById('check-btn');

if (startBtn) {
    startBtn.addEventListener('click', function() {
        fetch('/api/start', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to start monitoring: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error starting monitoring:', error);
                alert('Failed to start monitoring: Network error');
            });
    });
}

if (stopBtn) {
    stopBtn.addEventListener('click', function() {
        fetch('/api/stop', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Failed to stop monitoring: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error stopping monitoring:', error);
                alert('Failed to stop monitoring: Network error');
            });
    });
}

if (checkBtn) {
    checkBtn.addEventListener('click', function() {
        fetch('/api/check', { method: 'POST' })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Manual check completed!');
                    location.reload();
                } else {
                    alert('Check failed: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error running check:', error);
                alert('Check failed: Network error');
            });
    });
}
</script>

{% endblock %}