#!/usr/bin/env python3
"""
Test script for the new diff service functionality
"""

import sys
import os
from datetime import datetime

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from diff_service import DiffService
from document_database import DocumentRecord

def test_diff_service():
    """Test the diff service with sample data"""
    print("Testing Diff Service...")
    
    # Create a sample document record with repository metadata
    sample_document = DocumentRecord(
        id="test_repo_5",
        repository_id="test_repo",
        repository_name="Test Repository",
        revision=5,
        date=datetime.now(),
        filename="revision_5.md",
        filepath="/app/data/output/repositories/Test Repository/docs/revision_5.md",
        size=1024,
        author="test_user",
        commit_message="Test commit message",
        code_review_recommended=1,
        code_review_priority="medium",
        documentation_impact="yes",
        risk_level="medium",
        file_modified_time=datetime.now().timestamp(),
        processed_time=datetime.now(),
        repository_url="http://sundc:81/svn/test_repo",  # Sample SVN URL
        repository_type="svn"
    )
    
    # Test the diff service
    diff_service = DiffService()
    
    print(f"Document ID: {sample_document.id}")
    print(f"Repository URL: {sample_document.repository_url}")
    print(f"Repository Type: {sample_document.repository_type}")
    print(f"Revision: {sample_document.revision}")
    
    # Check if diff can be generated
    can_generate = diff_service.can_generate_diff(sample_document)
    print(f"Can generate diff: {can_generate}")
    
    if can_generate:
        print("\nAttempting to generate diff...")
        diff_content = diff_service.get_diff_for_document(sample_document)
        
        if diff_content:
            print("Diff generated successfully!")
            print(f"Diff content length: {len(diff_content)} characters")
            print("\nFirst 500 characters of diff:")
            print("-" * 50)
            print(diff_content[:500])
            print("-" * 50)
        else:
            print("Failed to generate diff content")
    else:
        print("Cannot generate diff for this document")

if __name__ == "__main__":
    test_diff_service()
