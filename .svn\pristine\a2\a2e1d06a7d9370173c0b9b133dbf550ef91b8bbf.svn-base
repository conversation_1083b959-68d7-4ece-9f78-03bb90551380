# SVN Monitor Integration with Existing Docker Setup

This guide shows how to integrate SVN Monitor with your existing Docker Compose setup that includes Ollama, LLM Proxy, and Open WebUI.

## Current Setup Analysis

Your existing `docker-compose.yml` includes:
- **ollama-server-local**: Ollama server with GPU support (RTX 3050)
- **llm-proxy-server-local**: LLM proxy server on port 11440
- **open-webui-local**: Web interface on port 3000
- **ollama-network**: Shared network for all services

## Integration Steps

### 1. Start Your Existing Services

First, ensure your main Docker Compose is running:

```bash
# In your main project directory
docker-compose up -d
```

Verify services are running:
```bash
docker ps
# Should show: ollama-server-local, llm-proxy-server-local, open-webui-local
```

### 2. Add SVN Monitor

Navigate to the SVN Monitor directory and run:

**PowerShell (Recommended):**
```powershell
.\setup-dev-windows.ps1
```

**Command Prompt:**
```cmd
.\setup-dev-windows.bat
```

**Manual Setup:**
```bash
# Copy configuration
copy config.example.json config.json

# Build and start SVN Monitor
docker-compose -f docker-compose.dev.yml up -d
```

### 3. Access Services

After integration, you'll have:

| Service | URL | Purpose |
|---------|-----|---------|
| SVN Monitor | http://localhost:5000 | Repository monitoring & configuration |
| Open WebUI | http://localhost:3000 | LLM chat interface |
| LLM Proxy | http://localhost:11440 | LLM proxy server |
| Ollama API | http://localhost:11434 | Direct Ollama access |

## Configuration

### SVN Monitor Configuration

Edit `config.json` to configure your repositories:

```json
{
  "repositories": [
    {
      "id": "my-project",
      "name": "My Project Repository",
      "url": "https://svn.mycompany.com/repos/myproject",
      "username": "your-username",
      "password": "your-password",
      "enabled": true,
      "branch_path": "trunk",
      "monitor_all_branches": false,
      "assigned_users": ["user-1"]
    }
  ],
  "ollama_host": "http://ollama-server-local:11434",
  "ollama_model": "llama2"
}
```

### Network Integration

The SVN Monitor automatically connects to your existing `ollama-network`, allowing it to:
- Access the Ollama server directly
- Share the same network namespace
- Communicate with other services if needed

## Branch Selection Features

Configure different monitoring strategies:

**Monitor Trunk Only:**
```json
{
  "branch_path": "trunk",
  "monitor_all_branches": false
}
```

**Monitor Specific Feature Branch:**
```json
{
  "branch_path": "branches/feature-authentication",
  "monitor_all_branches": false
}
```

**Monitor All Branches:**
```json
{
  "branch_path": null,
  "monitor_all_branches": true
}
```

## Development Workflow

### Daily Development

```bash
# Start your main services (if not already running)
docker-compose up -d

# Start SVN Monitor
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f svn-monitor

# Stop SVN Monitor (keeps main services running)
docker-compose -f docker-compose.dev.yml down
```

### Code Changes

The development setup includes hot reload:
1. Edit Python files in your editor
2. Save changes
3. SVN Monitor automatically restarts
4. Refresh browser to see changes

### Debugging

```bash
# View SVN Monitor logs
docker-compose -f docker-compose.dev.yml logs svn-monitor

# Access container shell
docker-compose -f docker-compose.dev.yml exec svn-monitor bash

# Check Ollama connectivity
docker-compose -f docker-compose.dev.yml exec svn-monitor python -c "
import requests
response = requests.get('http://ollama-server-local:11434/api/tags')
print('Ollama Status:', response.status_code)
print('Available Models:', response.json())
"
```

## GPU Utilization

Your existing Ollama server has GPU support configured. The SVN Monitor will automatically benefit from this when:
- Generating commit documentation
- Processing large diffs
- Creating email summaries

No additional GPU configuration needed for SVN Monitor.

## Troubleshooting

### Common Issues

**SVN Monitor can't connect to Ollama:**
```bash
# Check if Ollama is running
docker ps | grep ollama-server-local

# Test Ollama connectivity
curl http://localhost:11434/api/tags
```

**Port conflicts:**
- SVN Monitor uses port 5000
- If port 5000 is in use, edit `docker-compose.dev.yml` to use a different port

**Network issues:**
```bash
# Check network connectivity
docker network ls | grep ollama-network
docker network inspect ollama-network
```

### Reset Development Environment

```bash
# Stop SVN Monitor
docker-compose -f docker-compose.dev.yml down

# Remove containers and rebuild
docker-compose -f docker-compose.dev.yml down --volumes
docker-compose -f docker-compose.dev.yml build --no-cache
docker-compose -f docker-compose.dev.yml up -d
```

## Benefits of Integration

1. **Shared Resources**: Uses your existing Ollama server with GPU acceleration
2. **Network Efficiency**: All services on same Docker network
3. **Unified Management**: Manage all AI services from one place
4. **Resource Optimization**: No duplicate Ollama instances
5. **Development Friendly**: Hot reload and debugging capabilities

## Next Steps

1. **Configure Repositories**: Add your SVN repositories in the web interface
2. **Set Up Users**: Create user accounts for team members
3. **Test Monitoring**: Enable a repository and verify commit detection
4. **Customize AI Models**: Use different models for different repositories
5. **Email Integration**: Configure SMTP for commit notifications

The SVN Monitor is now fully integrated with your existing Docker infrastructure and ready for development!
