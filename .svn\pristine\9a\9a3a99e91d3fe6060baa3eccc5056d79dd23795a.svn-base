#!/bin/bash
# Integrate RepoSense AI with existing docker-compose.yml

set -e

echo "🔗 RepoSense AI Integration Script"
echo "======================================"

# Check if docker-compose.yml exists
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ Error: docker-compose.yml not found in current directory!"
    echo "Please run this script from the directory containing your docker-compose.yml"
    exit 1
fi

# Backup existing docker-compose.yml
echo "💾 Backing up existing docker-compose.yml..."
cp docker-compose.yml docker-compose.yml.backup.$(date +%Y%m%d_%H%M%S)

# Create RepoSense AI service definition
echo "📝 Creating RepoSense AI service definition..."
cat > reposense-ai-service.yml << 'EOF'

  # RepoSense AI - AI-Powered Repository Documentation
  reposense-ai:
    container_name: reposense-ai
    build:
      context: ./reposense-ai
      dockerfile: Dockerfile
    restart: unless-stopped
    networks:
      - ollama-network
    ports:
      - "5000:5000"
    volumes:
      # Persistent data
      - reposense_ai_data:/app/data
      - reposense_ai_logs:/app/logs
      
      # Production config
      - ./reposense-ai-config/config.production.json:/app/data/config.json:ro

      # SVN credentials (uncomment if needed)
      # - ~/.subversion:/home/<USER>/.subversion:ro

      # Git credentials (uncomment if needed)
      # - ~/.gitconfig:/home/<USER>/.gitconfig:ro
    
    environment:
      - REPOSENSE_AI_CONFIG=/app/data/config.json
      - REPOSENSE_AI_DATA_DIR=/app/data
      - REPOSENSE_AI_LOG_DIR=/app/logs
      - FLASK_ENV=production
      - FLASK_DEBUG=false
      - OLLAMA_BASE_URL=http://ollama:11434
      - PYTHONUNBUFFERED=1
    
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    depends_on:
      - ollama
EOF

# Add volumes to be appended
cat > reposense-ai-volumes.yml << 'EOF'
  reposense_ai_data:
  reposense_ai_logs:
EOF

echo "🔧 Integrating with your docker-compose.yml..."

# Find the line number where services section ends (before networks or volumes)
SERVICES_END=$(grep -n "^networks:" docker-compose.yml | head -1 | cut -d: -f1)
if [ -z "$SERVICES_END" ]; then
    SERVICES_END=$(grep -n "^volumes:" docker-compose.yml | head -1 | cut -d: -f1)
fi

if [ -z "$SERVICES_END" ]; then
    echo "❌ Could not find networks or volumes section in docker-compose.yml"
    echo "Please manually add the RepoSense AI service."
    exit 1
fi

# Insert RepoSense AI service before networks/volumes
SERVICES_END=$((SERVICES_END - 1))
head -n $SERVICES_END docker-compose.yml > docker-compose.yml.tmp
cat reposense-ai-service.yml >> docker-compose.yml.tmp
tail -n +$((SERVICES_END + 1)) docker-compose.yml >> docker-compose.yml.tmp

# Add volumes
VOLUMES_END=$(grep -n "^volumes:" docker-compose.yml.tmp | head -1 | cut -d: -f1)
if [ ! -z "$VOLUMES_END" ]; then
    # Find the end of volumes section
    VOLUMES_SECTION_END=$(tail -n +$((VOLUMES_END + 1)) docker-compose.yml.tmp | grep -n "^[a-zA-Z]" | head -1 | cut -d: -f1)
    if [ ! -z "$VOLUMES_SECTION_END" ]; then
        VOLUMES_SECTION_END=$((VOLUMES_END + VOLUMES_SECTION_END - 1))
        head -n $VOLUMES_SECTION_END docker-compose.yml.tmp > docker-compose.yml.new
        cat reposense-ai-volumes.yml >> docker-compose.yml.new
        tail -n +$((VOLUMES_SECTION_END + 1)) docker-compose.yml.tmp >> docker-compose.yml.new
    else
        # Volumes section is at the end
        cat docker-compose.yml.tmp > docker-compose.yml.new
        cat reposense-ai-volumes.yml >> docker-compose.yml.new
    fi
else
    # No volumes section, add it
    cat docker-compose.yml.tmp > docker-compose.yml.new
    echo "" >> docker-compose.yml.new
    echo "volumes:" >> docker-compose.yml.new
    cat reposense-ai-volumes.yml >> docker-compose.yml.new
fi

# Replace original file
mv docker-compose.yml.new docker-compose.yml

# Clean up temporary files
rm -f docker-compose.yml.tmp reposense-ai-service.yml reposense-ai-volumes.yml

echo "✅ RepoSense AI service added to docker-compose.yml"

# Create config directory
echo "📁 Creating configuration directory..."
mkdir -p reposense-ai-config
cp config.production.json reposense-ai-config/

echo ""
echo "🎉 Integration completed!"
echo ""
echo "📋 What was done:"
echo "   ✅ Backed up original docker-compose.yml"
echo "   ✅ Added reposense-ai service to docker-compose.yml"
echo "   ✅ Added required volumes"
echo "   ✅ Created reposense-ai-config/ directory"
echo ""
echo "🚀 Next steps:"
echo "   1. Customize reposense-ai-config/config.production.json"
echo "   2. Copy RepoSense AI source to ./reposense-ai/"
echo "   3. docker-compose build reposense-ai"
echo "   4. docker-compose up -d reposense-ai"
echo ""
echo "🌐 RepoSense AI will be available at: http://your-server:5000"
echo "📊 Health check: http://your-server:5000/health"
echo ""
echo "🔍 To view the changes:"
echo "   diff docker-compose.yml.backup.* docker-compose.yml"
