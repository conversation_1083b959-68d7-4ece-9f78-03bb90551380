#!/usr/bin/env python3
"""
Repository Monitor Simple Binary Builder
Simplified version for Windows compatibility
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path

def main():
    """Simple binary build process"""
    print("🚀 Repository Monitor Simple Binary Builder")
    print("=" * 50)
    
    # Get current directory
    project_root = Path.cwd()
    print(f"📁 Project root: {project_root}")
    
    # Check if we're in the right directory
    entry_point = project_root / "repository_monitor_binary.py"
    original_entry = project_root / "repository_monitor_app.py"

    if not entry_point.exists():
        if original_entry.exists():
            print("⚠️  Using original entry point (Docker version)")
            entry_point = original_entry
        else:
            print("❌ Error: No entry point found!")
            print("Make sure you're running this script from the project root directory.")
            print("\nCurrent directory contents:")
            for item in project_root.iterdir():
                if item.name.endswith('.py'):
                    print(f"   {item.name}")
            sys.exit(1)

    print(f"✅ Found entry point: {entry_point}")
    
    # Check PyInstaller
    pyinstaller_cmd = None

    # Try different ways to find pyinstaller (prioritize python -m PyInstaller for Windows Store Python)
    possible_commands = [
        f"{sys.executable} -m PyInstaller",  # This works for Windows Store Python
        "pyinstaller",
        sys.executable.replace("python.exe", "Scripts\\pyinstaller.exe"),
        sys.executable.replace("python.exe", "Scripts\\pyinstaller")
    ]

    for cmd in possible_commands:
        try:
            if cmd.startswith(sys.executable):
                # For python -m PyInstaller
                test_cmd = cmd.split() + ["--version"]
            else:
                test_cmd = [cmd, "--version"]

            result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                pyinstaller_cmd = cmd
                print(f"✅ Found PyInstaller: {cmd}")
                print(f"   Version: {result.stdout.strip()}")
                break
        except Exception:
            continue

    if not pyinstaller_cmd:
        print("❌ PyInstaller not found. Installing...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("✅ PyInstaller installed successfully")
            # Try to find it again
            for cmd in possible_commands:
                try:
                    if cmd.startswith(sys.executable):
                        test_cmd = cmd.split() + ["--version"]
                    else:
                        test_cmd = [cmd, "--version"]
                    result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        pyinstaller_cmd = cmd
                        print(f"✅ PyInstaller now available: {cmd}")
                        break
                except Exception:
                    continue
        except Exception as install_error:
            print(f"❌ Failed to install PyInstaller: {install_error}")
            sys.exit(1)

    if not pyinstaller_cmd:
        print("❌ Could not find or install PyInstaller")
        print("Please try installing manually:")
        print(f"   {sys.executable} -m pip install pyinstaller")
        sys.exit(1)
    
    # Clean previous builds
    dist_dir = project_root / "dist"
    build_dir = project_root / "build"
    
    print("🧹 Cleaning previous builds...")
    for dir_path in [dist_dir, build_dir]:
        if dir_path.exists():
            shutil.rmtree(dir_path)
            print(f"   Removed {dir_path}")
    
    # Determine platform-specific settings
    is_windows = platform.system() == "Windows"
    path_sep = ";" if is_windows else ":"
    binary_name = "repository-monitor.exe" if is_windows else "repository-monitor"
    
    # Build PyInstaller command
    if pyinstaller_cmd.startswith(sys.executable):
        # For python -m PyInstaller
        cmd = pyinstaller_cmd.split() + [
            "--onefile",
            "--name", "repository-monitor",
            "--console",  # Keep console for logs
        ]
    else:
        cmd = [
            pyinstaller_cmd,
            "--onefile",
            "--name", "repository-monitor",
            "--console",  # Keep console for logs
        ]
    
    # Add data files if they exist
    data_dirs = ["templates", "static", "docs", "marketing"]
    for dir_name in data_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            cmd.extend(["--add-data", f"{dir_name}{path_sep}{dir_name}"])
            print(f"✅ Including directory: {dir_name}")
        else:
            print(f"⚠️  Skipping missing directory: {dir_name}")
    
    # Add hidden imports
    hidden_imports = [
        "repository_backends.svn_backend",
        "repository_backends.git_backend", 
        "repository_backends.base",
        "flask",
        "sqlite3",
        "requests",
        "jinja2",
        "werkzeug",
        "markupsafe"
    ]
    
    for import_name in hidden_imports:
        cmd.extend(["--hidden-import", import_name])
    
    # Add collect-all for Flask components
    collect_all = ["flask", "jinja2", "werkzeug", "markupsafe"]
    for module in collect_all:
        cmd.extend(["--collect-all", module])

    # Exclude GUI frameworks we don't need (fixes Qt conflicts)
    exclude_modules = [
        "PyQt5", "PyQt6", "PySide2", "PySide6",  # Qt frameworks
        "tkinter", "turtle",  # Tkinter GUI
        "matplotlib", "numpy", "scipy",  # Heavy scientific packages
        "pandas", "jupyter", "notebook",  # Jupyter/data science
        "IPython", "zmq", "tornado",  # IPython dependencies
        "black", "blib2to3",  # Code formatting tools
        "nbformat", "jsonschema"  # Notebook formats
    ]

    for module in exclude_modules:
        cmd.extend(["--exclude-module", module])

    # Add entry point
    cmd.append(str(entry_point))
    
    print("\n🔨 Building binary...")
    print("Command:", " ".join(cmd))
    print()
    
    # Run PyInstaller
    try:
        result = subprocess.run(cmd, cwd=project_root, check=True)
        print("\n✅ Binary built successfully!")
        
        # Check if binary was created
        binary_path = dist_dir / binary_name
        if binary_path.exists():
            print(f"📦 Binary location: {binary_path}")
            print(f"📏 Binary size: {binary_path.stat().st_size / 1024 / 1024:.1f} MB")
        else:
            print(f"⚠️  Binary not found at expected location: {binary_path}")
            
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Build failed with return code {e.returncode}")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
    
    # Create simple startup script
    print("\n📝 Creating startup script...")
    
    if is_windows:
        startup_script = dist_dir / "start-repository-monitor.bat"
        with open(startup_script, 'w') as f:
            f.write("""@echo off
echo Starting Repository Monitor...
echo Access the web interface at: http://localhost:5000
echo Press Ctrl+C to stop

repository-monitor.exe
pause
""")
    else:
        startup_script = dist_dir / "start-repository-monitor.sh"
        with open(startup_script, 'w') as f:
            f.write("""#!/bin/bash
echo "Starting Repository Monitor..."
echo "Access the web interface at: http://localhost:5000"
echo "Press Ctrl+C to stop"

./repository-monitor
""")
        startup_script.chmod(0o755)
    
    print(f"✅ Startup script created: {startup_script}")
    
    # Copy configuration example
    config_example = project_root / "config.example.json"
    if config_example.exists():
        shutil.copy2(config_example, dist_dir / "config.example.json")
        print("✅ Configuration example copied")
    
    print("\n🎉 Build completed successfully!")
    print(f"📦 Binary package location: {dist_dir}")
    print("\nTo run the application:")
    if is_windows:
        print(f"   cd {dist_dir}")
        print("   start-repository-monitor.bat")
    else:
        print(f"   cd {dist_dir}")
        print("   ./start-repository-monitor.sh")
    
    print("\nFirst run will create a config.json file that you can customize.")

if __name__ == "__main__":
    main()
