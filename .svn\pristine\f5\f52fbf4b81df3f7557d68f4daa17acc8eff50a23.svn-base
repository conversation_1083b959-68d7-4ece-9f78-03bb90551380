#!/usr/bin/env python3
"""
Web interface for the SVN Monitor application
Provides a Flask-based web UI for configuration and monitoring
"""

import logging
import re
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from markupsafe import Markup

from config_manager import ConfigManager
from monitor_service import MonitorService
from models import Config, RepositoryConfig, User, UserRole, HistoricalScanConfig, HistoricalScanStatus
from user_management_service import UserManagementService
from repository_backends import get_backend_manager
from document_service import DocumentService, Document
from historical_scanner import HistoricalScanner


class WebInterface:
    """Web interface for configuration and monitoring"""
    
    def __init__(self, monitor_service: MonitorService):
        self.monitor_service = monitor_service
        self.config_manager = monitor_service.config_manager
        self.file_manager = monitor_service.file_manager

        # Initialize new services
        self.user_service = UserManagementService(monitor_service.config)
        self.backend_manager = get_backend_manager()
        self.document_service = DocumentService(
            monitor_service.config.output_dir,
            ollama_client=monitor_service.ollama_client
        )

        # Initialize historical scanner (create a simple document processor for it)
        from document_processor import DocumentProcessor
        document_processor = DocumentProcessor(monitor_service.config.output_dir)

        self.historical_scanner = HistoricalScanner(
            self.backend_manager,
            document_processor,
            monitor_service.ollama_client,
            db_path="/app/data/documents.db",
            config_manager=monitor_service.config_manager
        )
        self.historical_scanner.start()

        self.app = Flask(__name__)
        self.app.secret_key = monitor_service.config.web_secret_key
        self.logger = logging.getLogger(__name__)

        # Register custom Jinja2 filters
        self.app.jinja_env.filters['markdown'] = self.markdown_to_html

        self.setup_routes()

    def markdown_to_html(self, text):
        """Convert markdown text to HTML using simple regex replacements"""
        if not text:
            return ""

        # Escape HTML characters first
        html = text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

        # Convert markdown syntax to HTML
        # Headers
        html = re.sub(r'^# (.+)$', r'<h1>\1</h1>', html, flags=re.MULTILINE)
        html = re.sub(r'^## (.+)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
        html = re.sub(r'^### (.+)$', r'<h3>\1</h3>', html, flags=re.MULTILINE)
        html = re.sub(r'^#### (.+)$', r'<h4>\1</h4>', html, flags=re.MULTILINE)

        # Bold and italic
        html = re.sub(r'\*\*(.+?)\*\*', r'<strong>\1</strong>', html)
        html = re.sub(r'\*(.+?)\*', r'<em>\1</em>', html)

        # Code blocks
        html = re.sub(r'```(\w+)?\n(.*?)\n```', r'<pre><code class="language-\1">\2</code></pre>', html, flags=re.DOTALL)
        html = re.sub(r'`(.+?)`', r'<code>\1</code>', html)

        # Lists
        html = re.sub(r'^- (.+)$', r'<li>\1</li>', html, flags=re.MULTILINE)
        html = re.sub(r'(<li>.*</li>)', r'<ul>\1</ul>', html, flags=re.DOTALL)
        html = re.sub(r'</ul>\s*<ul>', '', html)  # Merge consecutive lists

        # Line breaks
        html = re.sub(r'\n\n', '</p><p>', html)
        html = re.sub(r'\n', '<br>', html)

        # Wrap in paragraphs
        if html and not html.startswith('<'):
            html = f'<p>{html}</p>'

        return Markup(html)

    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def index():
            import time
            start_time = time.time()
            status = self.monitor_service.get_status()
            load_time = time.time() - start_time
            self.logger.debug(f"Dashboard loaded in {load_time:.3f}s")
            return render_template('index.html', status=status, config=self.monitor_service.config)
        
        @self.app.route('/config')
        def config_page():
            # Get available models for fallback
            available_models = self.monitor_service.get_available_models()
            return render_template('config.html',
                                 config=self.monitor_service.config,
                                 available_models=available_models)
        
        @self.app.route('/config/save', methods=['POST'])
        def save_config():
            try:
                # Update config from form data
                new_config = self.config_manager.update_config_from_form(
                    self.monitor_service.config, 
                    request.form
                )
                
                # Validate config
                is_valid, errors = self.config_manager.validate_config(new_config)
                if not is_valid:
                    for error in errors:
                        flash(error, 'error')
                    return redirect(url_for('config_page'))
                
                # Update monitor service with new config
                self.monitor_service.update_config(new_config)
                
                flash('Configuration saved successfully!', 'success')
                
            except Exception as e:
                flash(f'Error saving configuration: {str(e)}', 'error')
            
            return redirect(url_for('config_page'))
        
        @self.app.route('/api/status')
        def api_status():
            import time
            start_time = time.time()
            status = self.monitor_service.get_status()
            load_time = time.time() - start_time
            status['load_time_ms'] = round(load_time * 1000, 2)
            return jsonify(status)

        @self.app.route('/api/performance')
        def api_performance():
            """Get performance metrics"""
            import time
            return jsonify({
                'ollama_cache_age_seconds': time.time() - (self.monitor_service.ollama_cache_time or 0) if self.monitor_service.ollama_cache_time else None,
                'ollama_cache_duration_seconds': self.monitor_service.ollama_cache_duration,
                'ollama_cached_status': self.monitor_service.ollama_connected_cache
            })

        @self.app.route('/api/ollama/models')
        def api_ollama_models():
            """Get available Ollama models"""
            models = self.monitor_service.get_available_models()
            return jsonify({
                'models': models,
                'count': len(models),
                'connected': self.monitor_service.get_ollama_connection_status()
            })
        
        @self.app.route('/api/start', methods=['POST'])
        def api_start():
            enabled_repos = self.monitor_service.config.get_enabled_repositories()
            if not enabled_repos:
                return jsonify({'error': 'No repositories configured or enabled'}), 400

            self.monitor_service.start_monitoring()
            return jsonify({'status': 'started', 'repositories': len(enabled_repos)})
        
        @self.app.route('/api/stop', methods=['POST'])
        def api_stop():
            self.monitor_service.stop_monitoring()
            return jsonify({'status': 'stopped'})
        
        @self.app.route('/api/check', methods=['POST'])
        def api_check():
            enabled_repos = self.monitor_service.config.get_enabled_repositories()
            if not enabled_repos:
                return jsonify({'error': 'No repositories configured or enabled'}), 400

            try:
                self.monitor_service.run_once()
                return jsonify({'status': 'check completed', 'repositories_checked': len(enabled_repos)})
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/logs')
        def logs_page():
            try:
                log_entries_count = self.monitor_service.config.web_log_entries
                logs = self.file_manager.read_recent_logs(log_entries_count)
                return render_template('logs.html', logs=logs, log_entries_count=log_entries_count)
            except Exception as e:
                return render_template('logs.html', logs=[f'Error reading logs: {str(e)}'], log_entries_count=100)
        
        @self.app.route('/api/test_ollama')
        def api_test_ollama():
            connected = self.monitor_service.ollama_client.test_connection()
            return jsonify({'connected': connected})

        # Repository management routes
        @self.app.route('/repositories')
        def repositories_page():
            return render_template('repositories.html',
                                 repositories=self.monitor_service.config.repositories,
                                 config=self.monitor_service.config)

        @self.app.route('/repositories/add', methods=['POST'])
        def add_repository():
            try:
                repo = self.config_manager.add_repository_from_form(
                    self.monitor_service.config,
                    request.form
                )
                self.monitor_service.save_config()
                self.monitor_service.file_manager.setup_directories()  # Create new repo directories
                flash(f'Repository "{repo.name}" added successfully!', 'success')
            except Exception as e:
                flash(f'Error adding repository: {str(e)}', 'error')

            return redirect(url_for('repositories_page'))

        @self.app.route('/repositories/<repo_id>/edit', methods=['POST'])
        def edit_repository(repo_id):
            try:
                success = self.config_manager.update_repository_from_form(
                    self.monitor_service.config,
                    repo_id,
                    request.form
                )
                if success:
                    self.monitor_service.save_config()
                    flash('Repository updated successfully!', 'success')
                else:
                    flash('Repository not found!', 'error')
            except Exception as e:
                flash(f'Error updating repository: {str(e)}', 'error')

            return redirect(url_for('repositories_page'))

        @self.app.route('/repositories/<repo_id>/delete', methods=['POST'])
        def delete_repository(repo_id):
            try:
                repo = self.monitor_service.config.get_repository_by_id(repo_id)
                if repo:
                    repo_name = repo.name
                    if self.monitor_service.config.remove_repository(repo_id):
                        self.monitor_service.save_config()
                        flash(f'Repository "{repo_name}" deleted successfully!', 'success')
                    else:
                        flash('Failed to delete repository!', 'error')
                else:
                    flash('Repository not found!', 'error')
            except Exception as e:
                flash(f'Error deleting repository: {str(e)}', 'error')

            return redirect(url_for('repositories_page'))

        @self.app.route('/api/repositories')
        def api_repositories():
            return jsonify({
                'repositories': [
                    {
                        'id': repo.id,
                        'name': repo.name,
                        'url': repo.url,
                        'enabled': repo.enabled,
                        'last_revision': repo.last_revision
                    }
                    for repo in self.monitor_service.config.repositories
                ]
            })

        @self.app.route('/api/repositories/<repo_id>/revisions')
        def api_repository_revisions(repo_id):
            """Get available revisions for a repository"""
            try:
                # Find repository
                repo = None
                for r in self.monitor_service.config.repositories:
                    if r.id == repo_id:
                        repo = r
                        break

                if not repo:
                    return jsonify({'error': 'Repository not found'}), 404

                # Get backend
                backend = self.backend_manager.get_backend_for_repository(repo, None)
                if not backend:
                    return jsonify({'error': 'No backend available for repository'}), 500

                # Get latest revision
                latest_revision = backend.get_latest_revision(repo)
                if not latest_revision:
                    return jsonify({'error': 'Could not determine latest revision'}), 500

                try:
                    latest_num = int(latest_revision)
                    # Generate list of all available revisions
                    revisions = []
                    for rev_num in range(1, latest_num + 1):
                        revisions.append({
                            'number': rev_num,
                            'display': f"r{rev_num}"
                        })

                    return jsonify({
                        'revisions': revisions,
                        'latest_revision': latest_num,
                        'total_count': latest_num
                    })

                except ValueError:
                    # Non-numeric revision system (like Git)
                    return jsonify({
                        'revisions': [{'number': latest_revision, 'display': latest_revision}],
                        'latest_revision': latest_revision,
                        'total_count': 1
                    })

            except Exception as e:
                self.logger.error(f"Error getting revisions for repository {repo_id}: {e}")
                return jsonify({'error': f'Error getting revisions: {str(e)}'}), 500

        # User Management Routes
        @self.app.route('/users')
        def users_page():
            users = self.monitor_service.config.users
            repositories = self.monitor_service.config.repositories
            user_roles = [role.value for role in UserRole]
            return render_template('users.html', users=users, repositories=repositories, user_roles=user_roles)

        @self.app.route('/users/add', methods=['POST'])
        def add_user():
            try:
                success, message, user = self.user_service.create_user(
                    username=request.form.get('username', '').strip(),
                    email=request.form.get('email', '').strip(),
                    full_name=request.form.get('full_name', '').strip(),
                    role=UserRole(request.form.get('role', 'developer')),
                    phone=request.form.get('phone', '').strip() or None,
                    department=request.form.get('department', '').strip() or None,
                    receive_all_notifications=request.form.get('receive_all_notifications') == 'on'
                )

                if success:
                    self.config_manager.save_config(self.monitor_service.config)
                    flash(message, 'success')
                else:
                    flash(message, 'error')

            except Exception as e:
                flash(f"Error adding user: {str(e)}", 'error')

            return redirect(url_for('users_page'))

        @self.app.route('/users/update/<user_id>', methods=['POST'])
        def update_user(user_id):
            try:
                update_data = {
                    'username': request.form.get('username', '').strip(),
                    'email': request.form.get('email', '').strip(),
                    'full_name': request.form.get('full_name', '').strip(),
                    'role': request.form.get('role', 'developer'),
                    'phone': request.form.get('phone', '').strip() or None,
                    'department': request.form.get('department', '').strip() or None,
                    'enabled': request.form.get('enabled') == 'on',
                    'receive_all_notifications': request.form.get('receive_all_notifications') == 'on'
                }

                success, message = self.user_service.update_user(user_id, **update_data)

                if success:
                    self.config_manager.save_config(self.monitor_service.config)
                    flash(message, 'success')
                else:
                    flash(message, 'error')

            except Exception as e:
                flash(f"Error updating user: {str(e)}", 'error')

            return redirect(url_for('users_page'))

        @self.app.route('/users/delete/<user_id>', methods=['POST'])
        def delete_user(user_id):
            try:
                success, message = self.user_service.delete_user(user_id)

                if success:
                    self.config_manager.save_config(self.monitor_service.config)
                    flash(message, 'success')
                else:
                    flash(message, 'error')

            except Exception as e:
                flash(f"Error deleting user: {str(e)}", 'error')

            return redirect(url_for('users_page'))

        # Repository Discovery Routes
        @self.app.route('/repositories/discover')
        def repository_discovery_page():
            return render_template('repository_discovery.html',
                                 config=self.monitor_service.config)

        @self.app.route('/repositories/discover/scan', methods=['POST'])
        def scan_repositories():
            try:
                base_url = request.form.get('base_url', '').strip()
                username = request.form.get('username', '').strip() or None
                password = request.form.get('password', '').strip() or None
                max_depth = int(request.form.get('max_depth', 3))
                backend_type = request.form.get('backend_type', 'svn').strip()

                if not base_url:
                    return jsonify({'success': False, 'message': 'Base URL is required'})

                discovered_repos = self.backend_manager.discover_repositories(
                    backend_type, base_url, self.monitor_service.config, username, password, max_depth
                )

                # Convert RepositoryInfo objects to dictionaries for JSON response
                repo_dicts = []
                for repo_info in discovered_repos:
                    repo_dicts.append({
                        'name': repo_info.name,
                        'url': repo_info.url,
                        'path': repo_info.path,
                        'last_revision': repo_info.last_revision,
                        'last_author': repo_info.last_author,
                        'last_date': repo_info.last_date,
                        'size': repo_info.size,
                        'repository_type': repo_info.repository_type
                    })

                return jsonify({
                    'success': True,
                    'repositories': repo_dicts
                })

            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/repositories/import', methods=['POST'])
        def import_repository():
            try:
                repo_data = request.json
                username = repo_data.get('username')
                password = repo_data.get('password')

                # Create repository config from discovered data
                repo_config = RepositoryConfig(
                    name=repo_data['name'],
                    url=repo_data['url'],
                    username=username,
                    password=password,
                    last_revision=int(repo_data.get('last_revision', 0)) if repo_data.get('last_revision') else 0,
                    enabled=True
                )

                # Add to configuration
                self.monitor_service.config.repositories.append(repo_config)
                self.config_manager.save_config(self.monitor_service.config)

                return jsonify({
                    'success': True,
                    'message': f"Repository '{repo_config.name}' imported successfully",
                    'repository_id': repo_config.id
                })

            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        # Document Management Routes
        @self.app.route('/documents')
        def documents_page():
            """Display all generated documents with pagination and filtering"""
            # Get query parameters
            page = request.args.get('page', 1, type=int)
            per_page = request.args.get('per_page', 25, type=int)
            repository_id = request.args.get('repository', None)
            code_review_filter = request.args.get('code_review', None)
            doc_impact_filter = request.args.get('doc_impact', None)

            # Convert string filters to boolean
            if code_review_filter == 'true':
                code_review_filter = True
            elif code_review_filter == 'false':
                code_review_filter = False
            else:
                code_review_filter = None

            if doc_impact_filter == 'true':
                doc_impact_filter = True
            elif doc_impact_filter == 'false':
                doc_impact_filter = False
            else:
                doc_impact_filter = None

            # Calculate offset
            offset = (page - 1) * per_page

            # Get documents with pagination
            documents = self.document_service.get_documents(
                limit=per_page,
                offset=offset,
                repository_id=repository_id,
                code_review_filter=code_review_filter,
                doc_impact_filter=doc_impact_filter
            )

            # Convert to legacy Document objects for template compatibility
            legacy_documents = [Document.from_record(doc) for doc in documents]

            # Get total count for pagination
            total_count = self.document_service.get_document_count(repository_id)
            total_pages = (total_count + per_page - 1) // per_page

            # Get stats and processing info
            stats = self.document_service.get_repository_stats()
            processing_stats = self.document_service.get_processing_stats()

            return render_template('documents.html',
                                 documents=legacy_documents,
                                 stats=stats,
                                 processing_stats=processing_stats,
                                 page=page,
                                 per_page=per_page,
                                 total_pages=total_pages,
                                 total_count=total_count,
                                 repository_filter=repository_id,
                                 code_review_filter=code_review_filter,
                                 doc_impact_filter=doc_impact_filter)

        @self.app.route('/documents/<doc_id>')
        def view_document(doc_id):
            """View a specific document"""
            from diff_service import DiffService

            document = self.document_service.get_document_by_id(doc_id)
            if not document:
                flash('Document not found', 'error')
                return redirect(url_for('documents_page'))

            content = self.document_service.get_document_content(doc_id)
            if not content:
                flash('Error reading document content', 'error')
                return redirect(url_for('documents_page'))

            # Check if diff should be included
            include_diff = request.args.get('include_diff', 'false').lower() == 'true'
            diff_format = request.args.get('diff_format', 'unified')
            diff_content = None

            if include_diff:
                # Get document record for diff generation
                document_record = self.document_service.get_document_record_by_id(doc_id)
                if document_record:
                    diff_service = DiffService(self.monitor_service.config_manager)
                    if diff_service.can_generate_diff(document_record):
                        diff_content = diff_service.get_diff_for_document(document_record, diff_format)
                        # Add diff to document content if available
                        if diff_content:
                            document.diff = diff_content

            return render_template('document_view.html',
                                 document=document,
                                 content=content,
                                 include_diff=include_diff,
                                 diff_content=diff_content,
                                 diff_format=diff_format)

        @self.app.route('/api/documents')
        def api_documents():
            """API endpoint for documents list with pagination"""
            # Get query parameters
            limit = request.args.get('limit', 50, type=int)
            offset = request.args.get('offset', 0, type=int)
            repository_id = request.args.get('repository', None)
            code_review_filter = request.args.get('code_review', None)
            doc_impact_filter = request.args.get('doc_impact', None)

            # Convert string filters to boolean
            if code_review_filter == 'true':
                code_review_filter = True
            elif code_review_filter == 'false':
                code_review_filter = False
            else:
                code_review_filter = None

            if doc_impact_filter == 'true':
                doc_impact_filter = True
            elif doc_impact_filter == 'false':
                doc_impact_filter = False
            else:
                doc_impact_filter = None

            # Get documents
            documents = self.document_service.get_documents(
                limit=limit,
                offset=offset,
                repository_id=repository_id,
                code_review_filter=code_review_filter,
                doc_impact_filter=doc_impact_filter
            )

            # Get total count
            total_count = self.document_service.get_document_count(repository_id)

            return jsonify({
                'documents': [
                    {
                        'id': doc.id,
                        'repository_id': doc.repository_id,
                        'repository_name': doc.repository_name,
                        'revision': doc.revision,
                        'date': doc.date.isoformat(),
                        'author': doc.author,
                        'commit_message': doc.commit_message,
                        'filename': doc.filename,
                        'size': doc.size,
                        'code_review_recommended': doc.code_review_recommended,
                        'code_review_priority': doc.code_review_priority,
                        'documentation_impact': doc.documentation_impact,
                        'risk_level': doc.risk_level
                    }
                    for doc in documents
                ],
                'total_count': total_count,
                'stats': self.document_service.get_repository_stats(),
                'processing_stats': self.document_service.get_processing_stats()
            })

        @self.app.route('/api/documents/<doc_id>/delete', methods=['POST'])
        def api_delete_document(doc_id):
            """Delete a document, including orphaned documents"""
            try:
                self.logger.info(f"Attempting to delete document: {doc_id}")

                # First check if document exists
                document = self.document_service.get_document_record_by_id(doc_id)
                self.logger.info(f"Document lookup result: {document}")

                if not document:
                    self.logger.warning(f"Document not found: {doc_id}")
                    return jsonify({'success': False, 'message': 'Document not found'}), 404

                # Check if document is orphaned (repository no longer exists)
                config = self.config_manager.load_config()
                valid_repo_ids = set(repo.id for repo in config.repositories)
                is_orphaned = document.repository_id not in valid_repo_ids

                if is_orphaned:
                    # For orphaned documents, just remove from database
                    success = self.document_service.delete_document(doc_id)
                    if success:
                        self.logger.info(f"Deleted orphaned document: {doc_id} (repo: {document.repository_id})")
                        return jsonify({'success': True, 'message': 'Orphaned document removed from database'})
                    else:
                        return jsonify({'success': False, 'message': 'Failed to delete orphaned document'}), 500
                else:
                    # For valid documents, use normal deletion process
                    success = self.document_service.delete_document(doc_id)
                    if success:
                        return jsonify({'success': True, 'message': 'Document deleted successfully'})
                    else:
                        return jsonify({'success': False, 'message': 'Failed to delete document'}), 500

            except Exception as e:
                self.logger.error(f"Error deleting document {doc_id}: {e}")
                return jsonify({'success': False, 'message': f'Error deleting document: {str(e)}'}), 500

        @self.app.route('/api/documents/cleanup-orphaned', methods=['POST'])
        def api_cleanup_orphaned_documents():
            """Remove all orphaned documents from database"""
            try:
                # Get valid repository IDs
                config = self.config_manager.load_config()
                valid_repo_ids = set(repo.id for repo in config.repositories)

                # Get all documents
                all_documents = self.document_service.get_documents()

                orphaned_count = 0
                for doc in all_documents:
                    if doc.repository_id not in valid_repo_ids:
                        success = self.document_service.delete_document(doc.id)
                        if success:
                            orphaned_count += 1
                            self.logger.info(f"Cleaned up orphaned document: {doc.id} (repo: {doc.repository_id})")
                        else:
                            self.logger.warning(f"Failed to delete orphaned document: {doc.id}")

                return jsonify({
                    'success': True,
                    'message': f'Cleaned up {orphaned_count} orphaned documents',
                    'orphaned_count': orphaned_count
                })
            except Exception as e:
                self.logger.error(f"Error cleaning up orphaned documents: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500

        @self.app.route('/api/documents/rescan', methods=['POST'])
        def api_rescan_documents():
            """Force rescan of all documents"""
            try:
                self.document_service.force_rescan()
                return jsonify({'success': True, 'message': 'Document rescan initiated'})
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/documents/clear-cache', methods=['POST'])
        def api_clear_document_cache():
            """Clear document cache"""
            try:
                self.document_service._invalidate_cache()
                return jsonify({'success': True, 'message': 'Document cache cleared'})
            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/documents/delete-all', methods=['POST'])
        def api_delete_all_documents():
            """Delete ALL documents (database records and physical files)"""
            try:
                import shutil
                import os

                # Count existing files first
                repositories_dir = "/app/data/output/repositories"
                file_count = 0
                if os.path.exists(repositories_dir):
                    for root, dirs, files in os.walk(repositories_dir):
                        file_count += len([f for f in files if f.endswith('.md')])

                # Delete the entire repositories directory
                if os.path.exists(repositories_dir):
                    shutil.rmtree(repositories_dir)
                    self.logger.info(f"Deleted repositories directory: {repositories_dir}")

                # Clear all documents from database
                db_count = self.document_service.clear_all_documents()

                # Clear document cache
                self.document_service._invalidate_cache()

                return jsonify({
                    'success': True,
                    'message': f'Deleted all documents: {file_count} files and {db_count} database records',
                    'deleted_files': file_count,
                    'deleted_db_records': db_count
                })
            except Exception as e:
                self.logger.error(f"Error deleting all documents: {e}")
                return jsonify({'success': False, 'message': str(e)}), 500

        # Historical Scanning Routes
        @self.app.route('/repositories/<repo_id>/historical-scan')
        def historical_scan_page(repo_id):
            """Display historical scanning configuration for a repository"""
            repo = None
            for r in self.monitor_service.config.repositories:
                if r.id == repo_id:
                    repo = r
                    break

            if not repo:
                flash('Repository not found', 'error')
                return redirect(url_for('repositories_page'))

            # Get scan progress if any
            scan_progress = self.historical_scanner.get_scan_progress(repo_id)

            return render_template('historical_scan.html',
                                 repository=repo,
                                 scan_progress=scan_progress,
                                 scan_statuses=HistoricalScanStatus)

        @self.app.route('/repositories/<repo_id>/historical-scan/configure', methods=['POST'])
        def configure_historical_scan(repo_id):
            """Configure historical scanning for a repository"""
            try:
                repo = None
                for r in self.monitor_service.config.repositories:
                    if r.id == repo_id:
                        repo = r
                        break

                if not repo:
                    return jsonify({'success': False, 'message': 'Repository not found'})

                # Parse form data
                scan_config = HistoricalScanConfig()
                scan_config.enabled = request.form.get('enabled') == 'on'
                scan_config.scan_by_revision = request.form.get('scan_by_revision') == 'on'
                scan_config.scan_by_date = request.form.get('scan_by_date') == 'on'

                # Revision range
                if scan_config.scan_by_revision:
                    start_rev = request.form.get('start_revision', '').strip()
                    end_rev = request.form.get('end_revision', '').strip()
                    scan_config.start_revision = int(start_rev) if start_rev else None
                    scan_config.end_revision = int(end_rev) if end_rev else None

                # Date range
                if scan_config.scan_by_date:
                    from datetime import datetime
                    start_date = request.form.get('start_date', '').strip()
                    end_date = request.form.get('end_date', '').strip()
                    if start_date:
                        scan_config.start_date = datetime.fromisoformat(start_date)
                    if end_date:
                        scan_config.end_date = datetime.fromisoformat(end_date)

                # Other settings
                scan_config.batch_size = int(request.form.get('batch_size', 10))
                scan_config.include_merge_commits = request.form.get('include_merge_commits') == 'on'
                scan_config.skip_large_commits = request.form.get('skip_large_commits') == 'on'
                scan_config.max_files_per_commit = int(request.form.get('max_files_per_commit', 100))

                # Analysis preferences
                scan_config.generate_documentation = request.form.get('generate_documentation') == 'on'
                scan_config.analyze_code_review = request.form.get('analyze_code_review') == 'on'
                scan_config.analyze_documentation_impact = request.form.get('analyze_documentation_impact') == 'on'

                # Update repository configuration
                repo.historical_scan = scan_config
                self.monitor_service.save_config()

                return jsonify({'success': True, 'message': 'Historical scan configuration saved'})

            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/repositories/<repo_id>/historical-scan/start', methods=['POST'])
        def start_historical_scan(repo_id):
            """Start historical scanning for a repository"""
            try:
                repo = None
                for r in self.monitor_service.config.repositories:
                    if r.id == repo_id:
                        repo = r
                        break

                if not repo:
                    return jsonify({'success': False, 'message': 'Repository not found'})

                # Always update configuration with form data if provided (auto-save on start)
                if request.form:
                    try:
                        # Parse form data and save configuration first
                        if not repo.historical_scan:
                            scan_config = HistoricalScanConfig()
                        else:
                            scan_config = repo.historical_scan

                        scan_config.enabled = request.form.get('enabled') == 'on'
                        scan_config.scan_by_revision = request.form.get('scan_by_revision') == 'on'
                        scan_config.scan_by_date = request.form.get('scan_by_date') == 'on'

                        # Revision range
                        if scan_config.scan_by_revision:
                            start_rev = request.form.get('start_revision', '').strip()
                            end_rev = request.form.get('end_revision', '').strip()
                            scan_config.start_revision = int(start_rev) if start_rev else None
                            scan_config.end_revision = int(end_rev) if end_rev else None

                            # Date range
                            if scan_config.scan_by_date:
                                from datetime import datetime
                                start_date = request.form.get('start_date', '').strip()
                                end_date = request.form.get('end_date', '').strip()
                                if start_date:
                                    scan_config.start_date = datetime.fromisoformat(start_date)
                                if end_date:
                                    scan_config.end_date = datetime.fromisoformat(end_date)

                            # Other settings
                            scan_config.batch_size = int(request.form.get('batch_size', 10))
                            scan_config.include_merge_commits = request.form.get('include_merge_commits') == 'on'
                            scan_config.skip_large_commits = request.form.get('skip_large_commits') == 'on'
                            scan_config.max_files_per_commit = int(request.form.get('max_files_per_commit', 100))

                            # Analysis preferences
                            scan_config.generate_documentation = request.form.get('generate_documentation') == 'on'
                            scan_config.analyze_code_review = request.form.get('analyze_code_review') == 'on'
                            scan_config.analyze_documentation_impact = request.form.get('analyze_documentation_impact') == 'on'

                            # Update repository configuration
                            repo.historical_scan = scan_config
                            self.monitor_service.save_config()

                            # Verify configuration is now enabled
                            if not scan_config.enabled:
                                return jsonify({'success': False, 'message': 'Historical scanning must be enabled to start scan'})

                    except Exception as e:
                        return jsonify({'success': False, 'message': f'Error auto-configuring scan: {str(e)}'})

                # Check if historical scanning is configured and enabled
                if not repo.historical_scan or not repo.historical_scan.enabled:
                    return jsonify({'success': False, 'message': 'Historical scanning not configured. Please enable and configure scanning first.'})

                # Queue the scan
                success = self.historical_scanner.queue_scan(repo, repo.historical_scan)

                if success:
                    return jsonify({'success': True, 'message': 'Historical scan started'})
                else:
                    return jsonify({'success': False, 'message': 'Failed to start scan'})

            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/repositories/<repo_id>/historical-scan/cancel', methods=['POST'])
        def cancel_historical_scan(repo_id):
            """Cancel historical scanning for a repository"""
            try:
                success = self.historical_scanner.cancel_scan(repo_id)

                if success:
                    return jsonify({'success': True, 'message': 'Historical scan cancelled'})
                else:
                    return jsonify({'success': False, 'message': 'No active scan to cancel'})

            except Exception as e:
                return jsonify({'success': False, 'message': str(e)})

        @self.app.route('/api/historical-scan/progress')
        def api_historical_scan_progress():
            """Get progress for all historical scans"""
            try:
                all_progress = self.historical_scanner.get_all_scan_progress()
                statistics = self.historical_scanner.get_statistics()

                # Convert progress objects to dictionaries
                progress_data = {}
                for repo_id, progress in all_progress.items():
                    progress_data[repo_id] = {
                        'repository_id': progress.repository_id,
                        'total_revisions': progress.total_revisions,
                        'processed_revisions': progress.processed_revisions,
                        'failed_revisions': progress.failed_revisions,
                        'current_revision': progress.current_revision,
                        'started_at': progress.started_at.isoformat() if progress.started_at else None,
                        'estimated_completion': progress.estimated_completion.isoformat() if progress.estimated_completion else None,
                        'error_message': progress.error_message,
                        'status': progress.status.value
                    }

                return jsonify({
                    'progress': progress_data,
                    'statistics': statistics
                })

            except Exception as e:
                return jsonify({'error': str(e)}), 500, 500

        @self.app.route('/api/documents/processing-stats')
        def api_processing_stats():
            """Get document processing statistics"""
            return jsonify(self.document_service.get_processing_stats())

        @self.app.route('/api/documents/cache-stats')
        def api_cache_stats():
            """Get cache performance statistics"""
            return jsonify(self.document_service.get_cache_stats())

        @self.app.route('/api/documents/migration-status')
        def api_migration_status():
            """Get database migration status"""
            return jsonify(self.document_service.get_migration_status())

        @self.app.route('/api/documents/<doc_id>/diff')
        def api_get_document_diff(doc_id):
            """Get document diff content generated on-demand"""
            from diff_service import DiffService

            document = self.document_service.get_document_record_by_id(doc_id)
            if not document:
                return jsonify({'error': 'Document not found'}), 404

            # Get format parameter (unified or side-by-side)
            format_type = request.args.get('format', 'unified')
            if format_type not in ['unified', 'side-by-side']:
                return jsonify({'error': 'Invalid format. Use "unified" or "side-by-side"'}), 400

            # Generate diff on-demand using DiffService
            diff_service = DiffService(self.monitor_service.config_manager)

            if not diff_service.can_generate_diff(document):
                return jsonify({'error': 'Cannot generate diff for this document - missing repository metadata'}), 404

            diff_content = diff_service.get_diff_for_document(document, format_type)
            if not diff_content:
                return jsonify({'error': 'Failed to generate diff content'}), 500

            return jsonify({'diff': diff_content, 'format': format_type})

        @self.app.route('/api/documents/<doc_id>/feedback/code-review', methods=['POST'])
        def api_update_code_review_feedback(doc_id):
            """Update code review feedback for a document"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'No data provided'}), 400

                status = data.get('status')
                comments = data.get('comments')
                reviewer = data.get('reviewer')

                if not status:
                    return jsonify({'error': 'Status is required'}), 400

                if status not in ['approved', 'rejected', 'needs_changes', 'in_progress']:
                    return jsonify({'error': 'Invalid status. Use: approved, rejected, needs_changes, in_progress'}), 400

                success = self.document_service.update_code_review_feedback(doc_id, status, comments, reviewer)
                if success:
                    return jsonify({'success': True, 'message': 'Code review feedback updated'})
                else:
                    return jsonify({'error': 'Failed to update code review feedback'}), 500

            except Exception as e:
                self.logger.error(f"Error updating code review feedback: {e}")
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/api/documents/<doc_id>/feedback/documentation', methods=['POST'])
        def api_update_documentation_feedback(doc_id):
            """Update documentation quality feedback for a document"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'No data provided'}), 400

                rating = data.get('rating')
                comments = data.get('comments')
                updated_by = data.get('updated_by')

                if rating is not None:
                    try:
                        rating = int(rating)
                        if rating < 1 or rating > 5:
                            return jsonify({'error': 'Rating must be between 1 and 5'}), 400
                    except (ValueError, TypeError):
                        return jsonify({'error': 'Rating must be a number between 1 and 5'}), 400

                success = self.document_service.update_documentation_feedback(doc_id, rating, comments, updated_by)
                if success:
                    return jsonify({'success': True, 'message': 'Documentation feedback updated'})
                else:
                    return jsonify({'error': 'Failed to update documentation feedback'}), 500

            except Exception as e:
                self.logger.error(f"Error updating documentation feedback: {e}")
                return jsonify({'error': 'Internal server error'}), 500

        @self.app.route('/api/documents/<doc_id>/feedback/risk-assessment', methods=['POST'])
        def api_update_risk_assessment_feedback(doc_id):
            """Update risk assessment override for a document"""
            try:
                data = request.get_json()
                if not data:
                    return jsonify({'error': 'No data provided'}), 400

                risk_override = data.get('risk_override')
                comments = data.get('comments')
                updated_by = data.get('updated_by')

                if not risk_override:
                    return jsonify({'error': 'Risk override is required'}), 400

                if risk_override not in ['HIGH', 'MEDIUM', 'LOW']:
                    return jsonify({'error': 'Invalid risk level. Use: HIGH, MEDIUM, LOW'}), 400

                success = self.document_service.update_risk_assessment_override(doc_id, risk_override, comments, updated_by)
                if success:
                    return jsonify({'success': True, 'message': 'Risk assessment override updated'})
                else:
                    return jsonify({'error': 'Failed to update risk assessment override'}), 500

            except Exception as e:
                self.logger.error(f"Error updating risk assessment override: {e}")
                return jsonify({'error': 'Internal server error'}), 500

    def run(self):
        """Run the web interface"""
        import os
        debug_mode = os.getenv('FLASK_DEBUG', '0') == '1' or os.getenv('SVN_MONITOR_ENV') == 'development'

        self.logger.info(f"Starting web interface on {self.monitor_service.config.web_host}:{self.monitor_service.config.web_port}")
        if debug_mode:
            self.logger.info("Debug mode enabled - templates and static files will auto-reload")

        self.app.run(
            host=self.monitor_service.config.web_host,
            port=self.monitor_service.config.web_port,
            debug=debug_mode,
            use_reloader=debug_mode,
            use_debugger=debug_mode
        )
