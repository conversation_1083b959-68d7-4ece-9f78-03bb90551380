# System Architecture & Design

This document describes the architecture, design patterns, and technical decisions behind the RepoSense AI system.

## Architecture Overview

RepoSense AI follows a modular, service-oriented architecture that separates concerns and promotes maintainability. The system is designed around the principle of single responsibility, with each component handling a specific aspect of the monitoring workflow.

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Interface │    │  Monitor Service│    │ Repository Disc.│
│   (Flask App)   │    │  (Background)   │    │   Service       │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴───────────┐
                    │    Config Manager       │
                    │  (Central Config)       │
                    └─────────────┬───────────┘
                                  │
    ┌─────────────┬─────────────┬─┴─────────────┬─────────────┐
    │             │             │               │             │
┌───▼───┐   ┌─────▼─────┐  ┌────▼────┐   ┌─────▼─────┐  ┌────▼────┐
│ User  │   │   Email   │  │ Ollama  │   │   File    │  │   SVN   │
│ Mgmt  │   │ Service   │  │ Service │   │ Manager   │  │ Service │
│Service│   │           │  │         │   │           │  │         │
└───────┘   └───────────┘  └─────────┘   └───────────┘  └─────────┘
```

### Core Application Modules

The application is built with a modular architecture:

- **`reposense_ai_app.py`** - Main application entry point and orchestration
- **`models.py`** - Data models for configuration and commit information
- **`config_manager.py`** - Configuration loading, saving, and validation
- **`repository_backends/`** - Plugin-based repository backends (SVN, Git future)
- **`ollama_service.py`** - AI integration for documentation generation
- **`email_service.py`** - Email notification handling with recipient management
- **`file_manager.py`** - File operations and output organization
- **`monitor_service.py`** - Core monitoring logic and coordination
- **`web_interface.py`** - Flask-based web interface and API endpoints
- **`document_service.py`** - Document management and file system scanning

### Modular Architecture Benefits

The RepoSense AI evolved from a monolithic architecture to a modular design:

#### **Refactoring Impact**
- **Original**: 662 lines in single file
- **Refactored**: 821 lines across 9 files (average 91 lines per file)
- **Main file**: Reduced from 662 to 61 lines (90% reduction)

#### **Key Benefits**

1. **Separation of Concerns**: Each module has a single, well-defined responsibility
2. **Improved Testability**: Individual components can be unit tested in isolation
3. **Enhanced Maintainability**: Changes to one component don't affect others
4. **Better Code Organization**: Logical grouping of related functionality
5. **Reusability**: Components can be reused in other projects

#### **Module Dependencies**
```
reposense_ai_app.py
├── monitor_service.py
│   ├── repository_backends/
│   │   └── models.py
│   ├── ollama_client.py
│   │   └── models.py
│   ├── email_service.py
│   │   └── models.py
│   └── file_manager.py
│       └── models.py
└── web_interface.py
    └── monitor_service.py (and its dependencies)
```

### Directory Structure

```
RepoSense AI/
├── docker-compose.yml
├── docker-compose.dev.yml
├── Dockerfile
├── reposense_ai_app.py
├── requirements.txt
├── repository_backends/          # Plugin architecture
│   ├── base.py                  # Abstract base class
│   ├── svn_backend.py           # SVN implementation
│   └── git_backend.py           # Git implementation (future)
├── services/                    # Core services
│   ├── config_manager.py
│   ├── monitor_service.py
│   ├── document_service.py
│   └── ...
├── data/                        # Persistent data (mounted volume)
│   ├── config.json             # Configuration file
│   ├── reposense_ai.log  # Application logs
│   └── repositories/           # Repository-specific outputs
│       ├── {repo-id-1}/
│       │   ├── docs/           # Generated documentation
│       │   └── emails/         # Email copies
│       └── {repo-id-2}/
└── docs/                       # Documentation
```

## Core Components

### 1. Web Interface (`web_interface.py`)
- **Purpose**: Provides HTTP endpoints and web UI for system interaction
- **Technology**: Flask with Jinja2 templates
- **Responsibilities**:
  - User authentication and session management
  - Repository configuration interface
  - User management interface
  - Repository discovery interface
  - System monitoring dashboard
  - RESTful API endpoints

### 2. Monitor Service (`monitor_service.py`)
- **Purpose**: Core monitoring logic and orchestration
- **Pattern**: Background service with threading
- **Responsibilities**:
  - Repository polling and change detection
  - Coordination between services
  - Status tracking and reporting
  - Error handling and recovery

### 3. User Management Service (`user_management_service.py`)
- **Purpose**: User lifecycle and repository associations
- **Pattern**: Service layer with business logic
- **Responsibilities**:
  - User CRUD operations
  - Role-based access control
  - Repository-user associations
  - Notification preference management

### 4. Repository Discovery Service (`repository_discovery_service.py`)
- **Purpose**: Automatic repository discovery from SVN servers
- **Pattern**: External integration service
- **Responsibilities**:
  - SVN server scanning
  - Repository structure analysis
  - Bulk repository import
  - Access validation

### 5. Configuration Manager (`config_manager.py`)
- **Purpose**: Centralized configuration management
- **Pattern**: Singleton-like configuration store
- **Responsibilities**:
  - Configuration persistence (JSON)
  - Validation and schema enforcement
  - Configuration updates and migrations
  - Default value management

## Data Models

### User Model
```python
@dataclass
class User:
    id: str                           # Unique identifier
    username: str                     # Login username
    email: str                        # Email address
    full_name: str                    # Display name
    role: UserRole                    # ADMIN, MANAGER, DEVELOPER, VIEWER
    enabled: bool                     # Account status
    receive_all_notifications: bool   # Global notification preference
    repository_subscriptions: List[str] # Specific repository subscriptions
    phone: Optional[str]              # Contact phone
    department: Optional[str]         # Organizational unit
    created_date: Optional[str]       # Account creation timestamp
    last_modified: Optional[str]      # Last update timestamp
```

### Repository Model
```python
@dataclass
class RepositoryConfig:
    id: str                          # Unique identifier
    name: str                        # Display name
    url: str                         # SVN repository URL
    username: Optional[str]          # SVN authentication
    password: Optional[str]          # SVN authentication
    last_revision: int               # Last processed revision
    enabled: bool                    # Monitoring status
    assigned_users: List[str]        # User IDs with access
    email_recipients: List[str]      # Legacy email support
```

### Configuration Model
```python
@dataclass
class Config:
    # Repository and user collections
    repositories: List[RepositoryConfig]
    users: List[User]
    
    # Service configurations
    ollama_config: OllamaConfig
    email_config: EmailConfig
    web_config: WebConfig
    
    # System settings
    monitoring_interval: int
    output_directory: str
    log_level: str
```

## Design Patterns

### 1. Service Layer Pattern
Each major functionality is encapsulated in a dedicated service class:
- Clear separation of concerns
- Testable business logic
- Consistent error handling
- Dependency injection support

### 2. Repository Pattern
Configuration and data access through centralized managers:
- Abstracted data persistence
- Consistent CRUD operations
- Transaction-like operations
- Easy testing with mocks

### 3. Observer Pattern
Event-driven architecture for monitoring:
- Decoupled change detection
- Extensible notification system
- Asynchronous processing
- Error isolation

### 4. Factory Pattern
Service instantiation and configuration:
- Consistent object creation
- Configuration injection
- Dependency resolution
- Testing support

## Security Considerations

### Authentication & Authorization
- Role-based access control (RBAC)
- User session management
- Repository-level permissions
- API endpoint protection

### Data Protection
- Configuration encryption support
- Secure credential storage
- Input validation and sanitization
- SQL injection prevention

### Network Security
- HTTPS support for web interface
- Secure SVN authentication
- Email encryption (TLS/SSL)
- Container network isolation

## Scalability Design

### Horizontal Scaling
- Stateless service design
- External configuration storage
- Load balancer compatibility
- Container orchestration ready

### Performance Optimization
- Efficient SVN polling
- Caching strategies
- Background processing
- Resource pooling

### Resource Management
- Configurable monitoring intervals
- Memory-efficient data structures
- Graceful degradation
- Resource cleanup

## Error Handling Strategy

### Layered Error Handling
1. **Service Level**: Business logic errors and validation
2. **Integration Level**: External service failures
3. **System Level**: Infrastructure and runtime errors
4. **User Level**: Friendly error messages and recovery

### Resilience Patterns
- **Circuit Breaker**: Prevent cascade failures
- **Retry Logic**: Handle transient failures
- **Fallback**: Graceful degradation
- **Timeout**: Prevent hanging operations

## Testing Strategy

### Unit Testing
- Service layer testing
- Mock external dependencies
- Configuration validation
- Error condition testing

### Integration Testing
- End-to-end workflows
- External service integration
- Database operations
- API endpoint testing

### System Testing
- Docker container testing
- Performance benchmarking
- Security testing
- User acceptance testing

## Future Architecture Considerations

### Microservices Evolution
- Service decomposition
- API gateway introduction
- Event streaming (Kafka/RabbitMQ)
- Service mesh implementation

### Cloud Native Features
- Kubernetes deployment
- Auto-scaling capabilities
- Health check endpoints
- Metrics and observability

### Advanced Features
- Real-time notifications (WebSocket)
- Advanced analytics
- Machine learning integration
- Multi-tenant support

## Technology Stack

### Backend
- **Python 3.8+**: Core application language
- **Flask**: Web framework and API
- **Threading**: Background processing
- **JSON**: Configuration persistence
- **Subprocess**: SVN command execution

### Frontend
- **HTML5/CSS3**: Modern web standards
- **Bootstrap 5**: Responsive UI framework
- **JavaScript**: Interactive functionality
- **Jinja2**: Server-side templating

### Infrastructure
- **Docker**: Containerization
- **Docker Compose**: Multi-container orchestration
- **SVN**: Version control integration
- **SMTP**: Email delivery
- **Ollama**: AI model integration

### Development
- **Git**: Source control
- **pytest**: Testing framework
- **Black**: Code formatting
- **mypy**: Type checking
