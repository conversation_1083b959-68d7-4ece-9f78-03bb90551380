#!/usr/bin/env python3
"""
Test script for hybrid LLM + heuristic metadata extraction
"""

import sys
sys.path.append('/app')

from document_service import DocumentService
from ollama_client import OllamaClient
from models import Config

def test_hybrid_extraction():
    """Test the hybrid extraction approach"""
    
    # Create a config with Ollama settings
    config = Config()
    config.ollama_host = "http://ollama-server-local:11434"
    config.ollama_model = "llama3:latest"
    
    # Create Ollama client
    ollama_client = OllamaClient(config)
    
    # Test connection
    if not ollama_client.test_connection():
        print("❌ Ollama not connected - testing heuristics only")
        ollama_client = None
    else:
        print("✅ Ollama connected - testing hybrid approach")
    
    # Create document service with Ollama client
    ds = DocumentService(ollama_client=ollama_client)
    
    # Test cases
    test_cases = [
        {
            "name": "Clear positive case",
            "content": """## Code Review Recommendation
This commit should be code reviewed due to its complexity."""
        },
        {
            "name": "Clear negative case", 
            "content": """## Code Review Recommendation
This commit should not require code review as it's trivial."""
        },
        {
            "name": "Ambiguous case (should trigger LLM)",
            "content": """## Code Review Recommendation
The changes in this commit involve updating configuration files and adding new functionality to the authentication system. While the changes are well-structured, they touch critical security components."""
        },
        {
            "name": "Missing section (should trigger LLM)",
            "content": """## Summary
This commit adds new features to the system.

## Technical Details
The implementation uses modern patterns."""
        }
    ]
    
    print("\n" + "="*60)
    print("TESTING HYBRID METADATA EXTRACTION")
    print("="*60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)
        
        result = ds._extract_code_review_recommendation(test_case['content'])
        
        if result is True:
            print("✅ Result: Code review RECOMMENDED")
        elif result is False:
            print("❌ Result: Code review NOT recommended")
        else:
            print("❓ Result: Could not determine")
        
        # Test all hybrid extraction methods
        if ollama_client:
            print("Testing all hybrid extraction methods...")

            # Test individual methods
            priority = ds._extract_code_review_priority(test_case['content'])
            doc_impact = ds._extract_documentation_impact(test_case['content'])
            risk_level = ds._extract_risk_level(test_case['content'])

            print(f"  Priority: {priority}")
            print(f"  Doc Impact: {doc_impact}")
            print(f"  Risk Level: {risk_level}")

            # Test full LLM extraction
            print("Testing full LLM metadata extraction...")
            llm_metadata = ds._extract_metadata_with_llm(test_case['content'])
            print(f"  LLM metadata: {llm_metadata}")

if __name__ == "__main__":
    test_hybrid_extraction()
