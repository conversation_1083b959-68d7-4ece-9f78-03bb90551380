{% extends "base.html" %}

{% block title %}Repository Discovery - Repository Monitor{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Repository Discovery</h1>
            <p class="page-subtitle">Discover and import repositories from SVN servers</p>
        </div>
        <a href="{{ url_for('repositories_page') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left"></i> Back to Repositories
        </a>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-search"></i>Discovery Settings</h5>
            </div>
            <div class="card-body">
                {% if not config.svn_server_url %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Tip:</strong> Configure your SVN server settings in
                    <a href="{{ url_for('config_page') }}" class="alert-link">Settings</a>
                    to pre-populate these fields.
                </div>
                {% endif %}
                <form id="discoveryForm">
                    <div class="mb-3">
                        <label for="base_url" class="form-label">SVN Server Base URL *</label>
                        <input type="text" class="form-control" id="base_url" name="base_url"
                               value="{{ config.svn_server_url or '' }}"
                               placeholder="http://your-server:port/svn">
                        <div class="form-text">Base URL of your SVN server (configured in <a href="{{ url_for('config_page') }}">Settings</a>)</div>
                    </div>

                    <div class="mb-3">
                        <label for="username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="username" name="username"
                               value="{{ config.svn_server_username or '' }}">
                        <div class="form-text">Optional: Username for authentication</div>
                    </div>

                    <div class="mb-3">
                        <label for="password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="password" name="password"
                               value="{{ config.svn_server_password or '' }}">
                        <div class="form-text">Optional: Password for authentication</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="max_depth" class="form-label">Search Depth</label>
                        <select class="form-select" id="max_depth" name="max_depth">
                            <option value="1">1 level (immediate subdirectories)</option>
                            <option value="2">2 levels</option>
                            <option value="3" selected>3 levels (recommended)</option>
                            <option value="4">4 levels</option>
                            <option value="5">5 levels (may be slow)</option>
                        </select>
                        <div class="form-text">How deep to search for repositories</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100" id="scanBtn">
                        <i class="fas fa-search"></i> Start Discovery
                    </button>
                </form>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h6><i class="fas fa-info-circle"></i>Discovery Tips</h6>
            </div>
            <div class="card-body">
                <ul class="small mb-0">
                    <li>Discovery looks for standard SVN layouts (trunk, branches, tags)</li>
                    <li>Repositories are identified by their structure</li>
                    <li>Use authentication if your server requires it</li>
                    <li>Lower search depth for faster results</li>
                    <li>Discovered repositories start disabled for safety</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="col-md-8">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-code-branch"></i>Discovered Repositories</h5>
                <div id="discoveryStatus" class="text-muted">
                    Ready to scan
                </div>
            </div>
            <div class="card-body">
                <div id="loadingIndicator" class="text-center py-4" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Scanning...</span>
                    </div>
                    <p class="mt-2">Scanning for repositories...</p>
                </div>
                
                <div id="noResults" class="text-center py-4" style="display: none;">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5>No Repositories Found</h5>
                    <p class="text-muted">Try adjusting your search parameters or check the server URL.</p>
                </div>
                
                <div id="resultsContainer" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <span id="resultsCount" class="text-muted"></span>
                        <button class="btn btn-sm btn-success" onclick="importAllRepositories()">
                            <i class="fas fa-download"></i> Import All
                        </button>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>Path</th>
                                    <th>Last Revision</th>
                                    <th>Last Author</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="repositoriesTable">
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div id="errorMessage" class="alert alert-danger" style="display: none;">
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Import Repository Modal -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import Repository</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label class="form-label">Repository Name</label>
                    <input type="text" class="form-control" id="import_name" readonly>
                </div>
                <div class="mb-3">
                    <label class="form-label">Repository URL</label>
                    <input type="text" class="form-control" id="import_url" readonly>
                </div>
                <div class="mb-3">
                    <label for="import_username" class="form-label">Username</label>
                    <input type="text" class="form-control" id="import_username">
                    <div class="form-text">Leave empty if no authentication required</div>
                </div>
                <div class="mb-3">
                    <label for="import_password" class="form-label">Password</label>
                    <input type="password" class="form-control" id="import_password">
                    <div class="form-text">Leave empty if no authentication required</div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="confirmImport()">Import Repository</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let discoveredRepositories = [];
let currentImportRepo = null;

document.getElementById('discoveryForm').addEventListener('submit', function(e) {
    e.preventDefault();

    // Validate base URL
    const baseUrlField = document.getElementById('base_url');
    const baseUrl = baseUrlField.value.trim();

    // Clear previous validation state
    baseUrlField.classList.remove('is-invalid');

    if (!baseUrl) {
        baseUrlField.classList.add('is-invalid');
        showError('Please enter a base URL');
        baseUrlField.focus();
        return;
    }

    // Basic URL validation
    try {
        new URL(baseUrl);
    } catch (e) {
        baseUrlField.classList.add('is-invalid');
        showError('Please enter a valid URL (e.g., http://sundc:81/svn)');
        baseUrlField.focus();
        return;
    }

    startDiscovery();
});

// Clear validation state when user types
document.getElementById('base_url').addEventListener('input', function() {
    this.classList.remove('is-invalid');
    document.getElementById('errorMessage').style.display = 'none';
});

// Clear any initial validation state on page load
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('base_url').classList.remove('is-invalid');
    document.getElementById('errorMessage').style.display = 'none';
});

function startDiscovery() {
    const formData = new FormData(document.getElementById('discoveryForm'));

    // Trim the base URL to remove any whitespace
    const baseUrl = document.getElementById('base_url').value.trim();
    formData.set('base_url', baseUrl);

    const scanBtn = document.getElementById('scanBtn');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const resultsContainer = document.getElementById('resultsContainer');
    const noResults = document.getElementById('noResults');
    const errorMessage = document.getElementById('errorMessage');
    const discoveryStatus = document.getElementById('discoveryStatus');
    
    // Show loading state
    scanBtn.disabled = true;
    scanBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Scanning...';
    loadingIndicator.style.display = 'block';
    resultsContainer.style.display = 'none';
    noResults.style.display = 'none';
    errorMessage.style.display = 'none';
    discoveryStatus.textContent = 'Scanning...';
    
    fetch('/repositories/discover/scan', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            discoveredRepositories = data.repositories;
            displayResults(data.repositories);
            discoveryStatus.textContent = `Found ${data.repositories.length} repositories`;
        } else {
            showError(data.message);
            discoveryStatus.textContent = 'Scan failed';
        }
    })
    .catch(error => {
        showError('Network error: ' + error.message);
        discoveryStatus.textContent = 'Scan failed';
    })
    .finally(() => {
        scanBtn.disabled = false;
        scanBtn.innerHTML = '<i class="fas fa-search"></i> Start Discovery';
        loadingIndicator.style.display = 'none';
    });
}

function displayResults(repositories) {
    const resultsContainer = document.getElementById('resultsContainer');
    const noResults = document.getElementById('noResults');
    const resultsCount = document.getElementById('resultsCount');
    const repositoriesTable = document.getElementById('repositoriesTable');
    
    if (repositories.length === 0) {
        noResults.style.display = 'block';
        resultsContainer.style.display = 'none';
        return;
    }
    
    resultsCount.textContent = `Found ${repositories.length} repositories`;
    repositoriesTable.innerHTML = '';
    
    repositories.forEach((repo, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><strong>${repo.name}</strong></td>
            <td><code>${repo.path}</code></td>
            <td>${repo.last_revision || '-'}</td>
            <td>${repo.last_author || '-'}</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="showImportModal(${index})">
                    <i class="fas fa-download"></i> Import
                </button>
            </td>
        `;
        repositoriesTable.appendChild(row);
    });
    
    resultsContainer.style.display = 'block';
    noResults.style.display = 'none';
}

function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
}

function showImportModal(index) {
    currentImportRepo = discoveredRepositories[index];
    
    document.getElementById('import_name').value = currentImportRepo.name;
    document.getElementById('import_url').value = currentImportRepo.url;
    document.getElementById('import_username').value = document.getElementById('username').value;
    document.getElementById('import_password').value = document.getElementById('password').value;
    
    new bootstrap.Modal(document.getElementById('importModal')).show();
}

function confirmImport() {
    if (!currentImportRepo) return;
    
    const importData = {
        ...currentImportRepo,
        username: document.getElementById('import_username').value || null,
        password: document.getElementById('import_password').value || null
    };
    
    fetch('/repositories/import', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(importData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Repository imported successfully!');
            bootstrap.Modal.getInstance(document.getElementById('importModal')).hide();
            // Remove from discovered list
            const index = discoveredRepositories.indexOf(currentImportRepo);
            if (index > -1) {
                discoveredRepositories.splice(index, 1);
                displayResults(discoveredRepositories);
            }
        } else {
            alert('Import failed: ' + data.message);
        }
    })
    .catch(error => {
        alert('Network error: ' + error.message);
    });
}

function importAllRepositories() {
    if (discoveredRepositories.length === 0) return;
    
    if (!confirm(`Import all ${discoveredRepositories.length} repositories?`)) return;
    
    const username = document.getElementById('username').value || null;
    const password = document.getElementById('password').value || null;
    
    let imported = 0;
    let failed = 0;
    
    discoveredRepositories.forEach(repo => {
        const importData = {
            ...repo,
            username: username,
            password: password
        };
        
        fetch('/repositories/import', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(importData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                imported++;
            } else {
                failed++;
            }
            
            // Check if all requests completed
            if (imported + failed === discoveredRepositories.length) {
                alert(`Import completed: ${imported} successful, ${failed} failed`);
                if (imported > 0) {
                    discoveredRepositories = [];
                    displayResults([]);
                }
            }
        })
        .catch(error => {
            failed++;
            if (imported + failed === discoveredRepositories.length) {
                alert(`Import completed: ${imported} successful, ${failed} failed`);
            }
        });
    });
}
</script>
{% endblock %}
