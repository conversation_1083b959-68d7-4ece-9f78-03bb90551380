"""
Document Service for Repository Monitor

Manages generated documentation files and provides web interface access.
"""

import os
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import re


@dataclass
class Document:
    """Represents a generated document"""
    id: str
    repository_id: str
    repository_name: str
    revision: int
    date: datetime
    filename: str
    filepath: str
    size: int
    author: str
    commit_message: str
    
    @property
    def display_name(self) -> str:
        """Get a user-friendly display name"""
        return f"Revision {self.revision} - {self.date.strftime('%Y-%m-%d %H:%M')}"
    
    @property
    def relative_path(self) -> str:
        """Get relative path from output directory"""
        return str(Path(self.filepath).relative_to(Path("/app/data/output")))


class DocumentService:
    """Service for managing generated documents"""
    
    def __init__(self, output_dir: str = "/app/data/output"):
        self.output_dir = Path(output_dir)
        self.logger = logging.getLogger(__name__)
        
    def scan_documents(self) -> List[Document]:
        """Scan the output directory for generated documents"""
        documents = []
        
        try:
            repositories_dir = self.output_dir / "repositories"
            if not repositories_dir.exists():
                return documents
                
            for repo_dir in repositories_dir.iterdir():
                if repo_dir.is_dir():
                    docs_dir = repo_dir / "docs"
                    if docs_dir.exists():
                        documents.extend(self._scan_repository_documents(repo_dir.name, docs_dir))
                        
        except Exception as e:
            self.logger.error(f"Error scanning documents: {e}")
            
        # Sort by date (newest first)
        documents.sort(key=lambda d: d.date, reverse=True)
        return documents
    
    def _scan_repository_documents(self, repo_id: str, docs_dir: Path) -> List[Document]:
        """Scan documents for a specific repository"""
        documents = []
        
        try:
            for doc_file in docs_dir.glob("*.md"):
                if doc_file.is_file():
                    doc = self._parse_document_file(repo_id, doc_file)
                    if doc:
                        documents.append(doc)
                        
        except Exception as e:
            self.logger.error(f"Error scanning repository documents in {docs_dir}: {e}")
            
        return documents
    
    def _parse_document_file(self, repo_id: str, doc_file: Path) -> Optional[Document]:
        """Parse a document file and extract metadata"""
        try:
            # Parse filename to extract revision and date
            # Expected format: revision_X_YYYY-MM-DD.md
            filename_match = re.match(r'revision_(\d+)_(\d{4}-\d{2}-\d{2})\.md', doc_file.name)
            if not filename_match:
                return None
                
            revision = int(filename_match.group(1))
            date_str = filename_match.group(2)
            
            # Read file content to extract metadata
            with open(doc_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Parse metadata from content
            repo_name = self._extract_field(content, "Repository")
            author = self._extract_field(content, "Author")
            commit_message = self._extract_field(content, "Message")
            date_field = self._extract_field(content, "Date")
            
            # Parse date
            doc_date = datetime.now()
            if date_field:
                try:
                    # Try parsing ISO format first
                    doc_date = datetime.fromisoformat(date_field.replace('Z', '+00:00'))
                except ValueError:
                    try:
                        # Fallback to date from filename
                        doc_date = datetime.strptime(date_str, '%Y-%m-%d')
                    except ValueError:
                        doc_date = datetime.now()
            
            return Document(
                id=f"{repo_id}_{revision}_{date_str}",
                repository_id=repo_id,
                repository_name=repo_name or repo_id.replace('-', ' ').title(),
                revision=revision,
                date=doc_date,
                filename=doc_file.name,
                filepath=str(doc_file),
                size=doc_file.stat().st_size,
                author=author or "Unknown",
                commit_message=commit_message or "No message"
            )
            
        except Exception as e:
            self.logger.error(f"Error parsing document file {doc_file}: {e}")
            return None
    
    def _extract_field(self, content: str, field_name: str) -> Optional[str]:
        """Extract a field value from document content"""
        pattern = rf'\*\*{field_name}:\*\*\s*(.+?)(?:\n|$)'
        match = re.search(pattern, content)
        return match.group(1).strip() if match else None
    
    def get_document_by_id(self, doc_id: str) -> Optional[Document]:
        """Get a specific document by ID"""
        documents = self.scan_documents()
        return next((doc for doc in documents if doc.id == doc_id), None)
    
    def get_document_content(self, doc_id: str) -> Optional[str]:
        """Get the content of a specific document"""
        document = self.get_document_by_id(doc_id)
        if not document:
            return None
            
        try:
            with open(document.filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            self.logger.error(f"Error reading document content {document.filepath}: {e}")
            return None
    
    def get_documents_by_repository(self, repo_id: str) -> List[Document]:
        """Get all documents for a specific repository"""
        documents = self.scan_documents()
        return [doc for doc in documents if doc.repository_id == repo_id]
    
    def get_repository_stats(self) -> Dict[str, int]:
        """Get statistics about documents per repository"""
        documents = self.scan_documents()
        stats = {}
        
        for doc in documents:
            if doc.repository_id not in stats:
                stats[doc.repository_id] = 0
            stats[doc.repository_id] += 1
            
        return stats
    
    def delete_document(self, doc_id: str) -> bool:
        """Delete a document file"""
        document = self.get_document_by_id(doc_id)
        if not document:
            return False
            
        try:
            os.remove(document.filepath)
            self.logger.info(f"Deleted document: {document.filepath}")
            return True
        except Exception as e:
            self.logger.error(f"Error deleting document {document.filepath}: {e}")
            return False
