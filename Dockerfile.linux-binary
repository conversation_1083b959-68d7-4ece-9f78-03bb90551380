# RepoSense AI - Linux Binary Deployment
# Build Linux binary from source and deploy in minimal container

# Stage 1: Build Linux binary
FROM python:3.11-slim as builder

# Install system dependencies for building
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    make \
    libffi-dev \
    libssl-dev \
    subversion \
    git \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy source code
COPY . .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install pyinstaller

# Build Linux binary
RUN pyinstaller --onefile \
    --name reposense-ai \
    --add-data "templates:templates" \
    --add-data "static:static" \
    --add-data "docs:docs" \
    --add-data "marketing:marketing" \
    --hidden-import=repository_backends.svn_backend \
    --hidden-import=repository_backends.git_backend \
    --hidden-import=repository_backends.base \
    --hidden-import=flask \
    --hidden-import=sqlite3 \
    --hidden-import=requests \
    --hidden-import=jinja2 \
    --collect-all=flask \
    --collect-all=jinja2 \
    --exclude-module=PyQt5 \
    --exclude-module=PySide6 \
    --exclude-module=tkinter \
    reposense_ai_binary.py

# Stage 2: Runtime Environment
FROM ubuntu:22.04 as runtime

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    subversion \
    git \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for security
RUN groupadd -r repository-monitor && \
    useradd -r -g repository-monitor -d /app -s /bin/bash repository-monitor

# Set working directory
WORKDIR /app

# Copy binary from builder stage
COPY --from=builder /app/dist/reposense-ai /app/reposense-ai

# Copy configuration templates
COPY --from=builder /app/config.example.json /app/config.example.json

# Create startup script
RUN cat > /app/start.sh << 'EOF'
#!/bin/bash
set -e

# Initialize configuration if not exists
if [ ! -f /app/data/config.json ]; then
    echo "Initializing configuration..."
    mkdir -p /app/data
    cp /app/config.example.json /app/data/config.json
    echo "Configuration initialized. Please customize /app/data/config.json"
fi

# Start RepoSense AI
echo "Starting RepoSense AI..."
exec /app/repository-monitor
EOF

RUN chmod +x /app/start.sh /app/repository-monitor && \
    mkdir -p /app/data /app/logs && \
    chown -R repository-monitor:repository-monitor /app

# Switch to non-root user
USER repository-monitor

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV REPOSENSE_AI_CONFIG=/app/data/config.json
ENV REPOSENSE_AI_DATA_DIR=/app/data
ENV REPOSENSE_AI_LOG_DIR=/app/logs

# Start the application
CMD ["/app/start.sh"]
