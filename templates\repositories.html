{% extends "base.html" %}

{% block title %}Repositories - RepoSense AI{% endblock %}

{% block extra_css %}
<style>
    /* Resizable modal styles */
    .modal-dialog-resizable {
        resize: both;
        overflow: auto;
        min-width: 600px;
        min-height: 400px;
        max-width: 95vw;
        max-height: 95vh;
    }

    .modal-dialog-resizable .modal-content {
        height: 100%;
        min-height: 400px;
        display: flex;
        flex-direction: column;
    }

    .modal-dialog-resizable .modal-body {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .modal-dialog-resizable .modal-body .row {
        flex: 1;
        margin: 0;
    }

    .modal-dialog-resizable .modal-body .col-md-8,
    .modal-dialog-resizable .modal-body .col-md-4 {
        display: flex;
        flex-direction: column;
        padding: 0 15px;
    }

    .modal-dialog-resizable .card {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-bottom: 0;
    }

    .modal-dialog-resizable .card-body {
        flex: 1;
        overflow: hidden;
        padding: 0;
    }

    /* Resize handle indicator */
    .modal-dialog-resizable::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 20px;
        height: 20px;
        background: linear-gradient(-45deg, transparent 30%, #999 30%, #999 40%, transparent 40%, transparent 60%, #999 60%, #999 70%, transparent 70%);
        cursor: nw-resize;
        pointer-events: none;
        opacity: 0.7;
        transition: opacity 0.2s;
    }

    .modal-dialog-resizable:hover::after {
        opacity: 1;
    }

    /* Scrollable areas */
    #file_browser,
    #selected_files {
        overflow-y: auto;
        flex: 1;
    }

    /* Remove fixed heights to allow flexible sizing */
    .modal-dialog-resizable #file_browser {
        max-height: none;
    }

    .modal-dialog-resizable #selected_files {
        max-height: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Repository Management</h1>
            <p class="page-subtitle">Configure and manage your repositories</p>
        </div>
        <div class="btn-group">
            <a href="{{ url_for('repository_discovery_page') }}" class="btn btn-outline-primary">
                <i class="fas fa-search"></i> Discover Repositories
            </a>
            <button type="button" class="btn btn-outline-secondary" onclick="refreshRepositoryStatus()">
                <i class="fas fa-sync-alt"></i> Refresh Status
            </button>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRepositoryModal">
                <i class="fas fa-plus"></i> Add Repository
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-code-branch"></i>Repositories</h5>
            </div>
            <div class="card-body">
                {% if repositories %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>URL</th>
                                    <th>Status</th>
                                    <th>Product Docs</th>
                                    <th>Last Revision</th>
                                    <th>Commit Date</th>
                                    <th>Processed Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for repo in repositories %}
                                <tr>
                                    <td>
                                        <strong>{{ repo.name }}</strong>
                                        {% if not repo.enabled %}
                                            <span class="badge bg-secondary ms-2">Disabled</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ repo.url }}</small>
                                    </td>
                                    <td>
                                        <span class="status-indicator {% if repo.enabled %}status-running{% else %}status-stopped{% endif %}"></span>
                                        {% if repo.enabled %}Enabled{% else %}Disabled{% endif %}
                                    </td>
                                    <td>
                                        {% if repo.product_documentation_files %}
                                            <span class="badge bg-success" title="{{ repo.product_documentation_files|join(', ') }}">
                                                <i class="fas fa-file-alt"></i> {{ repo.product_documentation_files|length }} file{{ 's' if repo.product_documentation_files|length != 1 else '' }}
                                            </span>
                                        {% else %}
                                            <span class="badge bg-secondary" title="Using default documentation discovery">
                                                <i class="fas fa-search"></i> Auto
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ repo.last_revision }}</span>
                                    </td>
                                    <td>
                                        {% if repo.last_commit_date %}
                                            <small class="text-muted">
                                                <i class="fas fa-code-branch me-1"></i>
                                                {{ repo.last_commit_date.strftime('%Y-%m-%d %H:%M:%S') }}
                                            </small>
                                        {% else %}
                                            <small class="text-muted">Unknown</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if repo.last_processed_time %}
                                            <small class="text-muted">
                                                <i class="fas fa-cogs me-1"></i>
                                                {{ repo.last_processed_time.strftime('%Y-%m-%d %H:%M:%S') }}
                                            </small>
                                        {% else %}
                                            <small class="text-muted">Never</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button class="btn btn-sm btn-outline-primary"
                                                    data-repo-id="{{ repo.id }}"
                                                    data-repo-name="{{ repo.name }}"
                                                    data-repo-url="{{ repo.url }}"
                                                    data-repo-username="{{ repo.username or '' }}"
                                                    data-repo-enabled="{{ repo.enabled|lower }}"
                                                    data-repo-email="{{ (repo.email_recipients or [])|join(', ') }}"
                                                    data-repo-docs="{{ (repo.product_documentation_files or [])|join('\n') }}"
                                                    onclick="editRepositoryFromData(this)"
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <a href="{{ url_for('historical_scan_page', repo_id=repo.id) }}"
                                               class="btn btn-sm btn-outline-info"
                                               title="Historical Scan">
                                                <i class="fas fa-history"></i> History
                                            </a>
                                            <form method="POST" action="{{ url_for('delete_repository', repo_id=repo.id) }}"
                                                  style="display: inline;"
                                                  onsubmit="return confirm('Are you sure you want to delete repository \'{{ repo.name }}\'?')">
                                                <button type="submit" class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-code-branch fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No repositories configured</h5>
                        <p class="text-muted">Add your first SVN repository to start monitoring.</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRepositoryModal">
                            <i class="fas fa-plus"></i> Add Repository
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add Repository Modal -->
<div class="modal fade" id="addRepositoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Repository</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_repository') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="add_name" class="form-label">Repository Name *</label>
                        <input type="text" class="form-control" id="add_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="add_url" class="form-label">Repository URL *</label>
                        <input type="url" class="form-control" id="add_url" name="url" required>
                        <div class="form-text">Example: https://svn.example.com/repo</div>
                    </div>
                    <div class="mb-3">
                        <label for="add_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="add_username" name="username">
                        <div class="form-text">Leave empty for anonymous access</div>
                    </div>
                    <div class="mb-3">
                        <label for="add_password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="add_password" name="password">
                    </div>
                    <div class="mb-3">
                        <label for="add_email_recipients" class="form-label">Email Recipients</label>
                        <textarea class="form-control" id="add_email_recipients" name="email_recipients" rows="3"
                                  placeholder="<EMAIL>, <EMAIL>"></textarea>
                        <div class="form-text">Repository-specific email recipients (comma-separated). These will be added to the global recipients.</div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="add_enabled" name="enabled" checked>
                        <label class="form-check-label" for="add_enabled">
                            Enable monitoring for this repository
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Repository</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Repository Modal -->
<div class="modal fade" id="editRepositoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Repository</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editRepositoryForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Repository Name *</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_url" class="form-label">Repository URL *</label>
                        <input type="url" class="form-control" id="edit_url" name="url" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="edit_username" name="username">
                    </div>
                    <div class="mb-3">
                        <label for="edit_password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="edit_password" name="password">
                        <div class="form-text">Leave empty to keep current password</div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_email_recipients" class="form-label">Email Recipients</label>
                        <textarea class="form-control" id="edit_email_recipients" name="email_recipients" rows="3"
                                  placeholder="<EMAIL>, <EMAIL>"></textarea>
                        <div class="form-text">Repository-specific email recipients (comma-separated). These will be added to the global recipients.</div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_product_documentation_files" class="form-label">Product Documentation Files</label>
                        <div class="input-group">
                            <textarea class="form-control" id="edit_product_documentation_files" name="product_documentation_files" rows="4"
                                      placeholder="README.md&#10;docs/user-guide.md&#10;docs/api-reference.md&#10;CHANGELOG.md"></textarea>
                            <button class="btn btn-outline-secondary" type="button" id="browse_docs_btn" onclick="openDocumentBrowser()">
                                <i class="fas fa-folder-open"></i> Browse
                            </button>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle text-info"></i>
                            Specify which files contain user-facing product documentation (one per line).
                            These files will be referenced by AI when analyzing documentation impact.
                            <br><strong>Supported formats:</strong> Markdown (.md), Text (.txt), HTML (.html), PDF (.pdf), Microsoft Word (.doc, .docx), RTF (.rtf), OpenDocument (.odt)
                            <br><strong>Examples:</strong> README.md, docs/user-guide.md, API.md, CHANGELOG.md, ProductGuide.docx
                            <br><strong>Tip:</strong> Use the Browse button to explore the repository and select files visually.
                            <br><small class="text-success"><i class="fas fa-check-circle"></i> Microsoft Word documents (.doc, .docx) are fully supported with automatic text extraction</small>
                        </div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_enabled" name="enabled">
                        <label class="form-check-label" for="edit_enabled">
                            Enable monitoring for this repository
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Repository</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Document Browser Modal -->
<div class="modal fade" id="documentBrowserModal" tabindex="-1" aria-labelledby="documentBrowserModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-resizable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="documentBrowserModalLabel">
                    <i class="fas fa-folder-open"></i> Browse Repository Documentation
                    <small class="text-muted ms-2" title="Drag the bottom-right corner to resize this dialog. Use Ctrl+1/2/3/0 for quick sizing.">
                        <i class="fas fa-expand-arrows-alt"></i> Resizable
                    </small>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-folder"></i> Repository Files
                                        <span id="current_path" class="text-muted ms-2">/</span>
                                    </h6>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="filter_docs" checked>
                                        <label class="form-check-label" for="filter_docs" title="Show only documentation files">
                                            <i class="fas fa-filter"></i> Docs Only
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div id="loading_indicator" class="text-center p-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <div class="mt-2">Loading repository files...</div>
                                </div>
                                <div id="file_browser" class="list-group list-group-flush" style="display: none;">
                                    <!-- File list will be populated here -->
                                </div>
                                <div id="error_message" class="alert alert-danger m-3" style="display: none;">
                                    <!-- Error messages will be shown here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-alt"></i> Selected Files
                                    <span id="selected_count" class="badge bg-primary ms-2">0</span>
                                </h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="selected_files" class="list-group list-group-flush">
                                    <div class="list-group-item text-muted text-center">
                                        <i class="fas fa-info-circle"></i>
                                        <br>Select documentation files from the repository browser
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="applySelectedFiles()">
                    <i class="fas fa-check"></i> Apply Selection
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function editRepository(id, name, url, username, enabled, emailRecipients, productDocFiles) {
    document.getElementById('edit_name').value = name;
    document.getElementById('edit_url').value = url;
    document.getElementById('edit_username').value = username;
    document.getElementById('edit_enabled').checked = enabled;
    document.getElementById('edit_email_recipients').value = emailRecipients || '';
    document.getElementById('edit_product_documentation_files').value = productDocFiles || '';

    // Update form action
    document.getElementById('editRepositoryForm').action = '/repositories/' + id + '/edit';

    // Show modal
    new bootstrap.Modal(document.getElementById('editRepositoryModal')).show();
}

function editRepositoryFromData(button) {
    // Get data from button attributes
    const id = button.getAttribute('data-repo-id');
    const name = button.getAttribute('data-repo-name');
    const url = button.getAttribute('data-repo-url');
    const username = button.getAttribute('data-repo-username');
    const enabled = button.getAttribute('data-repo-enabled') === 'true';
    const emailRecipients = button.getAttribute('data-repo-email');
    const productDocFiles = button.getAttribute('data-repo-docs');

    // Call the original function
    editRepository(id, name, url, username, enabled, emailRecipients, productDocFiles);
}

// Document browser functionality
let currentRepositoryId = null;
let currentPath = '/';
let selectedFiles = new Set();

function openDocumentBrowser() {
    // Get current repository info from the edit form
    const repoUrl = document.getElementById('edit_url').value;
    const repoUsername = document.getElementById('edit_username').value;

    if (!repoUrl) {
        alert('Please save the repository configuration first before browsing files.');
        return;
    }

    // Get repository ID from the form action
    const formAction = document.getElementById('editRepositoryForm').action;
    currentRepositoryId = formAction.split('/').slice(-2, -1)[0];

    // Reset browser state
    currentPath = '/';
    selectedFiles.clear();
    updateSelectedFilesDisplay();

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('documentBrowserModal'));
    modal.show();

    // Initialize resizable functionality
    initializeResizableModal();

    // Add event listener for filter toggle
    document.getElementById('filter_docs').addEventListener('change', function() {
        // Reload current directory with new filter setting
        loadRepositoryFiles(currentPath);
    });

    // Load the root directory
    loadRepositoryFiles('/');
}

function loadRepositoryFiles(path) {
    currentPath = path;
    document.getElementById('current_path').textContent = path || '/';

    // Show loading indicator
    document.getElementById('loading_indicator').style.display = 'block';
    document.getElementById('file_browser').style.display = 'none';
    document.getElementById('error_message').style.display = 'none';

    // Get filter setting
    const filterDocs = document.getElementById('filter_docs').checked;

    // Make API call to browse repository
    const params = new URLSearchParams({
        path: path || '/',
        filter_docs: filterDocs ? 'true' : 'false'
    });

    fetch(`/api/repositories/${currentRepositoryId}/browse?${params}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayRepositoryFiles(data.files, path);
            } else {
                showError(data.error || 'Failed to load repository files');
            }
        })
        .catch(error => {
            console.error('Error loading repository files:', error);
            showError('Failed to connect to repository. Please check the repository configuration.');
        })
        .finally(() => {
            document.getElementById('loading_indicator').style.display = 'none';
        });
}

function displayRepositoryFiles(files, currentPath) {
    const fileBrowser = document.getElementById('file_browser');
    fileBrowser.innerHTML = '';
    fileBrowser.style.display = 'block';

    // Add parent directory link if not at root
    if (currentPath !== '/' && currentPath !== '') {
        const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
        const parentItem = createFileItem('..', 'directory', parentPath, true);
        fileBrowser.appendChild(parentItem);
    }

    // Sort files: directories first, then files
    files.sort((a, b) => {
        if (a.type !== b.type) {
            return a.type === 'directory' ? -1 : 1;
        }
        return a.name.localeCompare(b.name);
    });

    // Add files and directories
    files.forEach(file => {
        const fullPath = currentPath === '/' ? file.name : `${currentPath}/${file.name}`;
        const fileItem = createFileItem(file.name, file.type, fullPath, false);
        fileBrowser.appendChild(fileItem);
    });
}

function createFileItem(name, type, fullPath, isParent) {
    const item = document.createElement('div');
    item.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';

    const leftContent = document.createElement('div');
    leftContent.className = 'd-flex align-items-center';

    const icon = document.createElement('i');
    if (isParent) {
        icon.className = 'fas fa-level-up-alt text-muted me-2';
    } else if (type === 'directory') {
        icon.className = 'fas fa-folder text-warning me-2';
    } else {
        icon.className = getFileIcon(name);
    }

    const nameSpan = document.createElement('span');
    nameSpan.textContent = name;
    if (isParent) {
        nameSpan.className = 'text-muted';
    }

    leftContent.appendChild(icon);
    leftContent.appendChild(nameSpan);
    item.appendChild(leftContent);

    // Add click handler
    if (type === 'directory' || isParent) {
        item.style.cursor = 'pointer';
        item.onclick = () => loadRepositoryFiles(fullPath);
    } else {
        // Add selection checkbox for files
        const rightContent = document.createElement('div');
        rightContent.className = 'd-flex align-items-center';

        // Add file type indicator for special formats
        const ext = name.split('.').pop().toLowerCase();
        if (['doc', 'docx', 'rtf', 'odt'].includes(ext)) {
            const typeIndicator = document.createElement('span');
            typeIndicator.className = 'badge bg-info me-2';
            typeIndicator.textContent = ext.toUpperCase();
            typeIndicator.title = 'Document file - content will be extracted for AI analysis';
            rightContent.appendChild(typeIndicator);
        }

        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.className = 'form-check-input';
        checkbox.checked = selectedFiles.has(fullPath);
        checkbox.onchange = (e) => toggleFileSelection(fullPath, e.target.checked);

        rightContent.appendChild(checkbox);
        item.appendChild(rightContent);

        // Make the whole item clickable to toggle selection
        item.style.cursor = 'pointer';
        item.onclick = (e) => {
            if (e.target !== checkbox) {
                checkbox.checked = !checkbox.checked;
                toggleFileSelection(fullPath, checkbox.checked);
            }
        };
    }

    return item;
}

function getFileIcon(filename) {
    const ext = filename.split('.').pop().toLowerCase();
    const iconMap = {
        // Text and Markdown files
        'md': 'fas fa-file-alt text-info me-2',
        'txt': 'fas fa-file-alt text-secondary me-2',
        'rst': 'fas fa-file-alt text-info me-2',

        // Web files
        'html': 'fas fa-file-code text-danger me-2',
        'htm': 'fas fa-file-code text-danger me-2',

        // Configuration files
        'json': 'fas fa-file-code text-warning me-2',
        'xml': 'fas fa-file-code text-success me-2',
        'yml': 'fas fa-file-code text-primary me-2',
        'yaml': 'fas fa-file-code text-primary me-2',

        // Document files
        'pdf': 'fas fa-file-pdf text-danger me-2',
        'doc': 'fas fa-file-word text-primary me-2',
        'docx': 'fas fa-file-word text-primary me-2',
        'rtf': 'fas fa-file-word text-info me-2',
        'odt': 'fas fa-file-alt text-success me-2',
        'pages': 'fas fa-file-alt text-warning me-2'
    };

    return iconMap[ext] || 'fas fa-file text-secondary me-2';
}

function toggleFileSelection(filePath, selected) {
    if (selected) {
        selectedFiles.add(filePath);
    } else {
        selectedFiles.delete(filePath);
    }
    updateSelectedFilesDisplay();
}

function updateSelectedFilesDisplay() {
    const selectedContainer = document.getElementById('selected_files');
    const countBadge = document.getElementById('selected_count');

    countBadge.textContent = selectedFiles.size;

    if (selectedFiles.size === 0) {
        selectedContainer.innerHTML = `
            <div class="list-group-item text-muted text-center">
                <i class="fas fa-info-circle"></i>
                <br>Select documentation files from the repository browser
            </div>
        `;
    } else {
        selectedContainer.innerHTML = '';
        Array.from(selectedFiles).sort().forEach(filePath => {
            const item = document.createElement('div');
            item.className = 'list-group-item d-flex justify-content-between align-items-center';

            const fileName = filePath.split('/').pop();
            const pathSpan = document.createElement('div');
            pathSpan.innerHTML = `
                <div class="fw-bold">${fileName}</div>
                <small class="text-muted">${filePath}</small>
            `;

            const removeBtn = document.createElement('button');
            removeBtn.className = 'btn btn-sm btn-outline-danger';
            removeBtn.innerHTML = '<i class="fas fa-times"></i>';
            removeBtn.onclick = () => {
                selectedFiles.delete(filePath);
                updateSelectedFilesDisplay();
                // Update checkboxes in the file browser
                const checkboxes = document.querySelectorAll('#file_browser input[type="checkbox"]');
                checkboxes.forEach(cb => {
                    const item = cb.closest('.list-group-item');
                    const itemPath = getItemPath(item);
                    if (itemPath === filePath) {
                        cb.checked = false;
                    }
                });
            };

            item.appendChild(pathSpan);
            item.appendChild(removeBtn);
            selectedContainer.appendChild(item);
        });
    }
}

function getItemPath(item) {
    // This is a helper function to get the path from a file item
    // Implementation depends on how we store the path in the item
    return item.dataset.path || '';
}

function applySelectedFiles() {
    const textarea = document.getElementById('edit_product_documentation_files');
    const filesArray = Array.from(selectedFiles).sort();
    textarea.value = filesArray.join('\n');

    // Close the modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('documentBrowserModal'));
    modal.hide();

    // Show success message
    if (filesArray.length > 0) {
        // You could add a toast notification here
        console.log(`Applied ${filesArray.length} selected files to configuration`);
    }
}

function showError(message) {
    const errorDiv = document.getElementById('error_message');
    errorDiv.textContent = message;
    errorDiv.style.display = 'block';
    document.getElementById('file_browser').style.display = 'none';
}

function initializeResizableModal() {
    const modalDialog = document.querySelector('#documentBrowserModal .modal-dialog-resizable');

    if (!modalDialog) return;

    // Set initial size (80% of viewport)
    const initialWidth = Math.min(1200, window.innerWidth * 0.8);
    const initialHeight = Math.min(800, window.innerHeight * 0.8);

    modalDialog.style.width = initialWidth + 'px';
    modalDialog.style.height = initialHeight + 'px';

    // Center the modal
    modalDialog.style.margin = 'auto';
    modalDialog.style.position = 'relative';

    // Add resize event listener to maintain proper layout
    const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
            // Trigger a layout recalculation when the modal is resized
            const fileBrowser = document.getElementById('file_browser');
            const selectedFiles = document.getElementById('selected_files');

            if (fileBrowser && selectedFiles) {
                // Force a repaint to ensure scrollbars appear correctly
                fileBrowser.style.display = 'none';
                selectedFiles.style.display = 'none';

                setTimeout(() => {
                    fileBrowser.style.display = '';
                    selectedFiles.style.display = '';
                }, 1);
            }
        }
    });

    resizeObserver.observe(modalDialog);

    // Store the observer for cleanup
    modalDialog._resizeObserver = resizeObserver;

    // Add keyboard shortcuts for common sizes
    document.addEventListener('keydown', function(e) {
        if (document.getElementById('documentBrowserModal').classList.contains('show')) {
            if (e.ctrlKey || e.metaKey) {
                switch(e.key) {
                    case '1':
                        // Small size
                        e.preventDefault();
                        resizeModalTo(800, 600);
                        break;
                    case '2':
                        // Medium size
                        e.preventDefault();
                        resizeModalTo(1000, 700);
                        break;
                    case '3':
                        // Large size
                        e.preventDefault();
                        resizeModalTo(1200, 800);
                        break;
                    case '0':
                        // Maximize
                        e.preventDefault();
                        resizeModalTo(window.innerWidth * 0.95, window.innerHeight * 0.95);
                        break;
                }
            }
        }
    });
}

function resizeModalTo(width, height) {
    const modalDialog = document.querySelector('#documentBrowserModal .modal-dialog-resizable');
    if (modalDialog) {
        modalDialog.style.width = Math.min(width, window.innerWidth * 0.95) + 'px';
        modalDialog.style.height = Math.min(height, window.innerHeight * 0.95) + 'px';
    }
}

// Cleanup when modal is hidden
document.getElementById('documentBrowserModal').addEventListener('hidden.bs.modal', function() {
    const modalDialog = document.querySelector('#documentBrowserModal .modal-dialog-resizable');
    if (modalDialog && modalDialog._resizeObserver) {
        modalDialog._resizeObserver.disconnect();
        delete modalDialog._resizeObserver;
    }
});

function refreshRepositoryStatus() {
    const refreshBtn = document.querySelector('button[onclick="refreshRepositoryStatus()"]');
    const originalHTML = refreshBtn.innerHTML;

    // Show loading state
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
    refreshBtn.disabled = true;

    // Force reload the page to get fresh data
    setTimeout(() => {
        location.reload();
    }, 500);
}
</script>
{% endblock %}
