#!/usr/bin/env python3
"""
File management operations for the SVN Monitor application
Handles file I/O, directory setup, and document saving
"""

import logging
import os
from pathlib import Path

from models import Config, CommitInfo


class FileManager:
    """Manager for file operations and directory setup"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def setup_directories(self):
        """Create necessary directories"""
        try:
            Path(self.config.output_dir).mkdir(parents=True, exist_ok=True)

            # Create directories for each repository
            for repo in self.config.repositories:
                repo_dir = self.get_repository_output_dir(repo.id)
                Path(f"{repo_dir}/docs").mkdir(parents=True, exist_ok=True)
                Path(f"{repo_dir}/emails").mkdir(parents=True, exist_ok=True)

            # Legacy directories for backward compatibility
            Path(f"{self.config.output_dir}/docs").mkdir(parents=True, exist_ok=True)
            Path(f"{self.config.output_dir}/emails").mkdir(parents=True, exist_ok=True)

            # Ensure data directory exists (for Docker volume)
            Path("/app/data").mkdir(parents=True, exist_ok=True)

            self.logger.info("Directories setup completed")
        except Exception as e:
            self.logger.error(f"Error setting up directories: {e}")
            raise

    def get_repository_output_dir(self, repo_id: str) -> str:
        """Get output directory for a specific repository"""
        # Sanitize repo_id for filesystem use
        safe_repo_id = "".join(c for c in repo_id if c.isalnum() or c in ('-', '_'))
        return f"{self.config.output_dir}/repositories/{safe_repo_id}"
    
    def save_documentation(self, commit: CommitInfo, documentation: str):
        """Save generated documentation to file"""
        try:
            # Use repository-specific directory if available
            if commit.repository_id:
                repo_dir = self.get_repository_output_dir(commit.repository_id)
                filename = f"{repo_dir}/docs/revision_{commit.revision}_{commit.date[:10]}.md"
            else:
                # Fallback to legacy directory
                filename = f"{self.config.output_dir}/docs/revision_{commit.revision}_{commit.date[:10]}.md"

            # Ensure directory exists
            Path(filename).parent.mkdir(parents=True, exist_ok=True)

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"# Repository Commit Analysis - Revision {commit.revision}\n\n")
                if commit.repository_name:
                    f.write(f"**Repository:** {commit.repository_name}\n")
                f.write(f"**Author:** {commit.author}\n")
                f.write(f"**Date:** {commit.date}\n")
                f.write(f"**Message:** {commit.message}\n\n")
                f.write("## Changed Files\n\n")
                for path in commit.changed_paths:
                    f.write(f"- {path}\n")
                f.write("\n---\n\n")
                f.write("# AI-Generated Analysis\n\n")
                f.write(documentation)
            
            self.logger.info(f"Documentation saved to {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"Error saving documentation: {e}")
            return None
    
    def save_email_copy(self, subject: str, body: str, commit: CommitInfo):
        """Save a copy of the sent email"""
        try:
            # Use repository-specific directory if available
            if commit.repository_id:
                repo_dir = self.get_repository_output_dir(commit.repository_id)
                email_filename = f"{repo_dir}/emails/revision_{commit.revision}_{commit.date[:10]}.txt"
            else:
                # Fallback to legacy directory
                email_filename = f"{self.config.output_dir}/emails/revision_{commit.revision}_{commit.date[:10]}.txt"

            # Ensure directory exists
            Path(email_filename).parent.mkdir(parents=True, exist_ok=True)

            full_body = f"""
{body}

---
Commit Details:
- Repository: {commit.repository_name or 'Unknown'}
- Revision: {commit.revision}
- Author: {commit.author}
- Date: {commit.date}
- Message: {commit.message}

Changed Files:
{chr(10).join('- ' + path for path in commit.changed_paths)}
"""

            # Get all recipients for this repository
            recipients = self.config.get_all_recipients_for_repository(commit.repository_id)

            with open(email_filename, 'w', encoding='utf-8') as f:
                f.write(f"To: {', '.join(recipients)}\n")
                f.write(f"Subject: {subject}\n\n")
                f.write(full_body)
            
            self.logger.info(f"Email copy saved to {email_filename}")
            return email_filename
            
        except Exception as e:
            self.logger.error(f"Error saving email copy: {e}")
            return None
    
    def get_log_file_path(self) -> str:
        """Get the path to the log file"""
        return '/app/data/repository_monitor.log'
    
    def read_recent_logs(self, lines: int = 100) -> list:
        """Read recent log entries"""
        try:
            log_file = self.get_log_file_path()
            if os.path.exists(log_file):
                with open(log_file, 'r') as f:
                    logs = f.read().split('\n')[-lines:]
                return logs
            else:
                return ['No logs available']
        except Exception as e:
            self.logger.error(f"Error reading logs: {e}")
            return [f'Error reading logs: {str(e)}']
