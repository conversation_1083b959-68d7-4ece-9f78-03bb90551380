# Deployment Guide

This guide covers production deployment of the SVN Monitor system using Docker, including security considerations, performance optimization, and maintenance procedures.

## Production Deployment with Docker

### Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- Minimum 2GB RAM
- 10GB available disk space
- Network access to SVN servers
- SMTP server for notifications
- Ollama server for AI features

### Quick Production Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd svn-checkin-monitor
   ```

2. **Create production configuration**
   ```bash
   cp config.example.json config.json
   # Edit config.json with production settings
   ```

3. **Create data directories**
   ```bash
   mkdir -p data/logs data/output
   chmod 755 data
   ```

4. **Deploy with Docker Compose**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

### Production Docker Compose

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  svn-monitor:
    build: 
      context: .
      dockerfile: Dockerfile
    container_name: svn-monitor-prod
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      - ./config.json:/app/config.json:ro
      - ./data:/app/data
      - /etc/localtime:/etc/localtime:ro
    environment:
      - PYTHONUNBUFFERED=1
      - SVN_MONITOR_ENV=production
    depends_on:
      - ollama
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  ollama:
    image: ollama/ollama:latest
    container_name: ollama-prod
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - svn-monitor
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

volumes:
  ollama_data:
    driver: local

networks:
  default:
    name: svn-monitor-network
```

### Nginx Configuration

Create `nginx.conf` for reverse proxy:

```nginx
events {
    worker_connections 1024;
}

http {
    upstream svn-monitor {
        server svn-monitor:5000;
    }

    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        client_max_body_size 16M;

        location / {
            proxy_pass http://svn-monitor;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        location /health {
            proxy_pass http://svn-monitor/health;
            access_log off;
        }
    }
}
```

### Production Configuration

**Security Settings**
```json
{
  "web": {
    "host": "0.0.0.0",
    "port": 5000,
    "secret_key": "generate-strong-random-key-here",
    "debug": false,
    "max_content_length": 16777216
  },
  "logging": {
    "level": "WARNING",
    "file": "/app/data/logs/svn_monitor.log",
    "max_size_mb": 50,
    "backup_count": 10
  }
}
```

**Performance Settings**
```json
{
  "monitoring": {
    "interval_seconds": 600,
    "max_retries": 5,
    "retry_delay": 120,
    "batch_size": 5,
    "parallel_processing": true
  },
  "ollama": {
    "timeout": 60,
    "max_tokens": 4000
  }
}
```

## Security Hardening

### Container Security

1. **Run as non-root user**
   ```dockerfile
   RUN adduser --disabled-password --gecos '' appuser
   USER appuser
   ```

2. **Use security options**
   ```yaml
   security_opt:
     - no-new-privileges:true
   read_only: true
   tmpfs:
     - /tmp
   ```

3. **Limit resources**
   ```yaml
   deploy:
     resources:
       limits:
         cpus: '1.0'
         memory: 1G
       reservations:
         cpus: '0.5'
         memory: 512M
   ```

### Network Security

1. **Use custom networks**
   ```yaml
   networks:
     svn-monitor-net:
       driver: bridge
       ipam:
         config:
           - subnet: **********/16
   ```

2. **Firewall configuration**
   ```bash
   # Allow only necessary ports
   ufw allow 22/tcp    # SSH
   ufw allow 80/tcp    # HTTP
   ufw allow 443/tcp   # HTTPS
   ufw enable
   ```

### Data Protection

1. **Encrypt sensitive data**
   ```bash
   # Use encrypted volumes
   docker volume create --driver local \
     --opt type=tmpfs \
     --opt device=tmpfs \
     --opt o=size=100m,uid=1000 \
     svn-monitor-secrets
   ```

2. **Secure file permissions**
   ```bash
   chmod 600 config.json
   chmod 700 data/
   chown -R 1000:1000 data/
   ```

## Monitoring and Observability

### Health Checks

Add health check endpoint to Flask app:

```python
@app.route('/health')
def health_check():
    return {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '2.0.0'
    }
```

### Logging Configuration

**Structured logging**
```json
{
  "logging": {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "handlers": {
      "file": {
        "filename": "/app/data/logs/svn_monitor.log",
        "max_size_mb": 50,
        "backup_count": 10
      },
      "console": {
        "enabled": true,
        "level": "WARNING"
      }
    }
  }
}
```

### Metrics Collection

**Prometheus metrics** (optional):
```python
from prometheus_client import Counter, Histogram, generate_latest

# Add metrics endpoints
@app.route('/metrics')
def metrics():
    return generate_latest()
```

## Backup and Recovery

### Configuration Backup

```bash
#!/bin/bash
# backup-config.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/svn-monitor"

mkdir -p $BACKUP_DIR
cp config.json $BACKUP_DIR/config_$DATE.json
tar -czf $BACKUP_DIR/data_$DATE.tar.gz data/

# Keep only last 30 days
find $BACKUP_DIR -name "*.json" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### Automated Backups

```yaml
# Add to docker-compose.prod.yml
  backup:
    image: alpine:latest
    volumes:
      - ./:/app
      - /backups:/backups
    command: |
      sh -c "
        apk add --no-cache tar gzip
        while true; do
          sleep 86400  # 24 hours
          /app/backup-config.sh
        done
      "
```

### Recovery Procedures

1. **Configuration recovery**
   ```bash
   # Stop services
   docker-compose down
   
   # Restore configuration
   cp /backups/svn-monitor/config_YYYYMMDD_HHMMSS.json config.json
   
   # Restore data
   tar -xzf /backups/svn-monitor/data_YYYYMMDD_HHMMSS.tar.gz
   
   # Restart services
   docker-compose up -d
   ```

## Performance Optimization

### Resource Allocation

**CPU and Memory**
```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 2G
    reservations:
      cpus: '1.0'
      memory: 1G
```

### Database Optimization

**Configuration tuning**
```json
{
  "monitoring": {
    "interval_seconds": 900,  # Reduce frequency for large repos
    "batch_size": 3,          # Process fewer repos simultaneously
    "parallel_processing": false  # Disable for resource-constrained systems
  }
}
```

### Caching Strategy

**File system caching**
```bash
# Mount with appropriate options
volumes:
  - type: bind
    source: ./data
    target: /app/data
    bind:
      propagation: cached
```

## Maintenance Procedures

### Regular Maintenance

**Daily tasks**
```bash
#!/bin/bash
# daily-maintenance.sh

# Check disk space
df -h /app/data

# Rotate logs
docker-compose exec svn-monitor logrotate /etc/logrotate.conf

# Check container health
docker-compose ps
```

**Weekly tasks**
```bash
#!/bin/bash
# weekly-maintenance.sh

# Update containers
docker-compose pull
docker-compose up -d

# Clean up old images
docker image prune -f

# Backup configuration
./backup-config.sh
```

### Updates and Upgrades

1. **Backup current state**
   ```bash
   ./backup-config.sh
   ```

2. **Pull latest changes**
   ```bash
   git pull origin main
   ```

3. **Update containers**
   ```bash
   docker-compose build --no-cache
   docker-compose up -d
   ```

4. **Verify functionality**
   ```bash
   curl -f http://localhost:5000/health
   ```

## Troubleshooting

### Common Issues

**Container won't start**
```bash
# Check logs
docker-compose logs svn-monitor

# Check configuration
docker-compose config

# Verify file permissions
ls -la config.json data/
```

**High resource usage**
```bash
# Monitor resource usage
docker stats

# Check application logs
tail -f data/logs/svn_monitor.log

# Adjust monitoring intervals
```

**Network connectivity issues**
```bash
# Test SVN connectivity
docker-compose exec svn-monitor svn info https://your-repo-url

# Test email connectivity
docker-compose exec svn-monitor telnet smtp.gmail.com 587
```

### Emergency Procedures

**Service recovery**
```bash
# Quick restart
docker-compose restart svn-monitor

# Full rebuild
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

**Data corruption recovery**
```bash
# Stop services
docker-compose down

# Restore from backup
tar -xzf /backups/svn-monitor/data_latest.tar.gz

# Restart with clean state
docker-compose up -d
```
