# SVN Monitor Development Setup for Windows with Docker Desktop
# Run this script in PowerShell as Administrator

Write-Host "=== SVN Monitor Development Setup for Windows ===" -ForegroundColor Green

# Check if Docker Desktop is running
Write-Host "Checking Docker Desktop status..." -ForegroundColor Yellow
try {
    docker version | Out-Null
    Write-Host "✓ Docker Desktop is running" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker Desktop is not running or not installed" -ForegroundColor Red
    Write-Host "Please start Docker Desktop and try again" -ForegroundColor Red
    exit 1
}

# Check if docker-compose is available
Write-Host "Checking Docker Compose..." -ForegroundColor Yellow
try {
    docker-compose version | Out-Null
    Write-Host "✓ Docker Compose is available" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker Compose is not available" -ForegroundColor Red
    exit 1
}

# Create necessary directories
Write-Host "Creating development directories..." -ForegroundColor Yellow
$directories = @("data", "data\logs", "data\output", "data\output\repositories")
foreach ($dir in $directories) {
    if (!(Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "✓ Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "✓ Directory already exists: $dir" -ForegroundColor Green
    }
}

# Create development configuration if it doesn't exist
Write-Host "Setting up development configuration..." -ForegroundColor Yellow
if (!(Test-Path "config.json")) {
    if (Test-Path "config.example.json") {
        Copy-Item "config.example.json" "config.json"
        Write-Host "✓ Created config.json from example" -ForegroundColor Green
        Write-Host "⚠ Please edit config.json with your development settings" -ForegroundColor Yellow
    } else {
        Write-Host "✗ config.example.json not found" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✓ config.json already exists" -ForegroundColor Green
}

# Set appropriate permissions for data directory
Write-Host "Setting directory permissions..." -ForegroundColor Yellow
try {
    icacls "data" /grant "Everyone:(OI)(CI)F" /T | Out-Null
    Write-Host "✓ Set permissions for data directory" -ForegroundColor Green
} catch {
    Write-Host "⚠ Could not set permissions (this is usually fine)" -ForegroundColor Yellow
}

# Check if existing Docker Compose is running
Write-Host "Checking existing Docker services..." -ForegroundColor Yellow
$existingServices = docker ps --format "table {{.Names}}" | Select-String "ollama-server-local"
if (-not $existingServices) {
    Write-Host "⚠ Warning: ollama-server-local not found. Make sure your main Docker Compose is running." -ForegroundColor Yellow
    Write-Host "Please start your main docker-compose.yml first, then run this script again." -ForegroundColor Yellow
    exit 1
}

Write-Host "✓ Found existing Ollama server, proceeding with SVN Monitor setup..." -ForegroundColor Green

# Build the SVN Monitor (no need to pull Ollama since it's already running)
Write-Host "Building SVN Monitor development environment..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml build

# Start the SVN Monitor service
Write-Host "Starting SVN Monitor service..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml up -d

# Wait for services to be ready
Write-Host "Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check service status
Write-Host "Checking service status..." -ForegroundColor Yellow
$services = docker-compose -f docker-compose.dev.yml ps --services
foreach ($service in $services) {
    $status = docker-compose -f docker-compose.dev.yml ps $service
    if ($status -match "Up") {
        Write-Host "✓ $service is running" -ForegroundColor Green
    } else {
        Write-Host "✗ $service is not running" -ForegroundColor Red
    }
}

# Download initial Ollama model
Write-Host "Setting up Ollama AI model..." -ForegroundColor Yellow
Write-Host "This may take a few minutes for the first time..." -ForegroundColor Yellow
try {
    docker-compose -f docker-compose.dev.yml exec -T ollama ollama pull llama2
    Write-Host "✓ Ollama model downloaded successfully" -ForegroundColor Green
} catch {
    Write-Host "⚠ Could not download Ollama model automatically" -ForegroundColor Yellow
    Write-Host "You can download it later with: docker-compose -f docker-compose.dev.yml exec ollama ollama pull llama2" -ForegroundColor Yellow
}

# Test web interface
Write-Host "Testing web interface..." -ForegroundColor Yellow
Start-Sleep -Seconds 5
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000" -TimeoutSec 10 -UseBasicParsing
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ Web interface is accessible at http://localhost:5000" -ForegroundColor Green
    }
} catch {
    Write-Host "⚠ Web interface may not be ready yet" -ForegroundColor Yellow
    Write-Host "Try accessing http://localhost:5000 in a few minutes" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=== Development Environment Setup Complete ===" -ForegroundColor Green
Write-Host ""
Write-Host "Services:" -ForegroundColor Cyan
Write-Host "  • SVN Monitor Web Interface: http://localhost:5000" -ForegroundColor White
Write-Host "  • Ollama API: http://localhost:11434" -ForegroundColor White
Write-Host ""
Write-Host "Useful Commands:" -ForegroundColor Cyan
Write-Host "  • View logs: docker-compose -f docker-compose.dev.yml logs -f" -ForegroundColor White
Write-Host "  • Stop services: docker-compose -f docker-compose.dev.yml down" -ForegroundColor White
Write-Host "  • Restart services: docker-compose -f docker-compose.dev.yml restart" -ForegroundColor White
Write-Host "  • Rebuild: docker-compose -f docker-compose.dev.yml build --no-cache" -ForegroundColor White
Write-Host ""
Write-Host "Next Steps:" -ForegroundColor Cyan
Write-Host "  1. Edit config.json with your SVN repository details" -ForegroundColor White
Write-Host "  2. Access http://localhost:5000 to configure users and repositories" -ForegroundColor White
Write-Host "  3. Test the monitoring functionality" -ForegroundColor White
Write-Host ""
Write-Host "For troubleshooting, check the logs with:" -ForegroundColor Yellow
Write-Host "  docker-compose -f docker-compose.dev.yml logs svn-monitor" -ForegroundColor White
