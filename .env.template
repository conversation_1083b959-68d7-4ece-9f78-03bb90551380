# RepoSense AI Environment Configuration Template
# Copy this to .env (for local overrides) or set in docker-compose.yml

# =============================================================================
# OLLAMA CONFIGURATION (Required)
# =============================================================================

# Ollama server URL - choose one:
# For Docker containers (recommended):
OLLAMA_BASE_URL=http://ollama:11434

# For external server access:
# OLLAMA_BASE_URL=http://************:11434

# Ollama model - use an available model:
# For code documentation (recommended):
OLLAMA_MODEL=codeqwen:7b-chat-v1.5-q8_0

# Alternative models:
# OLLAMA_MODEL=qwen2.5:14b
# OLLAMA_MODEL=llama3.1:latest
# OLLAMA_MODEL=deepseek-r1:8b

# =============================================================================
# WEB INTERFACE CONFIGURATION (Optional)
# =============================================================================

# Web interface host (usually 0.0.0.0 for Docker)
REPOSENSE_AI_WEB_HOST=0.0.0.0

# Web interface port
REPOSENSE_AI_WEB_PORT=5000

# =============================================================================
# APPLICATION CONFIGURATION (Optional)
# =============================================================================

# Environment type
REPOSENSE_AI_ENV=development

# Python settings
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# Flask settings (for development)
FLASK_ENV=development
FLASK_DEBUG=1

# Log level
REPOSENSE_AI_LOG_LEVEL=DEBUG

# =============================================================================
# NOTES
# =============================================================================

# Priority Order:
# 1. Environment Variables (this file) - HIGHEST PRIORITY
# 2. Configuration File (config.json)
# 3. Application Defaults - LOWEST PRIORITY

# Environment variables will ALWAYS override config file values.
# This is intentional for deployment flexibility.

# To check your effective configuration, run:
# python config_summary.py
