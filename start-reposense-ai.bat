@echo off
REM Quick Start Script for RepoSense AI
REM This script starts only the RepoSense AI, assuming your main Docker Compose is already running

echo RepoSense AI Quick Start
echo ========================

REM Check if main services are running
echo Checking existing Docker services...
docker ps --format "table {{.Names}}" | findstr "ollama-server-local" >nul
if errorlevel 1 (
    echo ERROR: ollama-server-local not found!
    echo Please start your main docker-compose.yml first:
    echo   docker-compose up -d
    pause
    exit /b 1
)

echo Found ollama-server-local

REM Handle command line arguments
if "%1"=="stop" goto stop
if "%1"=="status" goto status
if "%1"=="logs" goto start_with_logs
if "%1"=="build" goto build_and_start

REM Check if config exists
if not exist "config.json" (
    if exist "config.example.json" (
        echo Creating config.json from example...
        copy "config.example.json" "config.json"
        echo Created config.json - please edit with your settings
    ) else (
        echo ERROR: No configuration file found!
        echo Please create config.json or config.example.json
        pause
        exit /b 1
    )
)

goto start

:build_and_start
echo Building RepoSense AI...
docker-compose -f docker-compose.dev.yml build --no-cache
goto start

:start
echo Starting RepoSense AI...
docker-compose -f docker-compose.dev.yml up -d

echo Waiting for RepoSense AI to start...
timeout /t 10 /nobreak >nul

REM Check status
docker-compose -f docker-compose.dev.yml ps reposense-ai | findstr "Up" >nul
if errorlevel 1 (
    echo ERROR: RepoSense AI failed to start!
    echo Check logs with: docker-compose -f docker-compose.dev.yml logs reposense-ai
    pause
    exit /b 1
)

echo RepoSense AI started successfully!
echo.
echo Service URLs:
echo   RepoSense AI: http://localhost:5001
echo   Open WebUI:  http://localhost:3000
echo.
echo Commands:
echo   start-reposense-ai.bat stop    - Stop RepoSense AI
echo   start-reposense-ai.bat status  - Show status
echo   start-reposense-ai.bat logs    - Start with logs
echo   start-reposense-ai.bat build   - Rebuild and start
goto end

:stop
echo Stopping RepoSense AI...
docker-compose -f docker-compose.dev.yml down
echo RepoSense AI stopped
goto end

:status
echo RepoSense AI Status:
docker-compose -f docker-compose.dev.yml ps
echo.
echo Service URLs:
echo   RepoSense AI: http://localhost:5001
echo   Open WebUI:  http://localhost:3000
echo   LLM Proxy:   http://localhost:11440
echo   Ollama API:  http://localhost:11434
goto end

:start_with_logs
call :start
echo.
echo Showing logs (Ctrl+C to exit):
docker-compose -f docker-compose.dev.yml logs -f repository-monitor
goto end

:end
