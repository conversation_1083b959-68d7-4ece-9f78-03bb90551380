#!/usr/bin/env python3
"""
Ollama client for AI-powered content generation
Handles documentation and email content generation using Ollama API
"""

import logging
import requests
from typing import Tuple

from models import Config, CommitInfo


class OllamaClient:
    """Client for Ollama API interactions"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def call_ollama(self, prompt: str, system_prompt: str = "") -> str:
        """Call Ollama API to generate content"""
        try:
            url = f"{self.config.ollama_host}/api/generate"
            
            payload = {
                "model": self.config.ollama_model,
                "prompt": prompt,
                "system": system_prompt,
                "stream": False
            }
            
            response = requests.post(url, json=payload, timeout=120)
            response.raise_for_status()
            
            result = response.json()
            return result.get("response", "")
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error calling Ollama API: {e}")
            return ""
        except Exception as e:
            self.logger.error(f"Unexpected error calling Ollama: {e}")
            return ""
    
    def test_connection(self, timeout: int = 2) -> bool:
        """Test connection to Ollama with configurable timeout"""
        try:
            url = f"{self.config.ollama_host}/api/tags"
            response = requests.get(url, timeout=timeout)
            return response.status_code == 200
        except Exception as e:
            self.logger.debug(f"Ollama connection test failed: {e}")
            return False

    def get_available_models(self, timeout: int = 5) -> list:
        """Get list of available models from Ollama server"""
        try:
            url = f"{self.config.ollama_host}/api/tags"
            response = requests.get(url, timeout=timeout)
            if response.status_code == 200:
                data = response.json()
                models = []
                for model in data.get('models', []):
                    model_name = model.get('name', '')
                    if model_name:
                        models.append(model_name)
                return sorted(models)
            else:
                self.logger.warning(f"Failed to get models from Ollama: HTTP {response.status_code}")
                return []
        except Exception as e:
            self.logger.warning(f"Error getting models from Ollama: {e}")
            return []
    
    def generate_documentation(self, commit: CommitInfo) -> str:
        """Generate documentation for a commit using Ollama"""
        system_prompt = """You are a technical documentation generator. Your task is to create clear, 
        comprehensive documentation based on code changes. Focus on:
        1. What changes were made
        2. Why the changes were made (based on commit message)
        3. Impact on the codebase
        4. Any important technical details
        
        Write in markdown format with appropriate headers and formatting."""
        
        prompt = f"""
        Generate documentation for the following SVN commit:
        
        Revision: {commit.revision}
        Author: {commit.author}
        Date: {commit.date}
        Message: {commit.message}
        
        Changed files:
        {chr(10).join(commit.changed_paths)}
        
        Diff:
        {commit.diff}
        
        Please generate comprehensive documentation for this commit.
        """
        
        self.logger.info(f"Generating documentation for revision {commit.revision}")
        return self.call_ollama(prompt, system_prompt)
    
    def generate_email_content(self, commit: CommitInfo) -> Tuple[str, str]:
        """Generate email subject and body for a commit using Ollama"""
        system_prompt = """You are an email generator for code commit notifications. Create:
        1. A clear, concise subject line
        2. A professional email body that summarizes the changes
        
        The email should be informative but not overly technical. Focus on business impact 
        and key changes that stakeholders should know about."""
        
        prompt = f"""
        Generate an email notification for the following SVN commit:
        
        Revision: {commit.revision}
        Author: {commit.author}
        Date: {commit.date}
        Message: {commit.message}
        
        Changed files:
        {chr(10).join(commit.changed_paths)}
        
        Diff (summary):
        {commit.diff[:2000]}{'...' if len(commit.diff) > 2000 else ''}
        
        Please generate:
        1. EMAIL SUBJECT: [subject line]
        2. EMAIL BODY: [email content]
        
        Keep the subject line under 60 characters and the email body professional and concise.
        """
        
        self.logger.info(f"Generating email content for revision {commit.revision}")
        response = self.call_ollama(prompt, system_prompt)
        
        # Parse response to extract subject and body
        lines = response.split('\n')
        subject = "SVN Commit Notification"
        body = response
        
        for line in lines:
            if line.strip().startswith("EMAIL SUBJECT:"):
                subject = line.replace("EMAIL SUBJECT:", "").strip()
                break
        
        # Find email body section
        body_start = response.find("EMAIL BODY:")
        if body_start != -1:
            body = response[body_start + len("EMAIL BODY:"):].strip()
        
        return subject, body
