{% extends "base.html" %}

{% block title %}Documents - RepoSense AI{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Generated Documents</h1>
            <p class="page-subtitle">View and manage AI-generated documentation for repository commits</p>
        </div>
        <div>
            <button class="btn btn-outline-primary" onclick="refreshDocuments()">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center shadow-sm border-primary" style="border-width: 2px;">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ documents|length }}</h5>
                <p class="card-text text-muted">Total Documents</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center shadow-sm border-success" style="border-width: 2px;">
            <div class="card-body">
                <h5 class="card-title text-success">{{ stats|length }}</h5>
                <p class="card-text text-muted">Repositories</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center shadow-sm border-warning" style="border-width: 2px;">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ (documents|map(attribute='size')|sum / 1024)|round(1) }} KB</h5>
                <p class="card-text text-muted">Total Size</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center shadow-sm border-info" style="border-width: 2px;">
            <div class="card-body">
                <h5 class="card-title text-info">{% if documents %}{{ documents[0].date.strftime('%Y-%m-%d') }}{% else %}N/A{% endif %}</h5>
                <p class="card-text text-muted">Latest Document</p>
            </div>
        </div>
    </div>
</div>

<!-- Documents Table -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm border-primary" style="border-width: 2px;">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-file-alt me-2"></i> Documents</h5>
                <div>
                    <button class="btn btn-outline-light btn-sm" onclick="cleanupOrphanedDocuments()" title="Remove orphaned documents">
                        <i class="fas fa-broom"></i> Cleanup
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="deleteAllDocuments()" title="Delete ALL documents">
                        <i class="fas fa-trash"></i> Delete All
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="clearDocumentCache()" title="Clear document cache">
                        <i class="fas fa-trash-alt"></i> Clear Cache
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="refreshDocuments()" title="Refresh documents">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if documents %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Repository</th>
                                    <th>Revision</th>
                                    <th>Date</th>
                                    <th>Author</th>
                                    <th>Commit Message</th>
                                    <th>Code Review</th>
                                    <th>Docs Impact</th>
                                    <th>User Feedback</th>
                                    <th>Size</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for doc in documents %}
                                <tr>
                                    <td>
                                        <span class="badge bg-primary">{{ doc.repository_name }}</span>
                                    </td>
                                    <td>
                                        <strong>{{ doc.revision }}</strong>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ doc.date.strftime('%Y-%m-%d %H:%M') }}</small>
                                    </td>
                                    <td>{{ doc.author }}</td>
                                    <td>
                                        <span class="text-truncate d-inline-block" style="max-width: 300px;" title="{{ doc.commit_message }}">
                                            {{ doc.commit_message }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if doc.code_review_recommended is not none %}
                                            {% if doc.code_review_recommended %}
                                                <span class="badge bg-warning text-dark" title="Code review recommended">
                                                    <i class="fas fa-eye"></i>
                                                    {% if doc.code_review_priority %}{{ doc.code_review_priority }}{% else %}YES{% endif %}
                                                </span>
                                            {% else %}
                                                <span class="badge bg-success" title="No code review needed">
                                                    <i class="fas fa-check"></i> NO
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-secondary" title="Not analyzed">
                                                <i class="fas fa-question"></i> N/A
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if doc.documentation_impact is not none %}
                                            {% if doc.documentation_impact %}
                                                <span class="badge bg-info" title="Documentation updates needed">
                                                    <i class="fas fa-file-alt"></i> YES
                                                </span>
                                            {% else %}
                                                <span class="badge bg-success" title="No documentation impact">
                                                    <i class="fas fa-check"></i> NO
                                                </span>
                                            {% endif %}
                                        {% else %}
                                            <span class="badge bg-secondary" title="Not analyzed">
                                                <i class="fas fa-question"></i> N/A
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <!-- User Feedback Indicators -->
                                        <div class="d-flex gap-1">
                                            {% if doc.user_code_review_status %}
                                                <span class="badge bg-{{ 'success' if doc.user_code_review_status == 'approved' else 'warning' if doc.user_code_review_status == 'needs_changes' else 'danger' if doc.user_code_review_status == 'rejected' else 'info' }}"
                                                      title="Code Review: {{ doc.user_code_review_status|title }}">
                                                    <i class="fas fa-code-branch"></i>
                                                </span>
                                            {% endif %}
                                            {% if doc.user_documentation_rating %}
                                                <span class="badge bg-{{ 'success' if doc.user_documentation_rating >= 4 else 'warning' if doc.user_documentation_rating >= 3 else 'danger' }}"
                                                      title="Documentation Rating: {{ doc.user_documentation_rating }}/5">
                                                    <i class="fas fa-star"></i> {{ doc.user_documentation_rating }}
                                                </span>
                                            {% endif %}
                                            {% if doc.user_risk_assessment_override %}
                                                <span class="badge bg-{{ 'danger' if doc.user_risk_assessment_override == 'HIGH' else 'warning' if doc.user_risk_assessment_override == 'MEDIUM' else 'success' }}"
                                                      title="Risk Override: {{ doc.user_risk_assessment_override }}">
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                </span>
                                            {% endif %}
                                            {% if not doc.user_code_review_status and not doc.user_documentation_rating and not doc.user_risk_assessment_override %}
                                                <small class="text-muted">No feedback</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ (doc.size / 1024)|round(1) }} KB</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <div class="dropdown">
                                                <button class="btn btn-outline-primary dropdown-toggle" type="button"
                                                        data-bs-toggle="dropdown" aria-expanded="false" title="View Document">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ url_for('view_document', doc_id=doc.id) }}">
                                                            <i class="fas fa-file-alt me-2"></i>Document Only
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li><h6 class="dropdown-header">Document + Diff</h6></li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ url_for('view_document', doc_id=doc.id, include_diff='true', diff_format='unified') }}">
                                                            <i class="fas fa-code me-2"></i>Unified Diff
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ url_for('view_document', doc_id=doc.id, include_diff='true', diff_format='side-by-side') }}">
                                                            <i class="fas fa-columns me-2"></i>Side-by-Side Diff
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                            <button class="btn btn-outline-info"
                                                    onclick="toggleDiff('{{ doc.id }}')"
                                                    title="View Diff">
                                                <i class="fas fa-code"></i>
                                            </button>
                                            <button class="btn btn-outline-danger"
                                                    onclick="deleteDocument('{{ doc.id }}', '{{ doc.display_name }}')"
                                                    title="Delete Document">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <!-- Diff content row (initially hidden) -->
                                <tr id="diff-row-{{ doc.id }}" class="diff-row" style="display: none;">
                                    <td colspan="8" class="p-0">
                                        <div class="bg-light border-top">
                                            <div class="p-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6 class="mb-0 text-muted">
                                                        <i class="fas fa-code"></i> Code Changes (Diff)
                                                    </h6>
                                                    <div class="d-flex align-items-center gap-2">
                                                        <div class="btn-group btn-group-sm" role="group">
                                                            <input type="radio" class="btn-check" name="diffFormat{{ doc.id }}" id="unified{{ doc.id }}" value="unified" checked>
                                                            <label class="btn btn-outline-secondary" for="unified{{ doc.id }}" onclick="changeDiffFormat('{{ doc.id }}', 'unified')">Unified</label>

                                                            <input type="radio" class="btn-check" name="diffFormat{{ doc.id }}" id="sideBySide{{ doc.id }}" value="side-by-side">
                                                            <label class="btn btn-outline-secondary" for="sideBySide{{ doc.id }}" onclick="changeDiffFormat('{{ doc.id }}', 'side-by-side')">Side-by-Side</label>
                                                        </div>
                                                        <button class="btn btn-sm btn-outline-secondary"
                                                                onclick="toggleDiff('{{ doc.id }}')" title="Hide Diff">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                                <div id="diff-content-{{ doc.id }}" class="diff-content">
                                                    <div class="text-center text-muted py-3">
                                                        <i class="fas fa-spinner fa-spin"></i> Loading diff...
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Documents Found</h5>
                        <p class="text-muted">Documents will appear here after commits are processed and analyzed by the AI.</p>
                        <a href="{{ url_for('index') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteDocumentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the document:</p>
                <p><strong id="deleteDocumentName"></strong></p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    This action cannot be undone.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteDocumentForm" method="post" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Document
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshDocuments() {
    location.reload();
}

function cleanupOrphanedDocuments() {
    if (confirm('This will remove all documents that belong to repositories that no longer exist. Continue?')) {
        fetch('/api/documents/cleanup-orphaned', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Success: ${data.message}`);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while cleaning up orphaned documents.');
        });
    }
}

function clearDocumentCache() {
    if (confirm('This will clear the document cache and force a refresh from the database. Continue?')) {
        fetch('/api/documents/clear-cache', {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`Success: ${data.message}`);
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while clearing the cache.');
        });
    }
}

function deleteAllDocuments() {
    if (confirm('⚠️ WARNING: This will permanently delete ALL documents (database records AND physical files). This action cannot be undone. Are you sure?')) {
        if (confirm('This is your final confirmation. Delete ALL documents?')) {
            fetch('/api/documents/delete-all', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(`Success: ${data.message}`);
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting documents.');
            });
        }
    }
}

function deleteDocument(docId, docName) {
    document.getElementById('deleteDocumentName').textContent = docName;
    document.getElementById('deleteDocumentForm').action = '/api/documents/' + docId + '/delete';
    
    new bootstrap.Modal(document.getElementById('deleteDocumentModal')).show();
}

// Handle delete form submission
document.getElementById('deleteDocumentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const form = e.target;
    const formData = new FormData(form);
    
    fetch(form.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal and refresh page
            bootstrap.Modal.getInstance(document.getElementById('deleteDocumentModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the document.');
    });
});

// Auto-refresh every 30 seconds
setInterval(refreshDocuments, 30000);

// Diff toggle functionality
function toggleDiff(docId) {
    const diffRow = document.getElementById(`diff-row-${docId}`);
    const diffContent = document.getElementById(`diff-content-${docId}`);

    if (diffRow.style.display === 'none') {
        // Show diff row
        diffRow.style.display = '';

        // Load diff content if not already loaded
        if (diffContent.innerHTML.includes('Loading diff...')) {
            loadDiffContent(docId, 'unified');
        }
    } else {
        // Hide diff row
        diffRow.style.display = 'none';
    }
}

// Load diff content with specified format
function loadDiffContent(docId, format) {
    const diffContent = document.getElementById(`diff-content-${docId}`);

    // Show loading indicator
    diffContent.innerHTML = `
        <div class="text-center text-muted py-3">
            <i class="fas fa-spinner fa-spin"></i> Loading ${format} diff...
        </div>
    `;

    fetch(`/api/documents/${docId}/diff?format=${format}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                diffContent.innerHTML = `
                    <div class="alert alert-warning mb-0">
                        <i class="fas fa-exclamation-triangle"></i> ${data.error}
                    </div>
                `;
            } else {
                if (data.format === 'side-by-side') {
                    // Side-by-side format is already HTML
                    diffContent.innerHTML = `
                        <div style="max-height: 500px; overflow-y: auto;">
                            ${data.diff}
                        </div>
                    `;
                } else {
                    // Unified format needs to be wrapped in pre/code
                    diffContent.innerHTML = `
                        <pre class="bg-dark text-light p-3 rounded mb-0" style="max-height: 400px; overflow-y: auto;"><code>${escapeHtml(data.diff)}</code></pre>
                    `;
                }
            }
        })
        .catch(error => {
            console.error('Error loading diff:', error);
            diffContent.innerHTML = `
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-exclamation-circle"></i> Error loading diff content
                </div>
            `;
        });
}

// Change diff format
function changeDiffFormat(docId, format) {
    loadDiffContent(docId, format);
}

// Helper function to escape HTML
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}
</script>
{% endblock %}
