services:
  # RepoSense AI - Integrated with existing Ollama infrastructure
  reposense-ai:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: reposense-ai:dev
    container_name: reposense-ai-dev
    restart: unless-stopped
    ports:
      - "5001:5000"  # RepoSense AI web interface (changed to avoid port conflict)
    volumes:
      # Configuration file (read-write for development)
      - ./config.json:/app/config.dev.json
      # Data persistence for development
      - ./data:/app/data
      # Logs directory
      - ./logs:/app/logs
      # Mount entire source code for hot reload (much more maintainable)
      - .:/app
      # Exclude node_modules and other unnecessary directories
      - /app/node_modules
      - /app/.git
      - /app/__pycache__
      - /app/.pytest_cache
      # Mount SVN credentials for diff generation
      - ${APPDATA}/Subversion:/host-svn-credentials:ro
    env_file:
      - .env.dev
    environment:
      # Override any specific environment variables if needed
      - OLLAMA_BASE_URL=http://ollama-server-local:11434
    networks:
      - reposense-ai-network  # Use rebranded network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "sundc:***********"
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

# Use rebranded network
networks:
  reposense-ai-network:
    external: true  # Reference existing network

# No additional volumes needed - using existing ollama-data volume
