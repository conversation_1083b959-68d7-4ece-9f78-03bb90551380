# RepoSense AI - Binary Deployment Guide

This guide covers deploying RepoSense AI as optimized binaries in Docker environments for production use.

## Overview

RepoSense AI supports multiple deployment strategies:

1. **Binary Docker Deployment** - Optimized single binary in minimal container
2. **Multi-stage Docker Build** - Source-to-binary build with minimal runtime
3. **Standalone Binary** - Direct binary execution on host systems
4. **Hybrid Deployment** - Binary with external services (Ollama, databases)

## Quick Start

### 1. Build Binary Distribution

```bash
# Build standalone binary and create distribution package
python build-binary.py

# Or use Makefile
make build-binary
```

### 2. Deploy with Docker

```bash
# Production deployment with optimized binary
docker-compose -f docker-compose.production.yml up -d

# Or use Makefile
make deploy-prod
```

### 3. Access Application

Open your browser and navigate to: http://localhost:5000

## Binary Building Process

### Automated Build Script

The `build-binary.py` script creates optimized binaries:

```bash
python build-binary.py
```

**What it does:**
- Installs PyInstaller and dependencies
- Creates single-file binary with all resources
- Includes templates, static files, and documentation
- Handles platform-specific optimizations
- Creates complete distribution package

### Manual PyInstaller Build

For custom builds:

```bash
pyinstaller --onefile \
    --name reposense-ai \
    --add-data "templates:templates" \
    --add-data "static:static" \
    --add-data "docs:docs" \
    --add-data "marketing:marketing" \
    --hidden-import=repository_backends.svn_backend \
    --hidden-import=repository_backends.git_backend \
    --collect-all=flask \
    --collect-all=jinja2 \
    reposense_ai_app.py
```

## Docker Deployment Options

### Option 1: Production Multi-Stage Build

Uses `Dockerfile.production` for optimized deployment:

```bash
# Build and deploy
docker-compose -f docker-compose.production.yml up -d
```

**Features:**
- Multi-stage build (builder + runtime)
- Minimal Ubuntu runtime image
- Non-root user for security
- Health checks and resource limits
- Persistent data volumes

### Option 2: Binary-Only Deployment

Deploy pre-built binary in minimal container:

```bash
# Build binary first
python build-binary.py

# Deploy binary package
cd dist/reposense-ai-package
docker build -t reposense-ai:binary .
docker run -d -p 5000:5000 reposense-ai:binary
```

### Option 3: Development Deployment

For development and testing:

```bash
docker-compose -f docker-compose.dev.yml up -d
```

## Configuration Management

### Environment Variables

Key environment variables for binary deployment:

```bash
# Application configuration
REPOSENSE_AI_CONFIG=/app/data/config.json
REPOSENSE_AI_DATA_DIR=/app/data
REPOSENSE_AI_LOG_DIR=/app/logs

# Flask configuration
FLASK_ENV=production
FLASK_DEBUG=false

# Security settings
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1
```

### Volume Mounts

Essential volumes for persistent data:

```yaml
volumes:
  # Application data (databases, cache)
  - repository_data:/app/data
  
  # Application logs
  - repository_logs:/app/logs
  
  # Configuration override (optional)
  - ./config.production.json:/app/data/config.json:ro
  
  # SVN credentials (if needed)
  - ~/.subversion:/home/<USER>/.subversion:ro
```

### Configuration Files

**Production Configuration (`config.production.json`):**
```json
{
  "repositories": [
    {
      "name": "Production Repo",
      "url": "https://svn.company.com/repo",
      "username": "monitor",
      "password": "secure_password"
    }
  ],
  "ollama": {
    "base_url": "http://ollama:11434",
    "model": "llama2"
  },
  "web": {
    "host": "0.0.0.0",
    "port": 5000,
    "debug": false
  },
  "security": {
    "secret_key": "production_secret_key_here"
  }
}
```

## Production Deployment

### Complete Production Stack

```yaml
# docker-compose.production.yml
version: '3.8'

services:
  reposense-ai:
    build:
      context: .
      dockerfile: Dockerfile.production
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      - repository_data:/app/data
      - repository_logs:/app/logs
      - ./config.production.json:/app/data/config.json:ro
    environment:
      - FLASK_ENV=production
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
    networks:
      - app-network

  ollama:
    image: ollama/ollama:latest
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - repository-monitor
    networks:
      - app-network

volumes:
  repository_data:
  repository_logs:
  ollama_data:

networks:
  app-network:
    driver: bridge
```

### Nginx Configuration

```nginx
# nginx.conf
events {
    worker_connections 1024;
}

http {
    upstream reposense_ai {
        server repository-monitor:5000;
    }

    server {
        listen 80;
        server_name your-domain.com;
        
        location / {
            proxy_pass http://reposense_ai;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
```

## Security Considerations

### Container Security

1. **Non-root User**: Binary runs as non-root user
2. **Resource Limits**: CPU and memory limits configured
3. **Read-only Filesystem**: Configuration mounted read-only
4. **Network Isolation**: Services in isolated network

### Data Security

1. **Volume Encryption**: Use encrypted Docker volumes
2. **Secrets Management**: Use Docker secrets for sensitive data
3. **Network Security**: Configure firewall rules
4. **Access Control**: Implement authentication and authorization

### Example Secure Configuration

```bash
# Create encrypted volume
docker volume create --driver local \
    --opt type=tmpfs \
    --opt device=tmpfs \
    --opt o=size=1g,uid=1000 \
    repository_secure_data

# Run with security options
docker run -d \
    --name repository-monitor \
    --user 1000:1000 \
    --read-only \
    --tmpfs /tmp \
    --security-opt no-new-privileges \
    -v repository_secure_data:/app/data \
    repository-monitor:latest
```

## Monitoring and Maintenance

### Health Checks

Built-in health check endpoint:

```bash
# Check application health
curl -f http://localhost:5000/health

# Docker health check
docker ps  # Shows health status
```

### Log Management

```bash
# View logs
docker-compose logs -f repository-monitor

# Log rotation configuration
docker run --log-driver json-file \
    --log-opt max-size=10m \
    --log-opt max-file=3 \
    repository-monitor:latest
```

### Backup and Restore

```bash
# Backup data
docker run --rm \
    -v repository_data:/data \
    -v $(pwd)/backups:/backup \
    ubuntu tar czf /backup/backup-$(date +%Y%m%d).tar.gz -C /data .

# Restore data
docker run --rm \
    -v repository_data:/data \
    -v $(pwd)/backups:/backup \
    ubuntu tar xzf /backup/backup-20240101.tar.gz -C /data
```

### Performance Monitoring

```bash
# Monitor resource usage
docker stats repository-monitor

# Monitor application metrics
curl http://localhost:5000/metrics
```

## Troubleshooting

### Common Issues

**Binary not starting:**
```bash
# Check permissions
ls -la dist/repository-monitor
chmod +x dist/repository-monitor

# Check dependencies
ldd dist/repository-monitor
```

**Docker build failures:**
```bash
# Clean build cache
docker builder prune

# Rebuild without cache
docker build --no-cache -f Dockerfile.production .
```

**Port conflicts:**
```bash
# Check port usage
netstat -tulpn | grep 5000

# Use different port
docker run -p 8080:5000 repository-monitor:latest
```

### Debug Mode

Enable debug mode for troubleshooting:

```bash
# Set debug environment
export FLASK_DEBUG=true
export REPOSENSE_AI_LOG_LEVEL=DEBUG

# Run with debug
./repository-monitor
```

## Performance Optimization

### Binary Optimization

1. **UPX Compression**: Compress binary size
2. **Strip Symbols**: Remove debug symbols
3. **Optimize Imports**: Minimize included modules

### Container Optimization

1. **Multi-stage Builds**: Minimize image size
2. **Alpine Base**: Use minimal base images
3. **Layer Caching**: Optimize Dockerfile layers
4. **Resource Limits**: Set appropriate limits

### Example Optimized Build

```dockerfile
# Optimized production build
FROM python:3.11-alpine as builder
RUN apk add --no-cache gcc musl-dev
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
RUN pip install pyinstaller
COPY . .
RUN pyinstaller --onefile --strip reposense_ai_app.py

FROM alpine:latest
RUN apk add --no-cache libstdc++
COPY --from=builder /app/dist/reposense_ai_app /app/
EXPOSE 5000
CMD ["/app/reposense_ai_app"]
```

This comprehensive binary deployment strategy provides production-ready, secure, and optimized deployment options for RepoSense AI.
