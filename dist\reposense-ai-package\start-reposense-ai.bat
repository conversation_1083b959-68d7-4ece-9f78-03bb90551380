@echo off
REM RepoSense AI Startup Script

cd /d "%~dp0"

REM Create data directory if it doesn't exist
if not exist data mkdir data
if not exist logs mkdir logs

REM Initialize configuration if not exists
if not exist data\config.json (
    echo Initializing configuration...
    copy config.example.json data\config.json
    echo Configuration initialized. Please customize data\config.json
    pause
)

REM Set environment variables
set REPOSENSE_AI_CONFIG=%~dp0data\config.json
set REPOSENSE_AI_DATA_DIR=%~dp0data
set REPOSENSE_AI_LOG_DIR=%~dp0logs

REM Start RepoSense AI
echo Starting RepoSense AI...
echo Access the web interface at: http://localhost:5000
echo Press Ctrl+C to stop

reposense-ai.exe
pause
