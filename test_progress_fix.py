#!/usr/bin/env python3
"""
Test script to verify the progress calculation fix for revision ranges
"""

import requests
import json
import time

def test_progress_calculation():
    """Test progress calculation for revision ranges that don't start from 1"""
    
    base_url = "http://localhost:5001"
    repo_id = "e65f7062-d497-4a3d-aca5-56ddf1964361"  # Test repository ID
    
    print("🧪 Testing Progress Calculation Fix")
    print("=" * 50)
    
    # Test 1: Start a scan with a specific revision range (e.g., 70-75)
    print("\n1. Starting historical scan with revision range 70-75:")
    try:
        # Configure the scan
        config_data = {
            'scan_method': 'revision',
            'start_revision': '70',
            'end_revision': '75',
            'generate_documentation': 'on',
            'batch_size': '1'
        }
        
        response = requests.post(f"{base_url}/repositories/{repo_id}/historical-scan/configure", 
                               data=config_data)
        if response.status_code == 200:
            print(f"   ✅ Configuration saved: {response.status_code}")
        else:
            print(f"   ❌ Configuration failed: {response.status_code} - {response.text}")
            return
        
        # Start the scan
        response = requests.post(f"{base_url}/repositories/{repo_id}/historical-scan/start", 
                               data=config_data)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Scan started: {result.get('message', 'No message')}")
        else:
            print(f"   ❌ Scan start failed: {response.status_code} - {response.text}")
            return
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return
    
    # Test 2: Monitor progress for correct calculation
    print("\n2. Monitoring progress calculation:")
    try:
        for i in range(20):  # Monitor for up to 40 seconds
            response = requests.get(f"{base_url}/api/historical-scan/progress")
            if response.status_code == 200:
                data = response.json()
                repo_progress = data.get('progress', {}).get(repo_id)
                
                if repo_progress:
                    status = repo_progress.get('status', 'unknown')
                    total = repo_progress.get('total_revisions', 0)
                    processed = repo_progress.get('processed_revisions', 0)
                    current = repo_progress.get('current_revision', 'N/A')
                    
                    # Calculate expected percentage
                    if total > 0:
                        expected_percent = round((processed / total) * 100, 1)
                        
                        print(f"   📊 Status: {status}")
                        print(f"   📊 Progress: {processed}/{total} ({expected_percent}%)")
                        print(f"   📊 Current Revision: {current}")
                        
                        # Check if progress makes sense
                        if processed <= total and expected_percent <= 100:
                            print(f"   ✅ Progress calculation looks correct!")
                        else:
                            print(f"   ❌ Progress calculation issue: {processed}/{total} = {expected_percent}%")
                        
                        if status in ['completed', 'failed', 'cancelled']:
                            print(f"   🏁 Scan finished with status: {status}")
                            break
                    else:
                        print(f"   ⏳ Waiting for scan to start...")
                else:
                    print(f"   ⏳ No progress data yet...")
            else:
                print(f"   ❌ Progress API error: {response.status_code}")
            
            time.sleep(2)  # Wait 2 seconds between checks
            print("   " + "-" * 40)
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Progress Calculation Test Complete!")

if __name__ == "__main__":
    test_progress_calculation()
