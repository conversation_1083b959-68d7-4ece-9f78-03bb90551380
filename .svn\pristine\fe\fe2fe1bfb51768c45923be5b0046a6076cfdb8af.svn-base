#!/usr/bin/env python3
"""
Repository backend plugin system
Manages loading and accessing different repository backends (SVN, Git, etc.)
"""

import logging
from typing import Dict, Type, Optional, List
from .base import RepositoryBackend, RepositoryInfo
from models import RepositoryConfig


class RepositoryBackendManager:
    """Manager for repository backend plugins"""
    
    def __init__(self):
        self.backends: Dict[str, Type[RepositoryBackend]] = {}
        self.logger = logging.getLogger(__name__)
        self._load_backends()
    
    def _load_backends(self):
        """Load all available repository backends"""
        try:
            # Import SVN backend
            from .svn_backend import SVNBackend
            self.register_backend(SVNBackend)
            self.logger.info("Loaded SVN backend")
        except ImportError as e:
            self.logger.warning(f"Could not load SVN backend: {e}")
        
        try:
            # Import Git backend (when implemented)
            from .git_backend import GitBackend
            self.register_backend(GitBackend)
            self.logger.info("Loaded Git backend")
        except ImportError:
            self.logger.debug("Git backend not available")
    
    def register_backend(self, backend_class: Type[RepositoryBackend]):
        """
        Register a repository backend
        
        Args:
            backend_class: Backend class to register
        """
        # Create a temporary instance to get the backend type
        temp_instance = backend_class(None)
        backend_type = temp_instance.backend_type
        self.backends[backend_type] = backend_class
        self.logger.debug(f"Registered backend: {backend_type}")
    
    def get_backend(self, backend_type: str, config) -> Optional[RepositoryBackend]:
        """
        Get a backend instance by type
        
        Args:
            backend_type: Type of backend ('svn', 'git', etc.)
            config: Configuration object to pass to backend
            
        Returns:
            Backend instance or None if not found
        """
        if backend_type not in self.backends:
            self.logger.error(f"Backend type '{backend_type}' not found")
            return None
        
        try:
            return self.backends[backend_type](config)
        except Exception as e:
            self.logger.error(f"Error creating backend '{backend_type}': {e}")
            return None
    
    def get_backend_for_repository(self, repo: RepositoryConfig, config) -> Optional[RepositoryBackend]:
        """
        Get the appropriate backend for a repository based on its URL or configuration
        
        Args:
            repo: Repository configuration
            config: Application configuration
            
        Returns:
            Backend instance or None if no suitable backend found
        """
        # For now, determine backend type from URL
        backend_type = self._detect_repository_type(repo.url)
        
        if not backend_type:
            self.logger.error(f"Could not determine repository type for {repo.url}")
            return None
        
        return self.get_backend(backend_type, config)
    
    def _detect_repository_type(self, url: str) -> Optional[str]:
        """
        Detect repository type from URL
        
        Args:
            url: Repository URL
            
        Returns:
            Backend type string or None
        """
        url_lower = url.lower()
        
        # SVN detection
        if 'svn' in url_lower or url_lower.startswith('http') or url_lower.startswith('https'):
            # For now, assume HTTP(S) URLs are SVN unless we have better detection
            return 'svn'
        
        # Git detection
        if url_lower.endswith('.git') or 'github.com' in url_lower or 'gitlab.com' in url_lower:
            return 'git'
        
        # Default to SVN for backward compatibility
        return 'svn'
    
    def get_available_backends(self) -> List[str]:
        """
        Get list of available backend types
        
        Returns:
            List of backend type strings
        """
        return list(self.backends.keys())
    
    def discover_repositories(self, backend_type: str, base_url: str, config,
                            username: Optional[str] = None, password: Optional[str] = None,
                            max_depth: int = 3) -> List[RepositoryInfo]:
        """
        Discover repositories using a specific backend
        
        Args:
            backend_type: Type of backend to use
            base_url: Base URL to search
            config: Application configuration
            username: Optional username
            password: Optional password
            max_depth: Maximum search depth
            
        Returns:
            List of discovered repositories
        """
        backend = self.get_backend(backend_type, config)
        if not backend:
            return []
        
        if not backend.supports_discovery():
            self.logger.warning(f"Backend '{backend_type}' does not support discovery")
            return []
        
        try:
            return backend.discover_repositories(base_url, username, password, max_depth)
        except Exception as e:
            self.logger.error(f"Error discovering repositories with {backend_type}: {e}")
            return []


# Global instance
backend_manager = RepositoryBackendManager()


def get_backend_manager() -> RepositoryBackendManager:
    """Get the global backend manager instance"""
    return backend_manager
