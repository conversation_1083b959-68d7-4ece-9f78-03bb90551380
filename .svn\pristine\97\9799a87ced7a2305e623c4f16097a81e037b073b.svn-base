#!/usr/bin/env python3
"""
Data models for RepoSense AI application
Contains dataclasses for configuration and commit information
"""

import secrets
from dataclasses import dataclass, field
from typing import Dict, List, Optional
from datetime import datetime
import uuid
from enum import Enum


class UserRole(Enum):
    """User roles for access control and notification preferences"""
    ADMIN = "admin"
    MANAGER = "manager"
    DEVELOPER = "developer"
    VIEWER = "viewer"


class HistoricalScanStatus(Enum):
    """Historical scan status enumeration"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class HistoricalScanConfig:
    """Configuration for historical scanning of a repository"""
    enabled: bool = False

    # Revision range scanning
    scan_by_revision: bool = True
    start_revision: Optional[int] = None
    end_revision: Optional[int] = None

    # Date range scanning
    scan_by_date: bool = False
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

    # Scanning preferences
    batch_size: int = 10  # Number of revisions to process in each batch
    include_merge_commits: bool = True
    skip_large_commits: bool = False  # Skip commits with >100 files changed
    max_files_per_commit: int = 100

    # Progress tracking
    last_scanned_revision: Optional[int] = None
    scan_status: HistoricalScanStatus = HistoricalScanStatus.NOT_STARTED
    scan_started_at: Optional[datetime] = None
    scan_completed_at: Optional[datetime] = None
    total_revisions: Optional[int] = None
    processed_revisions: int = 0
    failed_revisions: int = 0
    error_message: Optional[str] = None

    # Analysis preferences
    generate_documentation: bool = True
    analyze_code_review: bool = True
    analyze_documentation_impact: bool = True


@dataclass
class User:
    """User configuration with details and preferences"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    username: str = ""
    email: str = ""
    full_name: str = ""
    role: UserRole = UserRole.DEVELOPER
    enabled: bool = True

    # Notification preferences
    receive_all_notifications: bool = False  # If True, receives notifications for all repositories
    repository_subscriptions: List[str] = field(default_factory=list)  # Repository IDs user is subscribed to

    # Contact details
    phone: Optional[str] = None
    department: Optional[str] = None

    # Metadata
    created_date: Optional[str] = None
    last_modified: Optional[str] = None

    def __post_init__(self):
        if not self.username and self.email:
            # Generate username from email if not provided
            self.username = self.email.split('@')[0]


@dataclass
class RepositoryConfig:
    """Configuration for a single repository"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    url: str = ""
    username: Optional[str] = None
    password: Optional[str] = None
    last_revision: int = 0
    last_commit_date: Optional[datetime] = None  # When the revision was committed to source control
    last_processed_time: Optional[datetime] = None  # When RepoSense AI processed the revision
    enabled: bool = True

    # Branch/path selection
    branch_path: Optional[str] = None  # Specific branch path to monitor (e.g., "trunk", "branches/feature-x")
    monitor_all_branches: bool = False  # If True, monitor all branches; if False, monitor only branch_path

    # User associations
    assigned_users: List[str] = field(default_factory=list)  # User IDs assigned to this repository

    # Legacy email support (for backward compatibility)
    email_recipients: List[str] = field(default_factory=list)  # Direct email addresses

    # Historical scanning configuration
    historical_scan: HistoricalScanConfig = field(default_factory=HistoricalScanConfig)

    # Product documentation configuration
    product_documentation_files: List[str] = field(default_factory=list)  # Specific files to reference for product documentation context

    def __post_init__(self):
        if not self.name and self.url:
            # Generate a default name from URL
            self.name = self.url.split('/')[-1] or self.url.split('/')[-2]


@dataclass
class CommitInfo:
    """Data class for repository commit information"""
    revision: str
    author: str
    date: str
    message: str
    changed_paths: List[str]
    diff: str
    repository_id: str = ""
    repository_name: str = ""


@dataclass
class Config:
    """Configuration data class"""
    # User management
    users: List[User] = field(default_factory=list)

    # Repository configurations
    repositories: List[RepositoryConfig] = field(default_factory=list)

    # AI and monitoring settings
    ollama_host: str = "http://ollama:11434"
    ollama_model: str = "llama2"  # Default model for technical analysis

    # Specialized AI models for different tasks
    ollama_model_documentation: Optional[str] = None  # Model for product documentation analysis
    ollama_model_code_review: Optional[str] = None    # Model for code review analysis
    ollama_model_risk_assessment: Optional[str] = None  # Model for risk assessment

    check_interval: int = 300  # seconds

    # SVN server settings (for repository discovery)
    svn_server_url: Optional[str] = None
    svn_server_username: Optional[str] = None
    svn_server_password: Optional[str] = None
    svn_server_type: str = "auto"  # auto, visualsvn, apache, apache_dav, standard

    # Email settings
    smtp_host: str = "localhost"
    smtp_port: int = 587
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    email_from: str = "<EMAIL>"
    email_recipients: List[str] = field(default_factory=list)  # Global recipients (always get emails)

    def get_all_recipients_for_repository(self, repo_id: str) -> List[str]:
        """Get all email recipients for a specific repository (users + legacy emails)"""
        all_recipients = set()

        # Add global recipients (legacy email list)
        all_recipients.update(self.email_recipients)

        # Add users who receive all notifications
        for user in self.users:
            if user.enabled and user.receive_all_notifications:
                all_recipients.add(user.email)

        # Add repository-specific users
        repo = self.get_repository_by_id(repo_id)
        if repo:
            # Add legacy email recipients
            all_recipients.update(repo.email_recipients)

            # Add assigned users
            for user_id in repo.assigned_users:
                user = self.get_user_by_id(user_id)
                if user is not None and user.enabled:
                    all_recipients.add(user.email)

            # Add users subscribed to this repository
            for user in self.users:
                if user.enabled and repo_id in user.repository_subscriptions:
                    all_recipients.add(user.email)

        return list(all_recipients)

    # User management methods
    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        for user in self.users:
            if user.id == user_id:
                return user
        return None

    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email address"""
        for user in self.users:
            if user.email.lower() == email.lower():
                return user
        return None

    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        for user in self.users:
            if user.username.lower() == username.lower():
                return user
        return None

    def add_user(self, user: User) -> bool:
        """Add a new user if email/username doesn't exist"""
        if self.get_user_by_email(user.email) or self.get_user_by_username(user.username):
            return False
        self.users.append(user)
        return True

    def remove_user(self, user_id: str) -> bool:
        """Remove a user by ID"""
        for i, user in enumerate(self.users):
            if user.id == user_id:
                # Remove user from all repository assignments
                for repo in self.repositories:
                    if user_id in repo.assigned_users:
                        repo.assigned_users.remove(user_id)
                del self.users[i]
                return True
        return False

    def get_users_by_role(self, role: UserRole) -> List[User]:
        """Get all users with a specific role"""
        return [user for user in self.users if user.role == role and user.enabled]

    # Output settings
    output_dir: str = "/app/data/output"
    generate_docs: bool = True
    send_emails: bool = True

    # Web interface settings
    web_enabled: bool = True
    web_port: int = 5000
    web_host: str = "0.0.0.0"
    web_secret_key: str = ""
    web_log_entries: int = 300  # Number of log entries to display in web interface

    def __post_init__(self):
        if not self.web_secret_key:
            self.web_secret_key = secrets.token_hex(32)

    def get_enabled_repositories(self) -> List[RepositoryConfig]:
        """Get list of enabled repositories"""
        return [repo for repo in self.repositories if repo.enabled]

    def get_repository_by_id(self, repo_id: str) -> Optional[RepositoryConfig]:
        """Get repository configuration by ID"""
        for repo in self.repositories:
            if repo.id == repo_id:
                return repo
        return None

    def add_repository(self, repo: RepositoryConfig):
        """Add a new repository configuration"""
        self.repositories.append(repo)

    def remove_repository(self, repo_id: str) -> bool:
        """Remove a repository configuration by ID"""
        for i, repo in enumerate(self.repositories):
            if repo.id == repo_id:
                # Remove repository from user subscriptions
                for user in self.users:
                    if repo_id in user.repository_subscriptions:
                        user.repository_subscriptions.remove(repo_id)
                del self.repositories[i]
                return True
        return False

    def get_repository_by_url(self, url: str) -> Optional[RepositoryConfig]:
        """Get repository by URL"""
        for repo in self.repositories:
            if repo.url == url:
                return repo
        return None

    def get_users_for_repository(self, repo_id: str) -> List[User]:
        """Get all users associated with a repository"""
        users = []
        repo = self.get_repository_by_id(repo_id)
        if repo:
            # Add assigned users
            for user_id in repo.assigned_users:
                user = self.get_user_by_id(user_id)
                if user and user.enabled:
                    users.append(user)

            # Add subscribed users
            for user in self.users:
                if user.enabled and repo_id in user.repository_subscriptions:
                    if user not in users:  # Avoid duplicates
                        users.append(user)

        return users
