@echo off
REM Quick Start Script for Repository Monitor
REM This script starts only the Repository Monitor, assuming your main Docker Compose is already running

echo Repository Monitor Quick Start
echo ==============================

REM Check if main services are running
echo Checking existing Docker services...
docker ps --format "table {{.Names}}" | findstr "ollama-server-local" >nul
if errorlevel 1 (
    echo ERROR: ollama-server-local not found!
    echo Please start your main docker-compose.yml first:
    echo   docker-compose up -d
    pause
    exit /b 1
)

echo Found ollama-server-local

REM Handle command line arguments
if "%1"=="stop" goto stop
if "%1"=="status" goto status
if "%1"=="logs" goto start_with_logs
if "%1"=="build" goto build_and_start

REM Check if config exists
if not exist "config.json" (
    if exist "config.example.json" (
        echo Creating config.json from example...
        copy "config.example.json" "config.json"
        echo Created config.json - please edit with your settings
    ) else (
        echo ERROR: No configuration file found!
        echo Please create config.json or config.example.json
        pause
        exit /b 1
    )
)

goto start

:build_and_start
echo Building Repository Monitor...
docker-compose -f docker-compose.dev.yml build --no-cache
goto start

:start
echo Starting Repository Monitor...
docker-compose -f docker-compose.dev.yml up -d

echo Waiting for Repository Monitor to start...
timeout /t 10 /nobreak >nul

REM Check status
docker-compose -f docker-compose.dev.yml ps repository-monitor | findstr "Up" >nul
if errorlevel 1 (
    echo ERROR: Repository Monitor failed to start!
    echo Check logs with: docker-compose -f docker-compose.dev.yml logs repository-monitor
    pause
    exit /b 1
)

echo Repository Monitor started successfully!
echo.
echo Service URLs:
echo   Repository Monitor: http://localhost:5001
echo   Open WebUI:  http://localhost:3000
echo.
echo Commands:
echo   start-repository-monitor.bat stop    - Stop Repository Monitor
echo   start-repository-monitor.bat status  - Show status
echo   start-repository-monitor.bat logs    - Start with logs
echo   start-repository-monitor.bat build   - Rebuild and start
goto end

:stop
echo Stopping Repository Monitor...
docker-compose -f docker-compose.dev.yml down
echo Repository Monitor stopped
goto end

:status
echo Repository Monitor Status:
docker-compose -f docker-compose.dev.yml ps
echo.
echo Service URLs:
echo   Repository Monitor: http://localhost:5001
echo   Open WebUI:  http://localhost:3000
echo   LLM Proxy:   http://localhost:11440
echo   Ollama API:  http://localhost:11434
goto end

:start_with_logs
call :start
echo.
echo Showing logs (Ctrl+C to exit):
docker-compose -f docker-compose.dev.yml logs -f repository-monitor
goto end

:end
