#!/usr/bin/env python3
"""
Configuration management for the RepoSense AI application
Handles loading, saving, and validation of configuration settings
"""

import json
import logging
import os
from dataclasses import asdict
from pathlib import Path
from enum import Enum
from datetime import datetime

from models import Config, RepositoryConfig, User, UserRole, HistoricalScanConfig, HistoricalScanStatus


class ConfigJSONEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles enums and datetime objects"""

    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value
        elif isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


class ConfigManager:
    """Manager for application configuration"""

    def __init__(self, config_path: str | None = None):
        # Standard config path resolution - only use data directory
        if config_path is None:
            if os.path.exists("/app/data/config.json"):
                config_path = "/app/data/config.json"
            elif os.path.exists("data/config.json"):
                config_path = "data/config.json"
            else:
                config_path = "/app/data/config.json"  # Default to data directory

        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
    
    def load_config(self) -> Config:
        """Load configuration from JSON file"""
        if os.path.exists(self.config_path):
            try:
                with open(self.config_path, 'r') as f:
                    config_dict = json.load(f)

                    # Convert user dictionaries to User objects
                    if 'users' in config_dict:
                        users = []
                        for user_data in config_dict['users']:
                            if isinstance(user_data, dict):
                                # Convert role string to UserRole enum if needed
                                if 'role' in user_data and isinstance(user_data['role'], str):
                                    try:
                                        user_data['role'] = UserRole(user_data['role'])
                                    except ValueError:
                                        # Default to DEVELOPER if invalid role
                                        user_data['role'] = UserRole.DEVELOPER
                                users.append(User(**user_data))
                            else:
                                users.append(user_data)
                        config_dict['users'] = users

                    # Convert repository dictionaries to RepositoryConfig objects
                    if 'repositories' in config_dict:
                        repositories = []
                        for repo_data in config_dict['repositories']:
                            if isinstance(repo_data, dict):
                                # Handle historical_scan configuration
                                if 'historical_scan' in repo_data and isinstance(repo_data['historical_scan'], dict):
                                    scan_data = repo_data['historical_scan']
                                    # Convert scan_status string to enum if needed
                                    if 'scan_status' in scan_data and isinstance(scan_data['scan_status'], str):
                                        try:
                                            scan_data['scan_status'] = HistoricalScanStatus(scan_data['scan_status'])
                                        except ValueError:
                                            scan_data['scan_status'] = HistoricalScanStatus.NOT_STARTED

                                    # Convert datetime strings back to datetime objects if needed
                                    for date_field in ['scan_started_at', 'scan_completed_at']:
                                        if date_field in scan_data and isinstance(scan_data[date_field], str):
                                            try:
                                                scan_data[date_field] = datetime.fromisoformat(scan_data[date_field])
                                            except ValueError:
                                                scan_data[date_field] = None

                                    repo_data['historical_scan'] = HistoricalScanConfig(**scan_data)

                                # Handle datetime fields
                                for date_field in ['last_processed_time', 'last_commit_date']:
                                    if date_field in repo_data and isinstance(repo_data[date_field], str):
                                        try:
                                            repo_data[date_field] = datetime.fromisoformat(repo_data[date_field])
                                        except ValueError:
                                            repo_data[date_field] = None

                                repositories.append(RepositoryConfig(**repo_data))
                            else:
                                repositories.append(repo_data)
                        config_dict['repositories'] = repositories

                    config = Config(**config_dict)

                    # Apply environment variable overrides
                    config = self._apply_environment_overrides(config)

                    self.logger.info(f"Configuration loaded from {self.config_path}")
                    return config
            except Exception as e:
                self.logger.error(f"Error loading config: {e}")
                return self._create_default_config()
        else:
            # Create default config
            return self._create_default_config()
    
    def save_config(self, config: Config):
        """Save configuration to JSON file"""
        try:
            # Ensure the directory exists
            Path(self.config_path).parent.mkdir(parents=True, exist_ok=True)

            with open(self.config_path, 'w') as f:
                json.dump(asdict(config), f, indent=2, cls=ConfigJSONEncoder)

            self.logger.info(f"Configuration saved to {self.config_path}")
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
            raise

    def _apply_environment_overrides(self, config: Config) -> Config:
        """Apply environment variable overrides to configuration

        Environment variables take precedence over config file values.
        This is the final step in configuration loading.
        """
        import os

        # Ollama configuration
        if 'OLLAMA_BASE_URL' in os.environ:
            config.ollama_host = os.environ['OLLAMA_BASE_URL']
            self.logger.info(f"🔧 Ollama host set by environment: {config.ollama_host}")

        if 'OLLAMA_MODEL' in os.environ:
            config.ollama_model = os.environ['OLLAMA_MODEL']
            self.logger.info(f"🔧 Ollama model set by environment: {config.ollama_model}")

        # Web interface configuration
        if 'REPOSENSE_AI_WEB_HOST' in os.environ:
            config.web_host = os.environ['REPOSENSE_AI_WEB_HOST']
            self.logger.info(f"🔧 Web host set by environment: {config.web_host}")

        if 'REPOSENSE_AI_WEB_PORT' in os.environ:
            try:
                config.web_port = int(os.environ['REPOSENSE_AI_WEB_PORT'])
                self.logger.info(f"🔧 Web port set by environment: {config.web_port}")
            except ValueError:
                self.logger.warning(f"⚠️ Invalid web port in environment: {os.environ['REPOSENSE_AI_WEB_PORT']}")

        return config
    
    def _create_default_config(self) -> Config:
        """Create and save a default configuration"""
        config = Config()

        # Apply environment variable overrides
        config = self._apply_environment_overrides(config)

        try:
            self.save_config(config)
            self.logger.info("Default configuration created")
        except Exception as e:
            self.logger.warning(f"Could not save default config: {e}")
        return config
    
    def validate_config(self, config: Config) -> tuple[bool, list]:
        """Validate configuration and return (is_valid, errors)"""
        errors = []

        # Note: Repository validation removed - users should be able to save
        # basic configuration without repositories configured yet.
        # Repositories will be validated when monitoring is started.

        # Validate intervals
        if config.check_interval < 30:
            errors.append("Check interval must be at least 30 seconds")
        
        # Validate ports
        if not (1 <= config.smtp_port <= 65535):
            errors.append("SMTP port must be between 1 and 65535")
        
        if not (1 <= config.web_port <= 65535):
            errors.append("Web port must be between 1 and 65535")
        
        # Validate email settings if email is enabled
        if config.send_emails:
            if not config.email_from:
                errors.append("Email 'from' address is required when email is enabled")
            if not config.email_recipients:
                errors.append("Email recipients are required when email is enabled")
        
        # Validate output directory
        if not config.output_dir:
            errors.append("Output directory is required")
        
        is_valid = len(errors) == 0
        return is_valid, errors

    def validate_config_for_monitoring(self, config: Config) -> tuple[bool, list]:
        """Validate configuration specifically for starting monitoring (requires repositories)"""
        errors = []

        # Validate repositories are configured for monitoring
        if not config.repositories:
            errors.append("At least one repository must be configured to start monitoring")

        enabled_repos = config.get_enabled_repositories()
        if not enabled_repos:
            errors.append("At least one repository must be enabled to start monitoring")

        is_valid = len(errors) == 0
        return is_valid, errors
    
    def update_config_from_form(self, config: Config, form_data: dict) -> Config:
        """Update configuration from web form data

        This updates the config file values. Environment variables will still
        override these values when the configuration is loaded.
        """
        try:
            self.logger.info("📝 Updating configuration from web form")
            self.logger.info(f"   Current ollama_host: {config.ollama_host}")
            self.logger.info(f"   Form ollama_host: {form_data.get('ollama_host', 'NOT_PROVIDED')}")

            # Create new config with form values, using current config as defaults
            updated_config = Config(
                repositories=config.repositories,  # Preserve existing repositories
                users=config.users,  # Preserve existing users
                ollama_host=form_data.get('ollama_host', config.ollama_host),
                ollama_model=form_data.get('ollama_model', config.ollama_model),
                check_interval=int(form_data.get('check_interval', config.check_interval)),
                svn_server_url=form_data.get('svn_server_url') or config.svn_server_url,
                svn_server_username=form_data.get('svn_server_username') or config.svn_server_username,
                svn_server_password=form_data.get('svn_server_password') or config.svn_server_password,
                smtp_host=form_data.get('smtp_host', config.smtp_host),
                smtp_port=int(form_data.get('smtp_port', config.smtp_port)),
                smtp_username=form_data.get('smtp_username') or config.smtp_username,
                smtp_password=form_data.get('smtp_password') or config.smtp_password,
                email_from=form_data.get('email_from', config.email_from),
                email_recipients=[r.strip() for r in form_data.get('email_recipients', ','.join(config.email_recipients)).split(',') if r.strip()],
                generate_docs='generate_docs' in form_data,
                send_emails='send_emails' in form_data,
                web_enabled=True,
                web_port=config.web_port,
                web_host=config.web_host,
                web_secret_key=config.web_secret_key,
                web_log_entries=int(form_data.get('web_log_entries', config.web_log_entries)),
                output_dir=config.output_dir
            )

            self.logger.info(f"📄 Config file will be saved with ollama_host: {updated_config.ollama_host}")

            # NOTE: Environment variables will override these values when config is loaded
            # This is intentional - environment variables have higher priority

            return updated_config

        except (ValueError, TypeError) as e:
            self.logger.error(f"Error updating config from form: {e}")
            raise ValueError(f"Invalid form data: {e}")

    def add_repository_from_form(self, config: Config, form_data: dict) -> RepositoryConfig:
        """Add a new repository from web form data"""
        # Parse email recipients
        email_recipients = []
        if form_data.get('email_recipients'):
            email_recipients = [email.strip() for email in form_data.get('email_recipients', '').split(',') if email.strip()]

        repo = RepositoryConfig(
            name=form_data.get('name', ''),
            url=form_data.get('url', ''),
            username=form_data.get('username') or None,
            password=form_data.get('password') or None,
            enabled=form_data.get('enabled', 'off') == 'on',
            email_recipients=email_recipients
        )
        config.add_repository(repo)
        return repo

    def update_repository_from_form(self, config: Config, repo_id: str, form_data: dict) -> bool:
        """Update an existing repository from web form data"""
        repo = config.get_repository_by_id(repo_id)
        if repo:
            # Parse email recipients
            email_recipients = []
            if form_data.get('email_recipients'):
                email_recipients = [email.strip() for email in form_data.get('email_recipients', '').split(',') if email.strip()]

            # Parse product documentation files
            product_doc_files = []
            if form_data.get('product_documentation_files'):
                product_doc_files = [file.strip() for file in form_data.get('product_documentation_files', '').split('\n') if file.strip()]

            repo.name = form_data.get('name', repo.name)
            repo.url = form_data.get('url', repo.url)
            repo.username = form_data.get('username') or None
            repo.password = form_data.get('password') or None
            repo.enabled = form_data.get('enabled', 'off') == 'on'
            repo.email_recipients = email_recipients
            repo.product_documentation_files = product_doc_files
            return True
        return False

    def get_config_path(self) -> str:
        """Get the configuration file path"""
        return self.config_path
