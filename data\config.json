{"users": [], "repositories": [{"id": "e96464c8-5f96-4e07-b72b-70c618be64e9", "name": "svn_monitor_server_test_repo", "url": "http://sundc:81/svn/svn_monitor_server_test_repo", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 8, "last_commit_date": "2025-08-06T19:49:43.657025Z", "last_processed_time": "2025-08-06T21:55:09.609638", "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 8, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "last_scanned_revision": null, "scan_status": "not_started", "scan_started_at": null, "scan_completed_at": null, "total_revisions": null, "processed_revisions": 0, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}, "product_documentation_files": ["/CHANGELOG.docx", "/CHANGELOG.html", "/README.md"]}], "ollama_host": "http://************:11434", "ollama_model": "codeqwen:7b-chat-v1.5-q8_0", "ollama_model_documentation": null, "ollama_model_code_review": null, "ollama_model_risk_assessment": null, "check_interval": 300, "svn_server_url": "http://sundc:81/svn", "svn_server_username": "fvaneijk", "svn_server_password": "an<PERSON><PERSON><PERSON>", "svn_server_type": "auto", "smtp_host": "localhost", "smtp_port": 587, "smtp_username": null, "smtp_password": null, "email_from": "<EMAIL>", "email_recipients": [], "output_dir": "/app/data/output", "generate_docs": true, "send_emails": false, "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "c1ea8ae1e1505ba37bd79788543d8fa7c48f9308c8fa67a1098a9e9936b010fc", "web_log_entries": 300}