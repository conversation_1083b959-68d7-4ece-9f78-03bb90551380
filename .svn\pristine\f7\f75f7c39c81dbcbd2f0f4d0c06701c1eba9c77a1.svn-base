{"repositories": [{"name": "RepoSense AI Server", "url": "http://sundc:81/svn/svn_monitor_server", "username": "your_svn_username", "password": "your_svn_password", "enabled": true, "scan_interval": 300}], "ollama": {"base_url": "http://ollama:11434", "model": "llama3.2:3b", "timeout": 30, "max_retries": 3, "fallback_models": ["llama3.2:1b", "qwen2.5:3b"]}, "web": {"host": "0.0.0.0", "port": 5000, "debug": false, "secret_key": "your-production-secret-key-here"}, "email": {"enabled": false, "smtp_server": "smtp.gmail.com", "smtp_port": 587, "username": "", "password": "", "from_address": "", "to_addresses": []}, "users": [{"username": "admin", "email": "<EMAIL>", "role": "admin", "password_hash": "your-hashed-password-here"}], "logging": {"level": "INFO", "max_file_size": "10MB", "backup_count": 5}, "ai_analysis": {"enabled": true, "batch_size": 10, "use_heuristics": true, "fallback_to_llm": true, "cache_results": true}, "security": {"require_authentication": true, "session_timeout": 3600, "max_login_attempts": 5}, "performance": {"max_workers": 4, "cache_size": 1000, "database_pool_size": 10}}