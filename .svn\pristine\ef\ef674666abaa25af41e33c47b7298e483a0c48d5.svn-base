{% extends "base.html" %}

{% block title %}Logs - RepoSense AI{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Application Logs</h1>
            <p class="page-subtitle">View real-time application logs and system events</p>
        </div>
        <button class="btn btn-outline-primary" onclick="refreshLogs()">
            <i class="fas fa-sync-alt"></i> Refresh
        </button>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-file-alt"></i>System Logs</h5>
            </div>
            <div class="card-body p-0">
                <div class="log-container p-3" id="log-container">
                    {% for log_line in logs %}
                        {% if log_line.strip() %}
                            <div class="log-line">{{ log_line }}</div>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>
            <div class="card-footer">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    Showing last {{ log_entries_count or 100 }} log entries. Logs auto-refresh every 10 seconds.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function refreshLogs() {
    location.reload();
}

// Auto-refresh logs every 10 seconds
setInterval(refreshLogs, 10000);

// Scroll to bottom of logs
document.addEventListener('DOMContentLoaded', function() {
    const logContainer = document.getElementById('log-container');
    logContainer.scrollTop = logContainer.scrollHeight;
});

// Color-code log levels
document.addEventListener('DOMContentLoaded', function() {
    const logLines = document.querySelectorAll('.log-line');
    logLines.forEach(line => {
        const text = line.textContent;
        if (text.includes('ERROR')) {
            line.style.color = '#dc3545';
        } else if (text.includes('WARNING')) {
            line.style.color = '#ffc107';
        } else if (text.includes('INFO')) {
            line.style.color = '#17a2b8';
        }
    });
});
</script>
{% endblock %}
