# Configuration Guide

This guide covers all configuration options for the RepoSense AI system, including setup, customization, and best practices.

## Configuration Overview

RepoSense AI uses a JSON-based configuration system with both file-based and web-based management. The configuration is centralized in `config.json` and can be modified through the web interface.

### Multi-Repository Support

The application supports monitoring multiple repositories with individual configurations:

- **Repository Management**: Add, edit, delete, and enable/disable repositories through the web interface
- **Independent Authentication**: Each repository can have its own username/password
- **Separate Output Directories**: Each repository gets its own organized output structure
- **Individual Email Lists**: Repository-specific email recipients plus global recipients

### Email Notification System

The email system supports a two-tier recipient structure:

1. **Global Recipients** (`email_recipients` in main config):
   - Receive notifications for ALL repositories
   - Typically managers, admins, or team leads
   - Configured in the main Configuration page

2. **Repository-Specific Recipients** (`email_recipients` per repository):
   - Receive notifications only for specific repositories
   - Typically developers working on that specific project
   - Configured in the Repositories page for each repository

3. **Smart Deduplication**:
   - Final recipient list = Global recipients + Repository-specific recipients
   - Automatic removal of duplicate email addresses
   - Each person receives only one email per commit, regardless of being in both lists

### Output Directory Structure

The application organizes output files in a structured directory hierarchy:

```
/app/data/
├── config.json                    # Main configuration file
├── reposense_ai.log         # Application logs
└── repositories/                  # Repository-specific outputs
    ├── {repository-id-1}/
    │   ├── docs/                  # Generated documentation
    │   │   ├── revision_123_2024-01-15.md
    │   │   └── revision_124_2024-01-16.md
    │   └── emails/                # Email copies
    │       ├── revision_123_2024-01-15.txt
    │       └── revision_124_2024-01-16.txt
    └── {repository-id-2}/
        ├── docs/
        └── emails/
```

**Key Features:**
- **Repository Isolation**: Each repository gets its own subdirectory using a sanitized repository ID
- **Organized by Type**: Documentation and email copies are separated into `docs/` and `emails/` subdirectories
- **Timestamped Files**: Files are named with revision number and date for easy identification
- **Persistent Storage**: All files are stored in the Docker volume for persistence across container restarts

## SVN Server Configuration and Repository Discovery

The system supports configuring an SVN server connection and browsing all repositories on that server:

### Configuration Workflow

1. **Configure SVN Server** (in Settings page):
   - Set SVN Server Base URL (e.g., `http://sundc:81/svn`)
   - Set default username/password for server access
   - These settings are used as defaults for repository discovery

2. **Discover Repositories**:
   - Click "Discover Repositories" button from Settings page
   - Repository discovery form is pre-populated with server settings
   - Scan the server to find all available repositories
   - Import selected repositories for monitoring

3. **Repository Management**:
   - Individual repositories are managed in the Repositories section
   - Each repository can have its own credentials
   - Repository-specific user assignments and email settings

### Key Features

- **Server-level Configuration**: Set base SVN server URL and default credentials
- **Automatic Discovery**: Recursively scan SVN server for repositories
- **Standard Layout Detection**: Identifies repositories by trunk/branches/tags structure
- **Flexible Authentication**: Server defaults with per-repository overrides
- **User-friendly Workflow**: Seamless integration between configuration and discovery

### Common Issues and Solutions

#### Configuration Page Becomes Unresponsive

**Issue**: Page greys out and becomes unresponsive when unchecking "Send Email Notifications"

**Root Cause**: JavaScript selector was using `.card:last-of-type .card-body` which targeted the entire form instead of just the email settings section.

**Solution**:
1. Added specific ID `email-settings-body` to the email settings card body
2. Updated JavaScript to use `document.getElementById('email-settings-body')`
3. Added error handling and console logging for debugging
4. Improved visual feedback with `pointer-events` and cursor styling

**Prevention**: Always use specific element IDs instead of generic CSS selectors for JavaScript interactions.

## Initial Setup

### 1. Create Configuration File

Create your configuration file in the data directory:
```bash
# Create data directory if it doesn't exist
mkdir -p data

# Create minimal configuration
cat > data/config.json << 'EOF'
{
  "repositories": [],
  "ollama_host": "http://ollama:11434",
  "ollama_model": "llama2",
  "check_interval": 300,
  "smtp_host": "localhost",
  "smtp_port": 587,
  "smtp_username": null,
  "smtp_password": null,
  "email_from": "svn-monitor@localhost",
  "email_recipients": [],
  "output_dir": "/app/data",
  "generate_docs": true,
  "send_emails": false,
  "web_enabled": true,
  "web_port": 5000,
  "web_host": "0.0.0.0",
  "web_secret_key": "change-this-secret-key-in-production",
  "users": []
}
EOF
```

### 2. Basic Configuration Structure

```json
{
  "repositories": [],
  "users": [],
  "ollama": {
    "base_url": "http://ollama:11434",
    "model": "llama2",
    "timeout": 30
  },
  "email": {
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "your-app-password",
    "from_email": "<EMAIL>",
    "use_tls": true
  },
  "web": {
    "host": "0.0.0.0",
    "port": 5000,
    "secret_key": "your-secret-key-here"
  },
  "monitoring": {
    "interval_seconds": 300,
    "max_retries": 3,
    "retry_delay": 60
  },
  "output": {
    "base_directory": "/app/data/output",
    "create_subdirs": true
  },
  "logging": {
    "level": "INFO",
    "file": "/app/data/logs/svn_monitor.log",
    "max_size_mb": 10,
    "backup_count": 5
  }
}
```

## Configuration Sections

### Repository Configuration

Repositories can be configured through the web interface or directly in the JSON file:

```json
{
  "repositories": [
    {
      "id": "repo-uuid-here",
      "name": "My Project",
      "url": "https://svn.example.com/repos/myproject",
      "username": "svn_user",
      "password": "svn_password",
      "last_revision": 0,
      "enabled": true,
      "assigned_users": ["user-uuid-1", "user-uuid-2"],
      "email_recipients": ["<EMAIL>"]
    }
  ]
}
```

**Repository Fields:**
- `id`: Unique identifier (auto-generated)
- `name`: Display name for the repository
- `url`: SVN repository URL (base URL without branch path)
- `username`: SVN authentication username (optional)
- `password`: SVN authentication password (optional)
- `last_revision`: Last processed revision number
- `enabled`: Whether monitoring is active
- `branch_path`: Specific branch to monitor (e.g., "trunk", "branches/feature-x")
- `monitor_all_branches`: If true, monitor all branches; if false, monitor only branch_path
- `assigned_users`: List of user IDs with access
- `email_recipients`: Legacy email addresses (backward compatibility)

### Branch Selection Configuration

SVN Monitor supports flexible branch monitoring options:

**Monitor Specific Branch:**
```json
{
  "url": "https://svn.example.com/repos/myproject",
  "branch_path": "trunk",
  "monitor_all_branches": false
}
```

**Monitor Specific Feature Branch:**
```json
{
  "url": "https://svn.example.com/repos/myproject",
  "branch_path": "branches/feature-authentication",
  "monitor_all_branches": false
}
```

**Monitor All Branches:**
```json
{
  "url": "https://svn.example.com/repos/myproject",
  "branch_path": null,
  "monitor_all_branches": true
}
```

**Branch Path Examples:**
- `trunk` - Main development branch
- `branches/feature-name` - Specific feature branch
- `branches/release-1.0` - Release branch
- `tags/v1.0.0` - Specific tag (read-only monitoring)

### User Management Configuration

Users are managed through the web interface but stored in the configuration:

```json
{
  "users": [
    {
      "id": "user-uuid-here",
      "username": "john.doe",
      "email": "<EMAIL>",
      "full_name": "John Doe",
      "role": "developer",
      "enabled": true,
      "receive_all_notifications": false,
      "repository_subscriptions": ["repo-uuid-1"],
      "phone": "******-0123",
      "department": "Engineering",
      "created_date": "2024-01-01T00:00:00Z",
      "last_modified": "2024-01-01T00:00:00Z"
    }
  ]
}
```

**User Roles:**
- `admin`: Full system access
- `manager`: User and repository management
- `developer`: Repository access and notifications
- `viewer`: Read-only access

### Ollama AI Configuration

Configure AI-powered documentation generation:

```json
{
  "ollama": {
    "base_url": "http://ollama:11434",
    "model": "llama2",
    "timeout": 30,
    "max_tokens": 2000,
    "temperature": 0.7,
    "system_prompt": "You are a technical documentation assistant..."
  }
}
```

**Available Models:**
- `llama2`: General purpose model
- `codellama`: Code-focused model
- `mistral`: Fast and efficient
- `neural-chat`: Conversational model

### Email Configuration

Configure SMTP settings for notifications:

```json
{
  "email": {
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "app-specific-password",
    "from_email": "SVN Monitor <<EMAIL>>",
    "from_name": "SVN Monitor",
    "use_tls": true,
    "use_ssl": false,
    "timeout": 30
  }
}
```

**Common SMTP Providers:**

**Gmail:**
```json
{
  "smtp_server": "smtp.gmail.com",
  "smtp_port": 587,
  "use_tls": true
}
```

**Outlook/Hotmail:**
```json
{
  "smtp_server": "smtp-mail.outlook.com",
  "smtp_port": 587,
  "use_tls": true
}
```

**Custom SMTP:**
```json
{
  "smtp_server": "mail.yourcompany.com",
  "smtp_port": 25,
  "use_tls": false
}
```

### Web Interface Configuration

Configure the Flask web application:

```json
{
  "web": {
    "host": "0.0.0.0",
    "port": 5000,
    "secret_key": "your-secret-key-here",
    "debug": false,
    "max_content_length": 16777216
  }
}
```

**Security Note:** Always use a strong, unique secret key in production.

### Monitoring Configuration

Control monitoring behavior:

```json
{
  "monitoring": {
    "interval_seconds": 300,
    "max_retries": 3,
    "retry_delay": 60,
    "batch_size": 10,
    "parallel_processing": true
  }
}
```

**Interval Guidelines:**
- Development: 60-300 seconds
- Production: 300-900 seconds
- High-activity repos: 60-180 seconds
- Archive repos: 1800+ seconds

### Output Configuration

Configure file storage and organization:

```json
{
  "output": {
    "base_directory": "/app/data/output",
    "create_subdirs": true,
    "file_permissions": "644",
    "directory_permissions": "755",
    "cleanup_old_files": true,
    "retention_days": 90
  }
}
```

**Directory Structure:**
```
/app/data/output/
├── repositories/
│   ├── {repo-id}/
│   │   ├── docs/
│   │   │   ├── revision-123.md
│   │   │   └── revision-124.md
│   │   └── emails/
│   │       ├── revision-123.html
│   │       └── revision-124.html
│   └── {repo-id-2}/
└── logs/
    └── svn_monitor.log
```

### Logging Configuration

Configure application logging:

```json
{
  "logging": {
    "level": "INFO",
    "file": "/app/data/logs/reposense_ai.log",
    "max_size_mb": 10,
    "backup_count": 5,
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "console_output": true
  }
}
```

**Log Levels:**
- `DEBUG`: Detailed debugging information
- `INFO`: General information messages
- `WARNING`: Warning messages
- `ERROR`: Error messages
- `CRITICAL`: Critical error messages

## Environment Variables

Override configuration with environment variables:

```bash
# Database
export SVN_MONITOR_CONFIG_FILE="/path/to/config.json"

# Web Interface
export SVN_MONITOR_WEB_HOST="0.0.0.0"
export SVN_MONITOR_WEB_PORT="5000"
export SVN_MONITOR_SECRET_KEY="your-secret-key"

# Email
export SVN_MONITOR_SMTP_SERVER="smtp.gmail.com"
export SVN_MONITOR_SMTP_USERNAME="<EMAIL>"
export SVN_MONITOR_SMTP_PASSWORD="app-password"

# Ollama
export SVN_MONITOR_OLLAMA_URL="http://ollama:11434"
export SVN_MONITOR_OLLAMA_MODEL="llama2"

# Monitoring
export SVN_MONITOR_INTERVAL="300"
export SVN_MONITOR_LOG_LEVEL="INFO"
```

## Docker Configuration

### Docker Compose Setup

```yaml
version: '3.8'
services:
  svn-monitor:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./config.json:/app/config.json
      - ./data:/app/data
    environment:
      - SVN_MONITOR_WEB_HOST=0.0.0.0
      - SVN_MONITOR_WEB_PORT=5000
    depends_on:
      - ollama

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
```

### Volume Mapping

```yaml
volumes:
  - ./config.json:/app/config.json:ro    # Configuration (read-only)
  - ./data:/app/data                     # Data persistence
  - ./logs:/app/logs                     # Log files
```

## Configuration Best Practices

### Security
1. **Use strong secret keys** for web interface
2. **Store credentials securely** (consider environment variables)
3. **Limit file permissions** on configuration files
4. **Use HTTPS** in production
5. **Regular credential rotation**

### Performance
1. **Optimize monitoring intervals** based on repository activity
2. **Use appropriate batch sizes** for multiple repositories
3. **Configure log rotation** to prevent disk space issues
4. **Monitor resource usage** and adjust accordingly

### Reliability
1. **Configure appropriate timeouts** for external services
2. **Set reasonable retry policies** for transient failures
3. **Monitor disk space** for output directories
4. **Regular configuration backups**

### Maintenance
1. **Document custom configurations**
2. **Version control configuration files**
3. **Test configuration changes** in development
4. **Monitor application logs** for configuration issues

## Troubleshooting Configuration

### Common Issues

**Web Interface Not Accessible:**
- Check host/port configuration
- Verify firewall settings
- Ensure Docker port mapping

**Email Notifications Not Working:**
- Verify SMTP credentials
- Check TLS/SSL settings
- Test with email provider's settings

**Repository Access Issues:**
- Validate SVN credentials
- Check repository URL accessibility
- Verify network connectivity

**AI Documentation Not Generated:**
- Confirm Ollama service is running
- Check model availability
- Verify API connectivity

### Configuration Validation

The system provides built-in configuration validation:

```python
# Validate configuration
is_valid, errors = config_manager.validate_config(config)
if not is_valid:
    for error in errors:
        print(f"Configuration error: {error}")
```

### Configuration Migration

When upgrading, the system automatically migrates configuration:

1. **Backup existing configuration**
2. **Apply schema updates**
3. **Validate migrated configuration**
4. **Report migration results**
