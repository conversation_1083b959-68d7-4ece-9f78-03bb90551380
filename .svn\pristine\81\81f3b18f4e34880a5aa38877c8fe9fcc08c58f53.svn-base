# SVN Monitor

A comprehensive SVN repository monitoring system with AI-powered documentation generation and intelligent user management.

## Features

- **Multi-Repository Monitoring**: Monitor multiple SVN repositories simultaneously
- **User Management**: Role-based user system with flexible notification preferences  
- **Repository Discovery**: Automatically discover repositories from SVN servers
- **AI-Powered Documentation**: Generate intelligent documentation using Ollama AI models
- **Smart Notifications**: Context-aware email notifications with user targeting
- **Modern Web Interface**: Responsive web UI for configuration and monitoring
- **Docker Support**: Containerized deployment with Docker Compose

## Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd svn-checkin-monitor
   ```

2. **Configure the system**
   ```bash
   cp config.example.json config.json
   # Edit config.json with your settings
   ```

3. **Deploy with Docker**
   ```bash
   docker-compose up -d
   ```

4. **Access the web interface**
   - Open http://localhost:5000
   - Configure repositories and users
   - Start monitoring

## Documentation

Comprehensive documentation is available in the `docs/` folder:

- **[📖 Documentation Index](docs/index.md)** - Start here for complete documentation
- **[🏗️ Architecture & Design](docs/design.md)** - System architecture and technical design
- **[⚙️ Configuration Guide](docs/configuration.md)** - Complete configuration reference
- **[👤 User Guide](docs/usage.md)** - How to use the system
- **[🚀 Deployment Guide](docs/deployment.md)** - Production deployment instructions
- **[💻 Development Guide](docs/development.md)** - Contributing and development setup

## System Requirements

- Docker & Docker Compose (recommended)
- Python 3.8+ (for local development)
- SVN client tools
- Ollama server (for AI features)
- SMTP server (for notifications)

## Architecture

The system follows a modular, service-oriented architecture:

```
Web Interface ←→ Monitor Service ←→ Repository Discovery
      ↓                ↓                     ↓
User Management ←→ Config Manager ←→ Email Service
      ↓                ↓                     ↓
File Manager ←→ Ollama Service ←→ SVN Operations
```

## Key Components

- **Monitor Service**: Core SVN monitoring and change detection
- **User Management**: Role-based user system with repository associations
- **Repository Discovery**: Automatic repository discovery from SVN servers
- **Web Interface**: Modern responsive UI for configuration and monitoring
- **AI Integration**: Ollama-powered intelligent documentation generation
- **Email Service**: Smart notifications with user targeting

## Configuration

The system uses JSON-based configuration with web interface management:

```json
{
  "repositories": [...],
  "users": [...],
  "ollama": {
    "base_url": "http://ollama:11434",
    "model": "llama2"
  },
  "email": {
    "smtp_server": "smtp.gmail.com",
    "smtp_port": 587
  },
  "web": {
    "host": "0.0.0.0",
    "port": 5000
  }
}
```

See the [Configuration Guide](docs/configuration.md) for complete details.

## User Management

The system includes comprehensive user management with:

- **Role-based Access**: Admin, Manager, Developer, Viewer roles
- **Repository Associations**: Assign users to specific repositories
- **Notification Preferences**: Global or repository-specific notifications
- **Contact Management**: Phone, department, and other contact details

## Repository Discovery

Automatically discover repositories from SVN servers:

- **Server Scanning**: Scan SVN servers for available repositories
- **Structure Analysis**: Identify repository structure (trunk, branches, tags)
- **Bulk Import**: Import multiple repositories at once
- **Access Validation**: Verify repository accessibility

## Docker Deployment

The system is designed for containerized deployment:

```yaml
version: '3.8'
services:
  svn-monitor:
    build: .
    ports:
      - "5000:5000"
    volumes:
      - ./config.json:/app/config.json
      - ./data:/app/data
    depends_on:
      - ollama

  ollama:
    image: ollama/ollama:latest
    ports:
      - "11434:11434"
```

## Development

For development setup and contribution guidelines, see the [Development Guide](docs/development.md).

### Local Development

```bash
# Install dependencies
pip install -r requirements.txt

# Run the application
python svn_monitor_app.py
```

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

- **Documentation**: Check the [docs/](docs/) folder
- **Issues**: Report issues on the project repository
- **Configuration Help**: See [Configuration Guide](docs/configuration.md)
- **Usage Questions**: Check [User Guide](docs/usage.md)

## Version

- **Current Version**: 2.0.0
- **Architecture**: Modular service-oriented design
- **Python Version**: 3.8+
- **Docker Support**: Yes
- **Web Interface**: Modern responsive design
