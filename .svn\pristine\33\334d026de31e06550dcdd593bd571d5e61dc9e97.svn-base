# SVN Monitor - Multi-Repository Monitoring with AI

A Python application that monitors multiple SVN repositories for changes and generates AI-powered documentation and email notifications using Ollama.

## Features

- **Multi-Repository Monitoring**: Monitor multiple SVN repositories simultaneously with independent configurations
- **AI Documentation**: Uses Ollama to generate intelligent documentation for code changes
- **Flexible Email Notifications**: Global and repository-specific email recipients with smart deduplication
- **Modern Web Interface**: Responsive web-based configuration and monitoring dashboard
- **Docker Support**: Easy deployment with Docker and Docker Compose including automated setup
- **Modular Architecture**: Clean separation of concerns with dedicated modules for each functionality
- **Comprehensive Logging**: Detailed logging for monitoring, debugging, and audit trails
- **Legacy Migration**: Automatic migration from single-repository to multi-repository configurations

## Architecture Overview

The application is built with a modular architecture:

- **`svn_monitor_app.py`** - Main application entry point and orchestration
- **`models.py`** - Data models for configuration and commit information
- **`config_manager.py`** - Configuration loading, saving, and validation
- **`svn_service.py`** - SVN operations and repository interaction
- **`ollama_service.py`** - AI integration for documentation generation
- **`email_service.py`** - Email notification handling with recipient management
- **`file_manager.py`** - File operations and output organization
- **`monitor_service.py`** - Core monitoring logic and coordination
- **`web_interface.py`** - Flask-based web interface and API endpoints

## Quick Start with Docker

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd svn-checkin-monitor
   ```

2. **Run the setup script:**
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

3. **Access the web interface:**
   Open http://localhost:5000 in your browser

4. **Configure your repositories:**
   - Go to the "Repositories" page to add your SVN repositories
   - Configure global settings in the "Configuration" page
   - Set up email notifications with global and repository-specific recipients
   - Start monitoring from the dashboard

## Modern Web Interface

The application features a modern, responsive web interface with:

- **Sidebar Navigation**: Clean sidebar with intuitive navigation between sections
- **Modern Design**: Contemporary styling with Inter font, subtle shadows, and smooth animations
- **Responsive Layout**: Mobile-friendly design that adapts to different screen sizes
- **Status Indicators**: Animated status indicators with pulsing effects for running services
- **Card-based Layout**: Organized content in cards with hover effects and rounded corners
- **Accessible from Outside Docker**: Web interface is accessible from any device on your network at `http://your-server-ip:5000`

## Web Interface

The application includes a comprehensive web interface:

- **Dashboard**: Monitor status, control the service, view statistics
- **Configuration**: Complete settings management via web forms
- **Logs**: Real-time log viewing with auto-refresh
- **API**: RESTful endpoints for integration

### Web Interface Features

- **Real-time Status Updates**: Live monitoring of service status
- **One-Click Controls**: Start/stop monitoring, run manual checks
- **Form-based Configuration**: No need to edit JSON files
- **Log Viewing**: Last 100 log entries with auto-refresh
- **Ollama Testing**: Built-in connection testing
- **Responsive Design**: Works on desktop and mobile

## Configuration

### Multi-Repository Support

The application supports monitoring multiple SVN repositories with individual configurations:

- **Repository Management**: Add, edit, delete, and enable/disable repositories through the web interface
- **Independent Authentication**: Each repository can have its own username/password
- **Separate Output Directories**: Each repository gets its own organized output structure
- **Individual Email Lists**: Repository-specific email recipients plus global recipients

### Email Notification System

The email system supports a two-tier recipient structure:

1. **Global Recipients** (`email_recipients` in main config):
   - Receive notifications for ALL repositories
   - Typically managers, admins, or team leads
   - Configured in the main Configuration page

2. **Repository-Specific Recipients** (`email_recipients` per repository):
   - Receive notifications only for specific repositories
   - Typically developers working on that specific project
   - Configured in the Repositories page for each repository

3. **Smart Deduplication**:
   - Final recipient list = Global recipients + Repository-specific recipients
   - Automatic removal of duplicate email addresses
   - Each person receives only one email per commit, regardless of being in both lists

### Configuration Sections

#### 1. Repository Configuration (`/repositories`)
- **Repository Management**: Add, edit, delete repositories
- **Per-Repository Settings**:
  - Name and SVN URL
  - Authentication credentials (username/password)
  - Repository-specific email recipients
  - Enable/disable monitoring

#### 2. Global Configuration (`/config`)
- **Ollama AI Settings**:
  - Host URL (default: http://ollama:11434)
  - Model selection (llama2, mistral, codellama, etc.)
- **Monitoring Settings**:
  - Check interval (60-86400 seconds)
  - Enable/disable documentation generation
  - Enable/disable email notifications globally
- **Email Settings**:
  - SMTP server configuration
  - Global email recipients (receive all notifications)
  - Sender settings

#### 3. Dashboard (`/`)
- **System Status**: Overall monitoring status and health
- **Repository Overview**: Status of all configured repositories
- **Quick Actions**: Start/stop monitoring, manual checks
- **Recent Activity**: Latest commits and system events

### Output Directory Structure

The application organizes output files in a structured directory hierarchy:

```
/app/data/
├── config.json                    # Main configuration file
├── svn_monitor.log                # Application logs
└── repositories/                  # Repository-specific outputs
    ├── {repository-id-1}/
    │   ├── docs/                  # Generated documentation
    │   │   ├── revision_123_2024-01-15.md
    │   │   └── revision_124_2024-01-16.md
    │   └── emails/                # Email copies
    │       ├── revision_123_2024-01-15.txt
    │       └── revision_124_2024-01-16.txt
    └── {repository-id-2}/
        ├── docs/
        └── emails/
```

**Key Features:**
- **Repository Isolation**: Each repository gets its own subdirectory using a sanitized repository ID
- **Organized by Type**: Documentation and email copies are separated into `docs/` and `emails/` subdirectories
- **Timestamped Files**: Files are named with revision number and date for easy identification
- **Persistent Storage**: All files are stored in the Docker volume for persistence across container restarts



## Directory Structure

```
.
├── docker-compose.yml
├── Dockerfile
├── svn_monitor.py
├── requirements.txt
├── setup.sh
├── .env.example
├── data/                 # Persistent data (mounted volume)
│   ├── config.json      # Configuration file
│   ├── svn_monitor.log  # Application logs
│   └── output/          # Generated content
│       ├── docs/        # Documentation files
│       └── emails/      # Email copies
└── config/              # Optional external config files
```

## Docker Services

### svn-monitor
- **Image**: Built from local Dockerfile
- **Purpose**: Main application container
- **Volumes**: `./data:/app/data` (persistent storage)
- **Network**: Connected to ollama service

### ollama
- **Image**: `ollama/ollama:latest`
- **Purpose**: LLM inference server
- **Ports**: `11434:11434` (API endpoint)
- **Volume**: `ollama_data` (model storage)

## Usage Commands

### Start Services
```bash
docker-compose up -d
```

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f svn-monitor
docker-compose logs -f ollama
```

### Run One-Time Check
```bash
docker-compose run --rm svn-monitor python svn_monitor.py --once
```

### Interactive Setup
```bash
docker-compose run --rm svn-monitor python svn_monitor.py --setup
```

### Stop Services
```bash
docker-compose down
```

### Restart Services
```bash
docker-compose restart
```

## Model Management

### List Available Models
```bash
docker-compose exec ollama ollama list
```

### Pull New Model
```bash
docker-compose exec ollama ollama pull mistral
```

### Update Configuration for New Model
Edit `data/config.json` and change `"ollama_model": "mistral"`, then restart:
```bash
docker-compose restart svn-monitor
```

## Troubleshooting

### Check Service Health
```bash
docker-compose ps
```

### View Detailed Logs
```bash
docker-compose logs --tail=100 svn-monitor
```

### Test Ollama Connection
```bash
docker-compose exec ollama curl http://localhost:11434/api/tags
```

### Access Container Shell
```bash
docker-compose exec svn-monitor bash
docker-compose exec ollama bash
```

### Reset Everything
```bash
docker-compose down -v  # Removes volumes
docker-compose build --no-cache
./setup.sh
```

## Security Considerations

- The application runs as non-root user (UID 1000)
- Sensitive data (passwords) should be stored in environment files
- Consider using Docker secrets for production deployments
- SVN credentials are stored in the configuration file

## Performance Tuning

### Ollama Performance
- **GPU Support**: Add GPU support to docker-compose.yml if available
- **Memory**: Increase Docker memory limits for larger models
- **Models**: Use smaller models (like `llama2:7b`) for faster responses

### Application Performance
- **Check Interval**: Adjust `check_interval` based on your needs
- **Batch Processing**: The app processes commits sequentially

## Production Deployment

For production use:

1. **Use external databases** for configuration storage
2. **Implement proper logging** aggregation (ELK stack, etc.)
3. **Set up monitoring** with health checks
4. **Use secrets management** for credentials
5. **Configure resource limits** in docker-compose.yml
6. **Set up automated backups** of the data volume

## Health Monitoring

The containers include health checks:
- **svn-monitor**: Checks if Python process is running
- **ollama**: Checks if API endpoint is responding

Monitor with:
```bash
docker-compose ps  # Shows health status
```

## Data Persistence

- **Application data**: Stored in `./data` directory (logs, config, output)
- **Ollama models**: Stored in Docker volume `ollama_data`
- **Configuration**: Persisted in `data/config.json`

## Backup Strategy

```bash
# Backup application data
tar -czf svn-monitor-backup-$(date +%Y%m%d).tar.gz data/

# Backup Ollama models
docker-compose exec ollama ollama list  # List models to backup
# Models are in Docker volume, backup with Docker volume backup tools
```
