{"users": [], "repositories": [{"id": "1ebcd006-d993-4dfa-85de-ec02c6e3e4e0", "name": "svn_monitor_server_test_repo", "url": "http://sundc:81/svn/svn_monitor_server_test_repo", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 5, "enabled": true, "branch_path": null, "monitor_all_branches": false, "assigned_users": [], "email_recipients": [], "historical_scan": {"enabled": true, "scan_by_revision": true, "start_revision": 1, "end_revision": 5, "scan_by_date": false, "start_date": null, "end_date": null, "batch_size": 10, "include_merge_commits": true, "skip_large_commits": false, "max_files_per_commit": 100, "last_scanned_revision": 5, "scan_status": "completed", "scan_started_at": null, "scan_completed_at": "2025-08-05T02:20:02.311779", "total_revisions": null, "processed_revisions": 5, "failed_revisions": 0, "error_message": null, "generate_documentation": true, "analyze_code_review": true, "analyze_documentation_impact": true}}], "ollama_host": "http://************:11434", "ollama_model": "llama3:latest", "check_interval": 300, "svn_server_url": "http://sundc:81/svn", "svn_server_username": "fvaneijk", "svn_server_password": "an<PERSON><PERSON><PERSON>", "svn_server_type": "auto", "smtp_host": "localhost", "smtp_port": 587, "smtp_username": null, "smtp_password": null, "email_from": "<EMAIL>", "email_recipients": [], "output_dir": "/app/data/output", "generate_docs": true, "send_emails": false, "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "c1ea8ae1e1505ba37bd79788543d8fa7c48f9308c8fa67a1098a9e9936b010fc", "web_log_entries": 300}