# Integration Guide

This guide shows how to integrate RepoSense AI with existing Docker setups and external services.

## Docker Integration

### Integration with Existing Docker Compose

If you have existing Docker services (Ollama, LLM Proxy, Open WebUI, etc.):

#### 1. Network Integration

**Option A: Use Existing Network**
```yaml
# In your repository-monitor docker-compose.yml
networks:
  default:
    external: true
    name: ollama-network  # Your existing network name
```

**Option B: Create Shared Network**
```yaml
# Create a shared network
networks:
  shared-network:
    driver: bridge

# Reference in both compose files
services:
  repository-monitor:
    networks:
      - shared-network
```

#### 2. Service Dependencies

```yaml
services:
  repository-monitor:
    depends_on:
      - ollama-server-local
    external_links:
      - ollama-server-local:ollama
    environment:
      - OLLAMA_HOST=ollama-server-local
      - OLLAMA_PORT=11434
```

#### 3. Port Management

Avoid port conflicts by adjusting ports:

```yaml
services:
  repository-monitor:
    ports:
      - "5002:5000"  # Use different external port
```

### Example Integration Setup

Your existing `docker-compose.yml`:
```yaml
services:
  ollama-server-local:
    image: ollama/ollama
    ports:
      - "11434:11434"
    networks:
      - ollama-network

  llm-proxy-server-local:
    image: your-llm-proxy
    ports:
      - "11440:11440"
    networks:
      - ollama-network

networks:
  ollama-network:
    driver: bridge
```

RepoSense AI integration:
```yaml
services:
  repository-monitor:
    build: .
    ports:
      - "5001:5000"
    environment:
      - OLLAMA_HOST=ollama-server-local
      - OLLAMA_PORT=11434
    networks:
      - ollama-network
    depends_on:
      - ollama-server-local

networks:
  ollama-network:
    external: true
```

## SVN Server Integration

### VisualSVN Server

RepoSense AI works seamlessly with VisualSVN Server:

#### Configuration
```json
{
  "repositories": [
    {
      "id": "project-repo",
      "url": "http://your-svn-server:port/svn/repository-name",
      "username": "svn-user",
      "password": "svn-password",
      "enabled": true
    }
  ],
  "server_type": "visualsvn",
  "auto_detect_server_type": true
}
```

#### Authentication
- **Basic Auth**: Username/password in configuration
- **Windows Auth**: Use domain credentials
- **Certificate Auth**: Mount certificates in Docker container

### Apache SVN Server

For Apache-based SVN servers:

```json
{
  "server_type": "apache",
  "repositories": [
    {
      "url": "http://apache-svn-server/svn/repo",
      "username": "user",
      "password": "pass"
    }
  ]
}
```

### SVN+SSH Integration

For SVN over SSH:

```json
{
  "repositories": [
    {
      "url": "svn+ssh://user@server/path/to/repo",
      "ssh_key_path": "/app/keys/id_rsa"
    }
  ]
}
```

Mount SSH keys:
```yaml
volumes:
  - ./ssh-keys:/app/keys:ro
```

## AI Service Integration

### Ollama Integration

RepoSense AI integrates with Ollama for AI-powered documentation:

#### Local Ollama
```json
{
  "ollama": {
    "host": "localhost",
    "port": 11434,
    "model": "llama2"
  }
}
```

#### Remote Ollama
```json
{
  "ollama": {
    "host": "ollama-server.company.com",
    "port": 11434,
    "model": "codellama",
    "timeout": 120
  }
}
```

#### Ollama with Authentication
```json
{
  "ollama": {
    "host": "secure-ollama.company.com",
    "port": 443,
    "ssl": true,
    "api_key": "your-api-key"
  }
}
```

### Alternative AI Services

RepoSense AI's plugin architecture allows integration with other AI services:

#### OpenAI Integration (Future)
```json
{
  "ai_backend": "openai",
  "openai": {
    "api_key": "sk-...",
    "model": "gpt-4",
    "organization": "org-..."
  }
}
```

## Email Integration

### SMTP Configuration

```json
{
  "email": {
    "smtp_server": "smtp.company.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "email-password",
    "use_tls": true,
    "from_address": "RepoSense AI <<EMAIL>>"
  }
}
```

### Email Templates

Custom email templates can be mounted:

```yaml
volumes:
  - ./email-templates:/app/templates/email:ro
```

## Authentication Integration

### LDAP Integration (Future)

```json
{
  "authentication": {
    "type": "ldap",
    "ldap": {
      "server": "ldap://ldap.company.com",
      "base_dn": "dc=company,dc=com",
      "user_dn": "ou=users,dc=company,dc=com"
    }
  }
}
```

### OAuth Integration (Future)

```json
{
  "authentication": {
    "type": "oauth",
    "oauth": {
      "provider": "github",
      "client_id": "your-client-id",
      "client_secret": "your-client-secret"
    }
  }
}
```

## Monitoring and Logging

### External Logging

Forward logs to external systems:

```yaml
services:
  repository-monitor:
    logging:
      driver: "syslog"
      options:
        syslog-address: "tcp://log-server:514"
        tag: "repository-monitor"
```

### Prometheus Metrics (Future)

```yaml
services:
  repository-monitor:
    ports:
      - "9090:9090"  # Metrics endpoint
    environment:
      - ENABLE_METRICS=true
```

## Backup Integration

### Configuration Backup

```bash
# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker cp repository-monitor:/app/data/config.json ./backups/config_$DATE.json
```

### Database Backup (Future)

When database support is added:

```yaml
services:
  backup:
    image: postgres:13
    command: pg_dump -h db -U user dbname
    volumes:
      - ./backups:/backups
```

## Security Considerations

### Network Security
- Use internal networks for service communication
- Expose only necessary ports
- Implement proper firewall rules

### Secrets Management
- Use Docker secrets for sensitive data
- Mount secrets as files, not environment variables
- Rotate credentials regularly

### SSL/TLS
- Use HTTPS for web interface
- Implement certificate management
- Enable SSL for database connections

## Troubleshooting Integration

### Network Issues
```bash
# Test network connectivity
docker exec repository-monitor ping ollama-server-local

# Check network configuration
docker network inspect ollama-network
```

### Service Discovery
```bash
# Check service resolution
docker exec repository-monitor nslookup ollama-server-local

# Test service connectivity
docker exec repository-monitor curl http://ollama-server-local:11434/api/version
```

### Log Analysis
```bash
# View integration logs
docker-compose logs repository-monitor | grep -i "ollama\|integration\|connection"
```
