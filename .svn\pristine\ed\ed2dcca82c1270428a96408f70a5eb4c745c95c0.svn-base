#!/usr/bin/env python3
"""
Abstract base class for repository backend plugins
Defines the interface that all repository backends (SVN, Git, etc.) must implement
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from models import CommitInfo, RepositoryConfig


@dataclass
class RepositoryInfo:
    """Information about a discovered repository"""
    name: str
    url: str
    path: str
    last_revision: Optional[str] = None
    last_author: Optional[str] = None
    last_date: Optional[str] = None
    size: Optional[int] = None
    repository_type: str = ""  # 'svn', 'git', etc.


class RepositoryBackend(ABC):
    """Abstract base class for repository backends"""
    
    def __init__(self, config):
        self.config = config
        self.logger = None  # Will be set by subclasses
    
    @property
    @abstractmethod
    def backend_type(self) -> str:
        """Return the type of this backend (e.g., 'svn', 'git')"""
        pass
    
    @abstractmethod
    def get_latest_revision(self, repo: RepositoryConfig) -> Optional[str]:
        """
        Get the latest revision/commit identifier from the repository
        
        Args:
            repo: Repository configuration
            
        Returns:
            Latest revision identifier (string to support different formats)
            None if error occurred
        """
        pass
    
    @abstractmethod
    def get_commit_info(self, repo: RepositoryConfig, revision: str) -> Optional[CommitInfo]:
        """
        Get detailed information about a specific commit
        
        Args:
            repo: Repository configuration
            revision: Revision/commit identifier
            
        Returns:
            CommitInfo object with commit details, None if error occurred
        """
        pass
    
    @abstractmethod
    def get_diff(self, repo: RepositoryConfig, revision: str) -> Optional[str]:
        """
        Get the diff/changes for a specific commit
        
        Args:
            repo: Repository configuration
            revision: Revision/commit identifier
            
        Returns:
            Diff content as string, None if error occurred
        """
        pass
    
    @abstractmethod
    def test_connection(self, repo: RepositoryConfig) -> bool:
        """
        Test if the repository is accessible with the given configuration
        
        Args:
            repo: Repository configuration
            
        Returns:
            True if connection successful, False otherwise
        """
        pass
    
    @abstractmethod
    def discover_repositories(self, base_url: str, username: Optional[str] = None, 
                            password: Optional[str] = None, max_depth: int = 3) -> List[RepositoryInfo]:
        """
        Discover repositories from a server/base URL
        
        Args:
            base_url: Base server URL to search
            username: Optional username for authentication
            password: Optional password for authentication
            max_depth: Maximum depth to search
            
        Returns:
            List of discovered repositories
        """
        pass
    
    @abstractmethod
    def get_repository_info(self, repo: RepositoryConfig) -> Dict[str, Any]:
        """
        Get detailed information about a repository
        
        Args:
            repo: Repository configuration
            
        Returns:
            Dictionary with repository information
        """
        pass
    
    def validate_repository_config(self, repo: RepositoryConfig) -> List[str]:
        """
        Validate repository configuration for this backend
        
        Args:
            repo: Repository configuration to validate
            
        Returns:
            List of validation error messages (empty if valid)
        """
        errors = []
        
        if not repo.url:
            errors.append("Repository URL is required")
        
        if not repo.name:
            errors.append("Repository name is required")
        
        return errors
    
    def supports_authentication(self) -> bool:
        """
        Check if this backend supports username/password authentication
        
        Returns:
            True if authentication is supported
        """
        return True
    
    def supports_discovery(self) -> bool:
        """
        Check if this backend supports repository discovery
        
        Returns:
            True if discovery is supported
        """
        return True
    
    def get_command_base(self, repo: RepositoryConfig) -> List[str]:
        """
        Get base command with authentication for repository operations
        This is a helper method that backends can override
        
        Args:
            repo: Repository configuration
            
        Returns:
            List of command components
        """
        return []
