#!/usr/bin/env python3
"""
RepoSense AI Startup Script

This script:
1. Validates configuration
2. Checks Ollama connectivity
3. Provides helpful error messages
4. Starts the application
"""

import sys
import os
import subprocess
from pathlib import Path

def check_configuration():
    """Check if configuration is valid"""
    print("🔍 Checking configuration...")
    
    try:
        # Import our config tools
        sys.path.insert(0, str(Path(__file__).parent))
        from config_summary import get_effective_config
        
        result = get_effective_config()
        config = result['effective_config']
        
        print(f"✅ Config file: {result['config_file_path'] or 'Using defaults'}")
        print(f"✅ Ollama host: {config.get('ollama_host', 'NOT_SET')}")
        print(f"✅ Ollama model: {config.get('ollama_model', 'NOT_SET')}")
        
        # Check for common issues
        ollama_host = config.get('ollama_host', '')
        if 'ollama-server-local' in ollama_host:
            print("⚠️  Warning: ollama_host contains 'ollama-server-local'")
            print("   Consider setting OLLAMA_BASE_URL environment variable")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration check failed: {e}")
        return False

def check_ollama_connectivity():
    """Check if Ollama server is accessible"""
    print("\n🔗 Checking Ollama connectivity...")
    
    try:
        import requests
        from config_summary import get_effective_config
        
        result = get_effective_config()
        ollama_host = result['effective_config'].get('ollama_host', '')
        
        if not ollama_host:
            print("❌ No Ollama host configured")
            return False
        
        # Test connection
        url = f"{ollama_host}/api/tags"
        print(f"   Testing: {url}")
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            models = [m['name'] for m in data.get('models', [])]
            print(f"✅ Ollama server accessible")
            print(f"✅ Available models: {', '.join(models[:3])}{'...' if len(models) > 3 else ''}")
            
            # Check if configured model is available
            configured_model = result['effective_config'].get('ollama_model', '')
            if configured_model in models:
                print(f"✅ Configured model '{configured_model}' is available")
            else:
                print(f"⚠️  Configured model '{configured_model}' not found")
                print(f"   Available models: {', '.join(models)}")
                print(f"   Set OLLAMA_MODEL environment variable to use an available model")
            
            return True
        else:
            print(f"❌ Ollama server returned HTTP {response.status_code}")
            return False
            
    except requests.exceptions.ConnectTimeout:
        print(f"❌ Connection timeout - Ollama server not accessible")
        return False
    except requests.exceptions.ConnectionError:
        print(f"❌ Connection error - Ollama server not accessible")
        return False
    except Exception as e:
        print(f"❌ Ollama connectivity check failed: {e}")
        return False

def show_startup_info():
    """Show helpful startup information"""
    print("\n🚀 RepoSense AI Startup")
    print("=" * 50)
    
    # Show environment
    env = os.environ.get('REPOSENSE_AI_ENV', 'development')
    print(f"Environment: {env}")
    
    # Show key environment variables
    env_vars = ['OLLAMA_BASE_URL', 'OLLAMA_MODEL', 'REPOSENSE_AI_WEB_HOST', 'REPOSENSE_AI_WEB_PORT']
    active_env_vars = {var: os.environ.get(var) for var in env_vars if os.environ.get(var)}
    
    if active_env_vars:
        print("Active environment variables:")
        for var, value in active_env_vars.items():
            print(f"  {var}={value}")
    else:
        print("No environment overrides active")

def main():
    """Main startup function"""
    show_startup_info()
    
    # Check configuration
    if not check_configuration():
        print("\n❌ Configuration issues found. Please fix before starting.")
        print("\nTo debug configuration, run:")
        print("  python config_summary.py")
        sys.exit(1)
    
    # Check Ollama connectivity
    if not check_ollama_connectivity():
        print("\n⚠️  Ollama connectivity issues found.")
        print("The application will start but AI features may not work.")
        print("\nTo fix Ollama issues:")
        print("  1. Ensure Ollama server is running")
        print("  2. Check OLLAMA_BASE_URL environment variable")
        print("  3. Verify model availability with: docker exec -it ollama ollama list")
        
        # Ask if user wants to continue
        try:
            response = input("\nContinue anyway? (y/N): ").strip().lower()
            if response not in ['y', 'yes']:
                print("Startup cancelled.")
                sys.exit(1)
        except KeyboardInterrupt:
            print("\nStartup cancelled.")
            sys.exit(1)
    
    print("\n🎯 Starting RepoSense AI...")
    print("=" * 50)
    
    # Start the application
    try:
        # Import and start the main application
        from reposense_ai_app import main as app_main
        app_main()
    except KeyboardInterrupt:
        print("\n👋 RepoSense AI stopped by user")
    except Exception as e:
        print(f"\n❌ Application error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
