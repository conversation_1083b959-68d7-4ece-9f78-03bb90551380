#!/bin/bash
# RepoSense AI Binary Deployment Script for Linux
# This script sets up RepoSense AI in your existing Docker environment

set -e

echo "🚀 RepoSense AI Binary Deployment"
echo "================================="

# Check if we're in the right directory
if [ ! -f "repository_monitor_binary.py" ] && [ ! -f "repository_monitor_app.py" ]; then
    echo "❌ Error: RepoSense AI source files not found!"
    echo "Make sure you're running this from the RepoSense AI directory."
    exit 1
fi

# Create necessary directories
echo "📁 Creating deployment directories..."
mkdir -p deployment/repository-monitor
mkdir -p deployment/config

# Copy source files to deployment directory
echo "📋 Copying source files..."
cp -r . deployment/repository-monitor/
cd deployment/repository-monitor

# Create production Dockerfile for binary build
echo "🐳 Creating production Dockerfile..."
cat > Dockerfile << 'EOF'
# RepoSense AI - Linux Binary Production Build
FROM python:3.11-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    gcc g++ make libffi-dev libssl-dev \
    subversion git curl \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY . .

# Install Python dependencies and PyInstaller
RUN pip install --no-cache-dir -r requirements.txt && \
    pip install pyinstaller

# Build binary with optimizations
RUN pyinstaller --onefile \
    --name repository-monitor \
    --optimize=2 \
    --add-data "templates:templates" \
    --add-data "static:static" \
    --add-data "docs:docs" \
    --add-data "marketing:marketing" \
    --hidden-import=repository_backends.svn_backend \
    --hidden-import=repository_backends.git_backend \
    --hidden-import=repository_backends.base \
    --hidden-import=flask \
    --hidden-import=sqlite3 \
    --hidden-import=requests \
    --hidden-import=jinja2 \
    --hidden-import=werkzeug \
    --hidden-import=markupsafe \
    --collect-all=flask \
    --collect-all=jinja2 \
    --collect-all=werkzeug \
    --collect-all=markupsafe \
    --exclude-module=PyQt5 \
    --exclude-module=PySide6 \
    --exclude-module=PyQt6 \
    --exclude-module=PySide2 \
    --exclude-module=tkinter \
    --exclude-module=matplotlib \
    --exclude-module=numpy \
    --exclude-module=scipy \
    --exclude-module=pandas \
    --exclude-module=jupyter \
    repository_monitor_binary.py

# Stage 2: Minimal runtime
FROM ubuntu:22.04

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    subversion git curl ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd -r repository-monitor && \
    useradd -r -g repository-monitor -d /app -s /bin/bash repository-monitor

WORKDIR /app

# Copy binary and config
COPY --from=builder /app/dist/repository-monitor /app/repository-monitor
COPY --from=builder /app/config.example.json /app/config.example.json

# Create startup script
RUN cat > /app/start.sh << 'SCRIPT_EOF'
#!/bin/bash
set -e

# Create directories
mkdir -p /app/data /app/logs

# Initialize config if not exists
if [ ! -f /app/data/config.json ]; then
    echo "Initializing configuration..."
    cp /app/config.example.json /app/data/config.json
    echo "Configuration initialized."
fi

# Set environment variables
export REPOSITORY_MONITOR_CONFIG=/app/data/config.json
export REPOSITORY_MONITOR_DATA_DIR=/app/data
export REPOSITORY_MONITOR_LOG_DIR=/app/logs

# Start application
echo "Starting RepoSense AI..."
echo "Web interface: http://localhost:5000"
exec /app/repository-monitor
SCRIPT_EOF

# Set permissions
RUN chmod +x /app/start.sh /app/repository-monitor && \
    mkdir -p /app/data /app/logs && \
    chown -R repository-monitor:repository-monitor /app

USER repository-monitor
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Environment variables
ENV REPOSITORY_MONITOR_CONFIG=/app/data/config.json
ENV REPOSITORY_MONITOR_DATA_DIR=/app/data
ENV REPOSITORY_MONITOR_LOG_DIR=/app/logs
ENV PYTHONUNBUFFERED=1

CMD ["/app/start.sh"]
EOF

# Create docker-compose service definition
echo "📝 Creating docker-compose service..."
cat > ../docker-compose.repository-monitor.yml << 'EOF'
version: '3.8'

services:
  repository-monitor:
    container_name: repository-monitor
    build:
      context: ./repository-monitor
      dockerfile: Dockerfile
    restart: unless-stopped
    networks:
      - ollama-network
    ports:
      - "5000:5000"
    volumes:
      # Persistent data
      - repository_monitor_data:/app/data
      - repository_monitor_logs:/app/logs
      
      # Production config (optional override)
      - ./config/config.production.json:/app/data/config.json:ro
      
      # SVN credentials (if needed)
      # - ~/.subversion:/home/<USER>/.subversion:ro
      
      # Git credentials (if needed)
      # - ~/.gitconfig:/home/<USER>/.gitconfig:ro
    
    environment:
      - REPOSITORY_MONITOR_CONFIG=/app/data/config.json
      - REPOSITORY_MONITOR_DATA_DIR=/app/data
      - REPOSITORY_MONITOR_LOG_DIR=/app/logs
      - FLASK_ENV=production
      - FLASK_DEBUG=false
      - OLLAMA_BASE_URL=http://ollama:11434
      - PYTHONUNBUFFERED=1
    
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    depends_on:
      - ollama

networks:
  ollama-network:
    external: true

volumes:
  repository_monitor_data:
  repository_monitor_logs:
EOF

# Copy production config
echo "⚙️ Setting up production configuration..."
cd ../config
cp ../../config.production.json ./

echo ""
echo "✅ Deployment setup completed!"
echo ""
echo "📁 Deployment structure:"
echo "   deployment/"
echo "   ├── repository-monitor/     (source code + Dockerfile)"
echo "   ├── config/"
echo "   │   └── config.production.json"
echo "   └── docker-compose.repository-monitor.yml"
echo ""
echo "🚀 Next steps:"
echo "   1. Customize deployment/config/config.production.json"
echo "   2. cd deployment"
echo "   3. docker-compose -f docker-compose.repository-monitor.yml build"
echo "   4. docker-compose -f docker-compose.repository-monitor.yml up -d"
echo ""
echo "🌐 Access: http://your-server:5000"
echo "📊 Health: http://your-server:5000/health"
