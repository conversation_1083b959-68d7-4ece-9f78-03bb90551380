@echo off
REM RepoSense AI Binary Builder for Windows
REM Simple batch file to build the binary

echo.
echo ========================================
echo RepoSense AI Binary Builder
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.11+ and try again
    pause
    exit /b 1
)

echo Python found: 
python --version

REM Check if we're in the right directory
if not exist "reposense_ai_binary.py" (
    if not exist "reposense_ai_app.py" (
        echo ERROR: Entry point files not found!
        echo Make sure you're running this from the project root directory
        pause
        exit /b 1
    )
)

echo.
echo Installing/updating dependencies...
python -m pip install --upgrade pip
python -m pip install -r requirements.txt

echo.
echo Building binary...
python build-binary-simple.py

if errorlevel 1 (
    echo.
    echo BUILD FAILED!
    echo Check the error messages above
    pause
    exit /b 1
)

echo.
echo ========================================
echo BUILD COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Binary location: dist\reposense-ai.exe
echo.
echo To run the application:
echo   cd dist
echo   start-reposense-ai.bat
echo.
echo First run will create a config.json file for customization.
echo.
pause
