# Hot Reload Development Guide

This guide covers the hot reload development setup for efficient SVN Monitor development.

## Quick Start

```powershell
# Start development environment with hot reload
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f svn-monitor

# Access web interface
# http://localhost:5000
```

## Hot Reload Features

### ✅ What Auto-Reloads

| File Type | Reload Method | Restart Required |
|-----------|---------------|------------------|
| **Templates** (`*.html`) | Immediate | ❌ No |
| **Static Files** (`*.css`, `*.js`) | Immediate | ❌ No |
| **Python Files** (`*.py`) | Auto-restart | ⚡ Automatic |
| **Configuration** (`config.json`) | Dynamic | ❌ No |

### Volume Mounts Configuration

```yaml
volumes:
  # Templates and static files (immediate reload)
  - ./templates:/app/templates
  - ./static:/app/static
  
  # Python source files (auto-restart)
  - ./svn_monitor_app.py:/app/svn_monitor_app.py
  - ./config_manager.py:/app/config_manager.py
  - ./models.py:/app/models.py
  - ./web_interface.py:/app/web_interface.py
  - ./svn_client.py:/app/svn_client.py
  - ./ollama_client.py:/app/ollama_client.py
  - ./email_service.py:/app/email_service.py
  - ./file_manager.py:/app/file_manager.py
  - ./monitor_service.py:/app/monitor_service.py
  - ./user_service.py:/app/user_service.py
  - ./repository_service.py:/app/repository_service.py
  
  # Configuration and data
  - ./config.json:/app/config.json
  - ./data:/app/data
  - ./logs:/app/logs
```

## Development Workflow

### 1. Template Development
```bash
# Edit template
vim templates/index.html

# 🔥 Changes appear immediately!
# Just refresh browser - no restart needed
```

### 2. Python Code Development
```bash
# Edit Python file
vim web_interface.py

# ⚡ Flask automatically restarts
# Watch logs: "Restarting with stat"
docker-compose -f docker-compose.dev.yml logs -f svn-monitor
```

### 3. Static File Development
```bash
# Edit CSS/JS
vim static/css/style.css

# 🔥 Changes appear immediately!
# Just refresh browser
```

## Debug Features

### Flask Debug Mode
- **Enabled**: `FLASK_DEBUG=1`
- **Features**:
  - Detailed error pages with stack traces
  - Interactive debugger in browser
  - Auto-reloader for Python files
  - Template auto-reload

### Debug Information
```bash
# View debug logs
docker-compose -f docker-compose.dev.yml logs svn-monitor | grep -i debug

# Check debugger PIN (for interactive debugging)
docker-compose -f docker-compose.dev.yml logs svn-monitor | grep "Debugger PIN"
```

## Testing Hot Reload

### Test Template Changes
1. Edit `templates/index.html`
2. Add a visible change (e.g., modify page title)
3. Refresh browser - change should appear immediately

### Test Python Changes
1. Edit `web_interface.py`
2. Add a log message or modify a route
3. Watch logs for "Restarting with stat"
4. Test the change in browser

### Test Static File Changes
1. Edit `static/css/style.css`
2. Add a visible style change
3. Refresh browser - change should appear immediately

## Troubleshooting

### Hot Reload Not Working

#### Check Volume Mounts
```bash
# Verify files are mounted correctly
docker-compose -f docker-compose.dev.yml exec svn-monitor ls -la /app/templates/
docker-compose -f docker-compose.dev.yml exec svn-monitor ls -la /app/
```

#### Check File Permissions
```bash
# Files should be readable by container
docker-compose -f docker-compose.dev.yml exec svn-monitor cat /app/templates/index.html
```

#### Restart Container
```bash
# If hot reload stops working
docker-compose -f docker-compose.dev.yml restart svn-monitor
```

### Container Issues

#### View Container Status
```bash
docker-compose -f docker-compose.dev.yml ps
```

#### Check Logs
```bash
# Real-time logs
docker-compose -f docker-compose.dev.yml logs -f svn-monitor

# Last 20 lines
docker-compose -f docker-compose.dev.yml logs --tail=20 svn-monitor
```

#### Rebuild Container
```bash
# If major changes or issues
docker-compose -f docker-compose.dev.yml down
docker-compose -f docker-compose.dev.yml build --no-cache
docker-compose -f docker-compose.dev.yml up -d
```

## Development Commands

### Essential Commands
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Stop development environment
docker-compose -f docker-compose.dev.yml down

# Restart specific service
docker-compose -f docker-compose.dev.yml restart svn-monitor

# View logs
docker-compose -f docker-compose.dev.yml logs -f svn-monitor

# Access container shell
docker-compose -f docker-compose.dev.yml exec svn-monitor bash
```

### Debugging Commands
```bash
# Check container health
docker-compose -f docker-compose.dev.yml exec svn-monitor curl -f http://localhost:5000 || echo "Health check failed"

# Test Python imports
docker-compose -f docker-compose.dev.yml exec svn-monitor python -c "import web_interface; print('OK')"

# Check Flask debug mode
docker-compose -f docker-compose.dev.yml exec svn-monitor python -c "import os; print('Debug:', os.getenv('FLASK_DEBUG'))"
```

## Integration with Existing Docker Stack

The development environment integrates seamlessly with your existing services:

| Service | Container | URL | Status |
|---------|-----------|-----|--------|
| **SVN Monitor** | `svn-monitor-dev` | http://localhost:5000 | 🔥 Hot Reload |
| **Open WebUI** | `open-webui-local` | http://localhost:3000 | ✅ Running |
| **LLM Proxy** | `llm-proxy-server-local` | http://localhost:11440 | ✅ Running |
| **Ollama Server** | `ollama-server-local` | http://localhost:11434 | ✅ Running |

### Network Configuration
- Uses existing `ollama-network`
- Connects to existing `ollama-server-local`
- No conflicts with existing services

## Performance Tips

1. **Selective Editing**: Only edit files you need to change
2. **Log Management**: Use `--tail` to limit log output
3. **Browser Cache**: Disable cache during development (F12 → Network → Disable cache)
4. **Container Resources**: Monitor Docker Desktop resource usage

## Performance Optimizations

The development environment includes several performance optimizations:

1. **Ollama Connection Caching**: Connection status is cached for 30 seconds to avoid slow dashboard loads
2. **Ollama Models Caching**: Available models are cached for 5 minutes to reduce API calls
3. **Reduced Timeouts**: Connection tests use 2-second timeouts instead of 5 seconds
4. **Performance Monitoring**: Load times are tracked and logged for debugging
5. **API Endpoints**: Performance metrics available at `/api/performance`

### Performance Metrics

- Dashboard loads in ~50ms (vs 4+ seconds before optimization)
- API status calls load in ~0.01ms when cached
- Ollama connection tests complete in <2 seconds
- Models API loads in ~50ms initially, then cached for 5 minutes

## Dynamic Model Selection

The configuration interface now dynamically queries your Ollama server for available models:

### Features

1. **Automatic Model Discovery**: Queries `/api/tags` endpoint to get available models
2. **Real-time Updates**: Refresh button to reload models without page refresh
3. **Caching**: Models are cached for 5 minutes to improve performance
4. **Fallback Support**: Graceful handling when Ollama is not connected
5. **Visual Feedback**: Loading spinners and status messages

### API Endpoints

- `GET /api/ollama/models` - Returns available models with connection status
- Response format:
  ```json
  {
    "connected": true,
    "count": 7,
    "models": ["deepseek-r1:1.5b", "gemma3:4b", "llama3.2:1b", ...]
  }
  ```

### Configuration Interface

The model dropdown in the configuration page:
- Automatically loads available models on page load
- Shows loading spinner while fetching models
- Displays status messages (connected, error, etc.)
- Includes refresh button to reload models
- Falls back to cached models if API fails

## Production Deployment

When ready for production:
```bash
# Stop development environment
docker-compose -f docker-compose.dev.yml down

# Deploy production version
docker-compose -f docker-compose.yml up -d
```

The hot reload setup is perfect for development but should not be used in production!

## Common Issues Fixed

### Configuration Page Becomes Unresponsive

**Issue**: Page greys out and becomes unresponsive when unchecking "Send Email Notifications"

**Root Cause**: JavaScript selector was using `.card:last-of-type .card-body` which targeted the entire form instead of just the email settings section.

**Solution**:
1. Added specific ID `email-settings-body` to the email settings card body
2. Updated JavaScript to use `document.getElementById('email-settings-body')`
3. Added error handling and console logging for debugging
4. Improved visual feedback with `pointer-events` and cursor styling

**Prevention**: Always use specific element IDs instead of generic CSS selectors for JavaScript interactions.

## SVN Server Configuration and Repository Discovery

The system now supports configuring an SVN server connection and browsing all repositories on that server:

### Configuration Workflow

1. **Configure SVN Server** (in Settings page):
   - Set SVN Server Base URL (e.g., `http://sundc:81/svn`)
   - Set default username/password for server access
   - These settings are used as defaults for repository discovery

2. **Discover Repositories**:
   - Click "Discover Repositories" button from Settings page
   - Repository discovery form is pre-populated with server settings
   - Scan the server to find all available repositories
   - Import selected repositories for monitoring

3. **Repository Management**:
   - Individual repositories are managed in the Repositories section
   - Each repository can have its own credentials
   - Repository-specific user assignments and email settings

### Key Features

- **Server-level Configuration**: Set base SVN server URL and default credentials
- **Automatic Discovery**: Recursively scan SVN server for repositories
- **Standard Layout Detection**: Identifies repositories by trunk/branches/tags structure
- **Flexible Authentication**: Server defaults with per-repository overrides
- **User-friendly Workflow**: Seamless integration between configuration and discovery
