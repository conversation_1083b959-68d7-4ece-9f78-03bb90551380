{"_comment": "SVN Monitor Development Configuration for Docker Desktop on Windows", "_description": "This configuration is optimized for development with Docker Desktop", "repositories": [{"_comment": "Example development repository - replace with your actual repo", "id": "dev-repo-1", "name": "Development Test Repository", "url": "https://svn.example.com/repos/test-project", "username": "your-dev-username", "password": "your-dev-password", "enabled": false, "last_revision": 0, "branch_path": "trunk", "monitor_all_branches": false, "assigned_users": ["dev-admin-user"], "email_recipients": ["developer@localhost"]}], "_users_section": "=== User Management ===", "users": [{"_comment": "Development admin user", "id": "dev-admin-user", "username": "dev-admin", "email": "admin@localhost", "full_name": "Development Administrator", "role": "admin", "enabled": true, "receive_all_notifications": true, "repository_subscriptions": [], "phone": "******-0100", "department": "Development", "created_date": "2024-01-01T00:00:00Z", "last_modified": "2024-01-01T00:00:00Z"}, {"_comment": "Development test user", "id": "dev-test-user", "username": "dev-tester", "email": "tester@localhost", "full_name": "Test Developer", "role": "developer", "enabled": true, "receive_all_notifications": false, "repository_subscriptions": ["dev-repo-1"], "phone": "******-0101", "department": "QA", "created_date": "2024-01-01T00:00:00Z", "last_modified": "2024-01-01T00:00:00Z"}], "_users_note": "User roles: admin, manager, developer, viewer", "_ollama_section": "=== Ollama AI Configuration ===", "ollama_host": "http://ollama-server-local:11434", "ollama_model": "llama2", "_ollama_note": "Using existing Ollama server from your Docker Compose setup", "_monitoring_section": "=== Monitoring Settings ===", "check_interval": 60, "_check_interval_note": "Faster interval for development (60 seconds)", "_email_section": "=== Email Configuration ===", "_email_note": "For development, you can use a local SMTP server or disable email", "smtp_host": "localhost", "smtp_port": 1025, "smtp_username": "", "smtp_password": "", "email_from": "svn-monitor@localhost", "use_tls": false, "_email_dev_note": "Consider using MailHog or similar for development email testing", "_web_section": "=== Web Interface Configuration ===", "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "development-secret-key-change-in-production", "_web_note": "Web interface accessible from host machine at localhost:5000", "_logging_section": "=== Logging Configuration ===", "log_level": "DEBUG", "log_file": "/app/data/logs/svn_monitor_dev.log", "_log_note": "Debug level logging for development", "_development_section": "=== Development Specific Settings ===", "_dev_note": "These settings are optimized for development workflow", "enable_debug_mode": true, "auto_reload": true, "detailed_logging": true}