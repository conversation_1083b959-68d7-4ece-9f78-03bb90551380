#!/bin/bash
# Setup SVN configuration for RepoSense AI

echo "Setting up SVN configuration..."

# Create .subversion directory
mkdir -p /home/<USER>/.subversion

# Copy credentials if mounted (optional)
if [ -d "/host-svn-credentials" ]; then
    echo "Copying SVN credentials from host..."
    cp -r /host-svn-credentials/* /home/<USER>/.subversion/ 2>/dev/null || true
fi

# Create or update SVN servers configuration for SSL handling
echo "Configuring SVN for SSL certificate handling..."
cat > /home/<USER>/.subversion/servers << 'EOF'
[global]
# Global server configuration options
http-timeout = 60
http-compression = yes
neon-debug-mask = 0
http-auth-types = basic;digest;negotiate
ssl-authority-files =
ssl-trust-default-ca = yes
ssl-client-cert-file =
ssl-client-cert-password =
store-passwords = yes
store-plaintext-passwords = yes

# SSL certificate handling - accept all certificate issues
ssl-ignore-unknown-ca = yes
ssl-ignore-invalid-date = yes
ssl-ignore-host-mismatch = yes

[groups]
# You can define server groups here

EOF

# Set proper ownership and permissions
chown appuser:appuser /home/<USER>/.subversion/servers
chmod 644 /home/<USER>/.subversion/servers

# Continue with the original command
exec "$@"
