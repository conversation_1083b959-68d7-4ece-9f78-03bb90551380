# Quick Start Script for RepoSense AI
# This script starts only the RepoSense AI, assuming your main Docker Compose is already running

param(
    [switch]$Build,
    [switch]$Logs,
    [switch]$Stop,
    [switch]$Status
)

$ErrorActionPreference = "Stop"

Write-Host "RepoSense AI Quick Start" -ForegroundColor Cyan
Write-Host "========================" -ForegroundColor Cyan

# Check if main services are running
Write-Host "Checking existing Docker services..." -ForegroundColor Yellow
$ollamaRunning = docker ps --format "table {{.Names}}" | Select-String "ollama-server-local"
if (-not $ollamaRunning) {
    Write-Host "❌ Error: ollama-server-local not found!" -ForegroundColor Red
    Write-Host "Please start your main docker-compose.yml first:" -ForegroundColor Yellow
    Write-Host "  docker-compose up -d" -ForegroundColor White
    exit 1
}

Write-Host "✓ Found ollama-server-local" -ForegroundColor Green

# Handle different actions
if ($Stop) {
    Write-Host "Stopping RepoSense AI..." -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml down
    Write-Host "✓ RepoSense AI stopped" -ForegroundColor Green
    exit 0
}

if ($Status) {
    Write-Host "RepoSense AI Status:" -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml ps

    Write-Host "`nService URLs:" -ForegroundColor Yellow
    Write-Host "  RepoSense AI: http://localhost:5001" -ForegroundColor White
    Write-Host "  Open WebUI:  http://localhost:3000" -ForegroundColor White
    Write-Host "  LLM Proxy:   http://localhost:11440" -ForegroundColor White
    Write-Host "  Ollama API:  http://localhost:11434" -ForegroundColor White
    exit 0
}

if ($Build) {
    Write-Host "Building RepoSense AI..." -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml build --no-cache
}

# Check if config exists in data directory
if (-not (Test-Path "data/config.json")) {
    Write-Host "❌ Error: No configuration file found!" -ForegroundColor Red
    Write-Host "Please run setup.sh first to create data/config.json" -ForegroundColor Yellow
    Write-Host "Or create data/config.json manually with your repository settings" -ForegroundColor Yellow
    exit 1
}

# Start RepoSense AI
Write-Host "Starting RepoSense AI..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml up -d

# Wait for startup
Write-Host "Waiting for RepoSense AI to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check status
$status = docker-compose -f docker-compose.dev.yml ps reposense-ai
if ($status -match "Up") {
    Write-Host "✓ RepoSense AI started successfully!" -ForegroundColor Green
    Write-Host "`nService URLs:" -ForegroundColor Cyan
    Write-Host "  RepoSense AI: http://localhost:5000" -ForegroundColor White
    Write-Host "  Open WebUI:  http://localhost:3000" -ForegroundColor White

    if ($Logs) {
        Write-Host "`nShowing logs (Ctrl+C to exit):" -ForegroundColor Yellow
        docker-compose -f docker-compose.dev.yml logs -f reposense-ai
    } else {
        Write-Host "`nTo view logs: .\start-reposense-ai.ps1 -Logs" -ForegroundColor Yellow
        Write-Host "To stop:      .\start-reposense-ai.ps1 -Stop" -ForegroundColor Yellow
        Write-Host "To rebuild:   .\start-reposense-ai.ps1 -Build" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Error: RepoSense AI failed to start!" -ForegroundColor Red
    Write-Host "Check logs with: docker-compose -f docker-compose.dev.yml logs reposense-ai" -ForegroundColor Yellow
    exit 1
}
