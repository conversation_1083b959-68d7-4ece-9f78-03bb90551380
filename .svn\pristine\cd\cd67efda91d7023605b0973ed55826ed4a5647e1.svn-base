"""
Document Service for Repository Monitor

Manages generated documentation files and provides web interface access.
"""

import os
import re
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import re


@dataclass
class Document:
    """Represents a generated document"""
    id: str
    repository_id: str
    repository_name: str
    revision: int
    date: datetime
    filename: str
    filepath: str
    size: int
    author: str
    commit_message: str
    code_review_recommended: Optional[bool] = None
    code_review_priority: Optional[str] = None  # "HIGH", "MEDIUM", "LOW"
    documentation_impact: Optional[bool] = None
    risk_level: Optional[str] = None  # "HIGH", "MEDIUM", "LOW"
    
    @property
    def display_name(self) -> str:
        """Get a user-friendly display name"""
        return f"Revision {self.revision} - {self.date.strftime('%Y-%m-%d %H:%M')}"
    
    @property
    def relative_path(self) -> str:
        """Get relative path from output directory"""
        return str(Path(self.filepath).relative_to(Path("/app/data/output")))


class DocumentService:
    """Service for managing generated documents"""
    
    def __init__(self, output_dir: str = "/app/data/output"):
        self.output_dir = Path(output_dir)
        self.logger = logging.getLogger(__name__)
        
    def scan_documents(self) -> List[Document]:
        """Scan the output directory for generated documents"""
        documents = []
        
        try:
            repositories_dir = self.output_dir / "repositories"
            if not repositories_dir.exists():
                return documents
                
            for repo_dir in repositories_dir.iterdir():
                if repo_dir.is_dir():
                    docs_dir = repo_dir / "docs"
                    if docs_dir.exists():
                        documents.extend(self._scan_repository_documents(repo_dir.name, docs_dir))
                        
        except Exception as e:
            self.logger.error(f"Error scanning documents: {e}")
            
        # Sort by date (newest first)
        documents.sort(key=lambda d: d.date, reverse=True)
        return documents
    
    def _scan_repository_documents(self, repo_id: str, docs_dir: Path) -> List[Document]:
        """Scan documents for a specific repository"""
        documents = []
        
        try:
            for doc_file in docs_dir.glob("*.md"):
                if doc_file.is_file():
                    doc = self._parse_document_file(repo_id, doc_file)
                    if doc:
                        documents.append(doc)
                        
        except Exception as e:
            self.logger.error(f"Error scanning repository documents in {docs_dir}: {e}")
            
        return documents
    
    def _parse_document_file(self, repo_id: str, doc_file: Path) -> Optional[Document]:
        """Parse a document file and extract metadata"""
        try:
            # Parse filename to extract revision and date
            # Expected format: revision_X_YYYY-MM-DD.md
            filename_match = re.match(r'revision_(\d+)_(\d{4}-\d{2}-\d{2})\.md', doc_file.name)
            if not filename_match:
                return None
                
            revision = int(filename_match.group(1))
            date_str = filename_match.group(2)
            
            # Read file content to extract metadata
            with open(doc_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Parse metadata from content
            repo_name = self._extract_field(content, "Repository")
            author = self._extract_field(content, "Author")
            commit_message = self._extract_field(content, "Message")
            date_field = self._extract_field(content, "Date")
            
            # Parse date
            doc_date = datetime.now()
            if date_field:
                try:
                    # Try parsing ISO format first
                    doc_date = datetime.fromisoformat(date_field.replace('Z', '+00:00'))
                except ValueError:
                    try:
                        # Fallback to date from filename
                        doc_date = datetime.strptime(date_str, '%Y-%m-%d')
                    except ValueError:
                        doc_date = datetime.now()
            
            # Extract LLM analysis metadata
            code_review_recommended = self._extract_code_review_recommendation(content)
            code_review_priority = self._extract_code_review_priority(content)
            documentation_impact = self._extract_documentation_impact(content)
            risk_level = self._extract_risk_level(content)

            return Document(
                id=f"{repo_id}_{revision}_{date_str}",
                repository_id=repo_id,
                repository_name=repo_name or repo_id.replace('-', ' ').title(),
                revision=revision,
                date=doc_date,
                filename=doc_file.name,
                filepath=str(doc_file),
                size=doc_file.stat().st_size,
                author=author or "Unknown",
                commit_message=commit_message or "No message",
                code_review_recommended=code_review_recommended,
                code_review_priority=code_review_priority,
                documentation_impact=documentation_impact,
                risk_level=risk_level
            )
            
        except Exception as e:
            self.logger.error(f"Error parsing document file {doc_file}: {e}")
            return None
    
    def _extract_field(self, content: str, field_name: str) -> Optional[str]:
        """Extract a field value from document content"""
        pattern = rf'\*\*{field_name}:\*\*\s*(.+?)(?:\n|$)'
        match = re.search(pattern, content)
        return match.group(1).strip() if match else None
    
    def get_document_by_id(self, doc_id: str) -> Optional[Document]:
        """Get a specific document by ID"""
        documents = self.scan_documents()
        return next((doc for doc in documents if doc.id == doc_id), None)
    
    def get_document_content(self, doc_id: str) -> Optional[str]:
        """Get the content of a specific document"""
        document = self.get_document_by_id(doc_id)
        if not document:
            return None

        try:
            with open(document.filepath, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception as e:
            self.logger.error(f"Error reading document content {document.filepath}: {e}")
            return None

    def _extract_code_review_recommendation(self, content: str) -> Optional[bool]:
        """Extract code review recommendation from LLM analysis"""
        try:
            # Look for code review recommendation section
            review_section = self._extract_section(content, "Code Review Recommendation")
            if not review_section:
                return None

            review_lower = review_section.lower()
            if "not required" in review_lower or "no review" in review_lower or "skip review" in review_lower:
                return False
            elif ("recommended" in review_lower or "should be reviewed" in review_lower or
                  "requires review" in review_lower or "priority" in review_lower):
                return True

            return None
        except Exception:
            return None

    def _extract_code_review_priority(self, content: str) -> Optional[str]:
        """Extract code review priority from LLM analysis"""
        try:
            review_section = self._extract_section(content, "Code Review Recommendation")
            if not review_section:
                return None

            review_lower = review_section.lower()
            if "high priority" in review_lower or "urgent" in review_lower:
                return "HIGH"
            elif "medium priority" in review_lower or "moderate" in review_lower:
                return "MEDIUM"
            elif "low priority" in review_lower or "optional" in review_lower:
                return "LOW"

            return None
        except Exception:
            return None

    def _extract_documentation_impact(self, content: str) -> Optional[bool]:
        """Extract documentation impact from LLM analysis"""
        try:
            doc_section = self._extract_section(content, "Documentation Impact")
            if not doc_section:
                return None

            doc_lower = doc_section.lower()
            if "not required" in doc_lower or "no updates" in doc_lower or "no impact" in doc_lower:
                return False
            elif "required" in doc_lower or "should be updated" in doc_lower or "needs update" in doc_lower:
                return True

            return None
        except Exception:
            return None

    def _extract_risk_level(self, content: str) -> Optional[str]:
        """Extract risk level from LLM analysis"""
        try:
            # Look in multiple sections for risk level
            sections = ["Code Review Recommendation", "Impact Assessment", "Summary"]

            for section_name in sections:
                section = self._extract_section(content, section_name)
                if section:
                    section_lower = section.lower()
                    if "high risk" in section_lower or "risk level: high" in section_lower:
                        return "HIGH"
                    elif "medium risk" in section_lower or "risk level: medium" in section_lower:
                        return "MEDIUM"
                    elif "low risk" in section_lower or "risk level: low" in section_lower:
                        return "LOW"

            return None
        except Exception:
            return None

    def _extract_section(self, content: str, section_name: str) -> Optional[str]:
        """Extract a specific section from the document content"""
        try:
            # Look for markdown section headers
            pattern = rf'##\s*{re.escape(section_name)}\s*\n(.*?)(?=\n##|\Z)'
            match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
            return match.group(1).strip() if match else None
        except Exception:
            return None
    
    def get_documents_by_repository(self, repo_id: str) -> List[Document]:
        """Get all documents for a specific repository"""
        documents = self.scan_documents()
        return [doc for doc in documents if doc.repository_id == repo_id]
    
    def get_repository_stats(self) -> Dict[str, int]:
        """Get statistics about documents per repository"""
        documents = self.scan_documents()
        stats = {}
        
        for doc in documents:
            if doc.repository_id not in stats:
                stats[doc.repository_id] = 0
            stats[doc.repository_id] += 1
            
        return stats
    
    def delete_document(self, doc_id: str) -> bool:
        """Delete a document file"""
        document = self.get_document_by_id(doc_id)
        if not document:
            return False
            
        try:
            os.remove(document.filepath)
            self.logger.info(f"Deleted document: {document.filepath}")
            return True
        except Exception as e:
            self.logger.error(f"Error deleting document {document.filepath}: {e}")
            return False
