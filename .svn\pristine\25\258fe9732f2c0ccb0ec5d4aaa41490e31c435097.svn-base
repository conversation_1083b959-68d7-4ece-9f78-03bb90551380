# Repository Monitor - Container Registry Deployment Guide

Deploy Repository Monitor to public container registries for easy distribution and deployment.

## 🎯 **Deployment Options**

### **1. Docker Hub (Most Popular)**
- **Registry**: `docker.io`
- **Public Access**: Free for public repositories
- **Multi-architecture**: AMD64 + ARM64 support
- **Auto-builds**: GitHub integration available

### **2. GitHub Container Registry**
- **Registry**: `ghcr.io`
- **Integration**: Native GitHub integration
- **Private/Public**: Free for public repositories
- **Actions**: Automated builds with GitHub Actions

### **3. Other Registries**
- **AWS ECR**: `public.ecr.aws`
- **Google Container Registry**: `gcr.io`
- **Azure Container Registry**: `azurecr.io`

## 🚀 **Quick Deployment**

### **Deploy to Docker Hub**

```bash
# Set your Docker Hub username
export DOCKER_USERNAME="your-dockerhub-username"

# Make script executable and run
chmod +x deploy-to-dockerhub.sh
./deploy-to-dockerhub.sh
```

### **Deploy to GitHub Container Registry**

```bash
# Set your GitHub credentials
export GITHUB_USERNAME="your-github-username"
export GITHUB_TOKEN="your-github-personal-access-token"

# Make script executable and run
chmod +x deploy-to-github-registry.sh
./deploy-to-github-registry.sh
```

## 📋 **Prerequisites**

### **Docker Hub Deployment**
1. **Docker Hub Account**: Create account at https://hub.docker.com
2. **Docker Desktop**: Install Docker with buildx support
3. **Repository**: Create public repository on Docker Hub

### **GitHub Container Registry**
1. **GitHub Account**: With repository access
2. **Personal Access Token**: With `write:packages` permission
3. **Docker**: With buildx support for multi-architecture builds

### **GitHub Actions (Automated)**
1. **Repository Secrets**: Set up required secrets
2. **Workflow File**: Place `github-actions-deploy.yml` in `.github/workflows/`
3. **Permissions**: Enable Actions and Packages permissions

## 🔧 **Manual Deployment Steps**

### **Step 1: Prepare Repository**

```bash
# Ensure you have the required Dockerfiles
ls -la Dockerfile*

# Required files:
# - Dockerfile.linux-binary (optimized binary build)
# - Dockerfile.production (source-based build)
# - repository_monitor_binary.py (entry point)
```

### **Step 2: Build Multi-Architecture Images**

```bash
# Create buildx builder
docker buildx create --name repository-monitor-builder --use

# Build for multiple architectures
docker buildx build \
    --platform linux/amd64,linux/arm64 \
    --file Dockerfile.linux-binary \
    --tag your-username/repository-monitor:latest \
    --tag your-username/repository-monitor:v2.1.0 \
    --push \
    .
```

### **Step 3: Verify Deployment**

```bash
# Check image details
docker buildx imagetools inspect your-username/repository-monitor:latest

# Test the image
docker run -d -p 5000:5000 your-username/repository-monitor:latest
curl http://localhost:5000/health
```

## 🏷️ **Image Tags Strategy**

### **Recommended Tags**
- `latest` - Latest stable binary version
- `v2.1.0` - Specific version binary
- `binary` - Latest binary build
- `source` - Latest source-based build
- `v2.1.0-source` - Specific version source build

### **Tag Examples**
```bash
# Docker Hub
docker.io/username/repository-monitor:latest
docker.io/username/repository-monitor:v2.1.0
docker.io/username/repository-monitor:binary

# GitHub Container Registry
ghcr.io/username/repository-monitor:latest
ghcr.io/username/repository-monitor:v2.1.0
ghcr.io/username/repository-monitor:binary
```

## 🤖 **Automated Deployment with GitHub Actions**

### **Setup GitHub Actions**

1. **Create Workflow File**:
   ```bash
   mkdir -p .github/workflows
   cp github-actions-deploy.yml .github/workflows/docker-deploy.yml
   ```

2. **Configure Repository Secrets** (if using Docker Hub):
   - `DOCKERHUB_USERNAME`: Your Docker Hub username
   - `DOCKERHUB_TOKEN`: Docker Hub access token

3. **Enable Permissions**:
   - Go to repository Settings → Actions → General
   - Enable "Read and write permissions" for GITHUB_TOKEN

### **Workflow Triggers**
- **Push to main/master**: Builds and pushes `latest` tag
- **Create tag**: Builds and pushes version-specific tags
- **Release**: Deploys to production environment
- **Pull Request**: Builds for testing (no push)

### **Workflow Features**
- ✅ Multi-architecture builds (AMD64 + ARM64)
- ✅ Security scanning with Trivy
- ✅ Automated staging deployment
- ✅ Production deployment on release
- ✅ Docker Hub description updates

## 📦 **Using Published Images**

### **Docker Run**
```bash
# Latest binary version
docker run -d -p 5000:5000 \
    -v repository_data:/app/data \
    -v repository_logs:/app/logs \
    your-username/repository-monitor:latest

# With configuration
docker run -d -p 5000:5000 \
    -v $(pwd)/config.json:/app/data/config.json:ro \
    -v repository_data:/app/data \
    your-username/repository-monitor:latest
```

### **Docker Compose**
```yaml
# Use docker-compose.public.yml
version: '3.8'
services:
  repository-monitor:
    image: your-username/repository-monitor:latest
    ports:
      - "5000:5000"
    volumes:
      - repository_data:/app/data
      - ./config.production.json:/app/data/config.json:ro
```

### **Kubernetes**
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: repository-monitor
spec:
  replicas: 1
  selector:
    matchLabels:
      app: repository-monitor
  template:
    metadata:
      labels:
        app: repository-monitor
    spec:
      containers:
      - name: repository-monitor
        image: your-username/repository-monitor:latest
        ports:
        - containerPort: 5000
        volumeMounts:
        - name: config
          mountPath: /app/data/config.json
          subPath: config.json
```

## 🔒 **Security Considerations**

### **Image Security**
- **Minimal Base**: Ubuntu 22.04 minimal image
- **Non-root User**: Runs as dedicated user account
- **Security Scanning**: Automated vulnerability scanning
- **Regular Updates**: Keep base images updated

### **Registry Security**
- **Access Tokens**: Use tokens instead of passwords
- **Private Repositories**: For sensitive deployments
- **Image Signing**: Consider signing images for production
- **Vulnerability Monitoring**: Enable security alerts

### **Deployment Security**
- **Secrets Management**: Use proper secrets management
- **Network Policies**: Implement network segmentation
- **Resource Limits**: Set appropriate resource constraints
- **Health Checks**: Monitor container health

## 📊 **Image Specifications**

### **Binary Image**
- **Base**: Ubuntu 22.04
- **Size**: ~50MB
- **Architectures**: linux/amd64, linux/arm64
- **Runtime**: Single binary executable
- **Dependencies**: Minimal system libraries

### **Source Image**
- **Base**: Python 3.11-slim
- **Size**: ~200MB
- **Architectures**: linux/amd64, linux/arm64
- **Runtime**: Python interpreter
- **Dependencies**: Full Python environment

## 🌐 **Public Repository Examples**

### **Docker Hub**
```bash
# Pull and run
docker pull your-username/repository-monitor:latest
docker run -d -p 5000:5000 your-username/repository-monitor:latest
```

### **GitHub Container Registry**
```bash
# Pull and run
docker pull ghcr.io/your-username/repository-monitor:latest
docker run -d -p 5000:5000 ghcr.io/your-username/repository-monitor:latest
```

## 🎯 **Best Practices**

### **Version Management**
- Use semantic versioning (v2.1.0, v2.1.1, etc.)
- Tag both binary and source versions
- Maintain latest tag for stable releases
- Use pre-release tags for testing (v2.1.0-rc1)

### **Documentation**
- Update README with deployment instructions
- Include docker-compose examples
- Document configuration options
- Provide troubleshooting guides

### **Monitoring**
- Set up image vulnerability monitoring
- Monitor download statistics
- Track user feedback and issues
- Maintain compatibility matrix

This container registry deployment makes Repository Monitor easily accessible to users worldwide while maintaining security and providing multiple deployment options.
