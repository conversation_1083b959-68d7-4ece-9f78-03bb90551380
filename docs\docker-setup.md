# Docker Setup Guide

This guide explains how to run the RepoSense AI application using Docker.

## Quick Start

### Prerequisites
- Docker Desktop (Windows/Mac) or Docker Engine (Linux)
- Docker Compose
- Git

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd reposense-ai
   ```

2. **Initialize configuration**
   ```bash
   # Run setup script to create data/config.json
   ./setup.sh

   # Or use PowerShell on Windows
   .\setup-dev-windows.ps1
   ```

3. **Start development environment**
   ```bash
   # Single docker-compose.yml for all environments
   docker-compose up -d

   # Or use the startup scripts
   .\start-reposense-ai.ps1  # Windows
   ./start-reposense-ai.sh   # Linux/Mac
   ```

4. **Access the application**
   - Web Interface: http://localhost:5000
   - Ollama API: http://localhost:11434

### Production Deployment

1. **Configure the system**
   ```bash
   # Initialize with setup script
   ./setup.sh

   # Edit data/config.json with your settings
   # Or use environment variables for overrides
   ```

2. **Deploy with <PERSON><PERSON> Compose**
   ```bash
   # Single compose file for all environments
   docker-compose up -d
   ```

3. **Access the web interface**
   - Open http://localhost:5000
   - Configure repositories using the web interface
   - Use repository discovery for automatic SVN repository detection
   - Configure repositories and users
   - Start monitoring

## Docker Configuration

### Development Environment (`docker-compose.dev.yml`)
- **Hot Reload**: Code changes are automatically reflected
- **Debug Mode**: Enhanced logging and debugging features
- **Port 5001**: Development web interface
- **Volume Mounting**: Local code mounted for development

### Production Environment (`docker-compose.yml`)
- **Optimized Build**: Production-ready container
- **Port 5000**: Production web interface
- **Persistent Data**: Volumes for configuration and logs
- **Health Checks**: Container health monitoring

## Integration with Existing Docker Setup

If you have existing Docker services (Ollama, LLM Proxy, etc.):

### 1. Network Integration
```yaml
networks:
  default:
    external: true
    name: ollama-network
```

### 2. Service Dependencies
```yaml
services:
  repository-monitor:
    depends_on:
      - ollama-server-local
    external_links:
      - ollama-server-local:ollama-server-local
```

### 3. Environment Variables
```yaml
environment:
  - OLLAMA_HOST=ollama-server-local
  - OLLAMA_PORT=11434
```

## Troubleshooting

### Common Issues

1. **Port Conflicts**
   - Change ports in docker-compose files if 5000/5001 are in use
   - Update configuration accordingly

2. **Volume Permissions**
   ```bash
   # Fix volume permissions (Linux/Mac)
   sudo chown -R 1000:1000 ./data
   ```

3. **Network Issues**
   ```bash
   # Recreate networks
   docker-compose down
   docker network prune
   docker-compose up -d
   ```

4. **Container Health**
   ```bash
   # Check container status
   docker-compose ps
   docker-compose logs repository-monitor
   ```

## Windows-Specific Setup

### Docker Desktop Requirements
- Windows 10/11 Pro, Enterprise, or Education
- Hyper-V enabled or WSL2 backend
- At least 4GB RAM allocated to Docker

### PowerShell Execution Policy
```powershell
# Enable script execution
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### File Sharing
- Ensure the project directory is shared with Docker
- Check Docker Desktop settings > Resources > File Sharing

## Advanced Configuration

### Custom Networks
```yaml
networks:
  repository-monitor-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### Resource Limits
```yaml
services:
  repository-monitor:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
```

### Environment-Specific Overrides
```bash
# Use environment-specific compose files
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## Usage Commands

### Start Services
```bash
docker-compose up -d
```

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f repository-monitor
docker-compose logs -f ollama
```

### Run One-Time Check
```bash
docker-compose run --rm repository-monitor python reposense_ai_app.py --once
```

### Interactive Setup
```bash
docker-compose run --rm repository-monitor python reposense_ai_app.py --setup
```

### Stop Services
```bash
docker-compose down
```

### Restart Services
```bash
docker-compose restart repository-monitor
```

## Model Management

### List Available Models
```bash
docker-compose exec ollama ollama list
```

### Pull New Model
```bash
docker-compose exec ollama ollama pull mistral
```

### Update Configuration for New Model
Edit `data/config.json` and change `"ollama_model": "mistral"`, then restart:
```bash
docker-compose restart repository-monitor
```

## Development Commands

### Essential Development Commands
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Stop development environment
docker-compose -f docker-compose.dev.yml down

# Restart specific service
docker-compose -f docker-compose.dev.yml restart repository-monitor

# View logs
docker-compose -f docker-compose.dev.yml logs -f repository-monitor

# Access container shell
docker-compose -f docker-compose.dev.yml exec repository-monitor bash
```

### Debugging Commands
```bash
# Check container health
docker-compose -f docker-compose.dev.yml exec repository-monitor curl -f http://localhost:5000 || echo "Health check failed"

# Test Python imports
docker-compose -f docker-compose.dev.yml exec repository-monitor python -c "import web_interface; print('OK')"

# Check Flask debug mode
docker-compose -f docker-compose.dev.yml exec repository-monitor python -c "import os; print('Debug:', os.getenv('FLASK_DEBUG'))"
```

### Hot Reload Troubleshooting

#### Check Volume Mounts
```bash
# Verify files are mounted correctly
docker-compose -f docker-compose.dev.yml exec repository-monitor ls -la /app/templates/
docker-compose -f docker-compose.dev.yml exec repository-monitor ls -la /app/
```

#### Check File Permissions
```bash
# Files should be readable by container
docker-compose -f docker-compose.dev.yml exec repository-monitor cat /app/templates/index.html
```

#### Restart Container
```bash
# If hot reload stops working
docker-compose -f docker-compose.dev.yml restart repository-monitor
```

#### Rebuild Container
```bash
# If major changes or issues
docker-compose -f docker-compose.dev.yml down
docker-compose -f docker-compose.dev.yml build --no-cache
docker-compose -f docker-compose.dev.yml up -d
```

## Troubleshooting

### Check Service Health
```bash
docker-compose ps
```

### View Detailed Logs
```bash
docker-compose logs --tail=100 repository-monitor
```

### Test Ollama Connection
```bash
docker-compose exec ollama curl http://localhost:11434/api/tags
```

### Access Container Shell
```bash
docker-compose exec repository-monitor bash
docker-compose exec ollama bash
```

### Reset Everything
```bash
docker-compose down -v  # Removes volumes
docker-compose build --no-cache
./setup.sh
```

## Health Monitoring

The containers include health checks:
- **repository-monitor**: Checks if Python process is running
- **ollama**: Checks if API endpoint is responding

Monitor with:
```bash
docker-compose ps  # Shows health status
```

## Data Persistence

- **Application data**: Stored in `./data` directory (logs, config, output)
- **Ollama models**: Stored in Docker volume `ollama_data`
- **Configuration**: Persisted in `data/config.json`

## Backup Strategy

```bash
# Backup application data
tar -czf repository-monitor-backup-$(date +%Y%m%d).tar.gz data/

# Backup Ollama models
docker-compose exec ollama ollama list  # List models to backup
# Models are in Docker volume, backup with Docker volume backup tools
```
