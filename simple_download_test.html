<!DOCTYPE html>
<html>
<head>
    <title>Simple Download Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1>Simple Download Test</h1>
        
        <div class="alert alert-info">
            <strong>Instructions:</strong>
            <ol>
                <li>Open browser developer tools (F12)</li>
                <li>Go to Console tab</li>
                <li>Click the download buttons below</li>
                <li>Check console for debug messages</li>
                <li>Check if files download</li>
            </ol>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>Test Document Content</h5>
            </div>
            <div class="card-body">
                <pre id="rawContent">This is test document content for download testing.

It includes multiple lines and should be included in the download.

## Test Features:
- Document metadata
- Content formatting  
- Diff inclusion
- Error handling</pre>
                
                <div id="rawDiffContent" style="display: none;">Index: test.py
===================================================================
--- test.py	(revision 1)
+++ test.py	(revision 2)
@@ -1,2 +1,3 @@
 print("Hello World")
+print("New line added")
 # End of file</div>
            </div>
        </div>
        
        <div class="mt-3">
            <button class="btn btn-primary me-2" onclick="testBasicDownload()">
                <i class="fas fa-download"></i> Test Basic Download
            </button>
            <button class="btn btn-success me-2" onclick="testMarkdownDownload()">
                <i class="fab fa-markdown"></i> Test Markdown Download
            </button>
            <button class="btn btn-info" onclick="testBrowserSupport()">
                <i class="fas fa-info-circle"></i> Test Browser Support
            </button>
        </div>
        
        <div class="mt-3">
            <div id="output" class="alert alert-secondary">
                <strong>Output:</strong> Click buttons above to test downloads
            </div>
        </div>
    </div>

    <script>
        function log(message) {
            console.log(message);
            const output = document.getElementById('output');
            output.innerHTML += '<br>' + message;
        }

        function testBrowserSupport() {
            log('=== Browser Support Test ===');
            log('URL.createObjectURL: ' + (typeof URL.createObjectURL === 'function'));
            log('Blob support: ' + (typeof Blob === 'function'));
            log('Document.createElement: ' + (typeof document.createElement === 'function'));
            log('User Agent: ' + navigator.userAgent);
        }

        function testBasicDownload() {
            log('=== Basic Download Test ===');
            
            try {
                const content = 'Hello World!\nThis is a test file.';
                const blob = new Blob([content], { type: 'text/plain' });
                log('Blob created: ' + blob.size + ' bytes');
                
                const url = URL.createObjectURL(blob);
                log('Object URL created: ' + url.substring(0, 50) + '...');
                
                const a = document.createElement('a');
                a.href = url;
                a.download = 'test-basic.txt';
                
                document.body.appendChild(a);
                log('Link element added to DOM');
                
                a.click();
                log('Click triggered');
                
                URL.revokeObjectURL(url);
                document.body.removeChild(a);
                log('Cleanup completed');
                
            } catch (error) {
                log('ERROR: ' + error.message);
            }
        }

        function testMarkdownDownload() {
            log('=== Markdown Download Test ===');
            
            try {
                // Simulate document data
                const documentData = {
                    displayName: 'Test Document - Revision 123',
                    repositoryName: 'test-repo',
                    revision: '123',
                    author: 'test-user',
                    date: '2025-01-01 12:00:00',
                    filename: 'test-document.md',
                    size: '2.5',
                    relativePath: 'docs/test-document.md',
                    commitMessage: 'Test commit message',
                    changedFiles: ['/test.py', '/README.md'],
                    filesChangedCount: '2 files changed in this commit'
                };
                
                log('Document data prepared');
                
                // Build markdown content
                let content = '# ' + documentData.displayName + '\n\n';
                content += '## Revision Summary Document\n\n';
                content += '**Repository:** ' + documentData.repositoryName + '\n\n';
                content += '**Revision:** ' + documentData.revision + '\n\n';
                content += '**Author:** ' + documentData.author + '\n\n';
                content += '**Date:** ' + documentData.date + ' UTC\n\n';
                content += '**Filename:** ' + documentData.filename + '\n\n';
                content += '**Size:** ' + documentData.size + ' KB\n\n';
                content += '**Path:** ' + documentData.relativePath + '\n\n';
                content += '**Commit Message:** ' + documentData.commitMessage + '\n\n';
                
                if (documentData.changedFiles && documentData.changedFiles.length > 0) {
                    content += '**Files Changed:**\n\n';
                    documentData.changedFiles.forEach(function(filePath) {
                        content += '- `' + filePath + '`\n';
                    });
                    content += '\n' + documentData.filesChangedCount + '\n\n';
                }
                
                content += '---\n\n';
                content += '## Document Content\n\n';
                content += document.getElementById('rawContent').textContent;
                
                // Add diff if available
                const diffElement = document.getElementById('rawDiffContent');
                if (diffElement && diffElement.textContent.trim()) {
                    content += '\n\n---\n\n## Code Changes (Diff)\n\n```diff\n';
                    content += diffElement.textContent.trim();
                    content += '\n```';
                }
                
                log('Content built: ' + content.length + ' characters');
                
                const blob = new Blob([content], { type: 'text/markdown' });
                log('Markdown blob created: ' + blob.size + ' bytes');
                
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'test-document-complete.md';
                
                document.body.appendChild(a);
                a.click();
                URL.revokeObjectURL(url);
                document.body.removeChild(a);
                
                log('Markdown download completed successfully!');
                
            } catch (error) {
                log('ERROR in markdown download: ' + error.message);
                log('Stack trace: ' + error.stack);
            }
        }
    </script>
</body>
</html>
