#!/usr/bin/env python3
"""
Email service for sending commit notifications
Handles SMTP configuration and email sending functionality
"""

import logging
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

from models import Config, CommitInfo


class EmailService:
    """Service for sending email notifications"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def send_email(self, subject: str, body: str, commit: CommitInfo):
        """Send email notification"""
        # Get all recipients for this repository (global + repository-specific)
        recipients = self.config.get_all_recipients_for_repository(commit.repository_id)

        if not recipients:
            self.logger.warning(f"No email recipients configured for repository {commit.repository_name} (ID: {commit.repository_id})")
            return

        try:
            msg = MIMEMultipart()
            msg['From'] = self.config.email_from
            msg['To'] = ', '.join(recipients)
            msg['Subject'] = subject
            
            # Add commit details to email body
            full_body = f"""
{body}

---
Commit Details:
- Revision: {commit.revision}
- Author: {commit.author}
- Date: {commit.date}
- Message: {commit.message}

Changed Files:
{chr(10).join('- ' + path for path in commit.changed_paths)}
"""
            
            msg.attach(MIMEText(full_body, 'plain'))
            
            # Connect to SMTP server
            with smtplib.SMTP(self.config.smtp_host, self.config.smtp_port) as server:
                if self.config.smtp_username and self.config.smtp_password:
                    server.starttls()
                    server.login(self.config.smtp_username, self.config.smtp_password)
                
                server.send_message(msg)
            
            self.logger.info(f"Email sent for revision {commit.revision}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending email: {e}")
            return False
    
    def is_configured(self) -> bool:
        """Check if email service is properly configured"""
        return bool(self.config.email_recipients and 
                   self.config.smtp_host and 
                   self.config.email_from)
