# SVN Monitor - Windows Development Setup

This guide covers setting up the SVN Monitor system for development on Windows using Docker Desktop.

> **Note**: If you already have Docker services running (Ollama, LLM Proxy, etc.), see [INTEGRATION-GUIDE.md](INTEGRATION-GUIDE.md) for integration instructions.

## Prerequisites

### Required Software
- **Windows 10/11** (Pro, Enterprise, or Education for Hyper-V)
- **Docker Desktop for Windows** (latest version)
- **Git for Windows** (for cloning the repository)
- **PowerShell 5.1+** or **Windows Terminal** (recommended)

### Optional but Recommended
- **Visual Studio Code** with Docker extension
- **Windows Subsystem for Linux (WSL2)** for better Docker performance

## Quick Start

### 1. Install Docker Desktop

1. Download Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop)
2. Install and start Docker Desktop
3. Ensure Docker Desktop is running (check system tray)
4. Verify installation:
   ```powershell
   docker --version
   docker-compose --version
   ```

### 2. <PERSON>lone and Setup

```powershell
# Clone the repository
git clone <repository-url>
cd svn-checkin-monitor

# Run the automated setup (PowerShell - recommended)
.\setup-dev-windows.ps1

# OR run the batch file alternative
.\setup-dev-windows.bat
```

### 3. Access the Application

- **Web Interface**: http://localhost:5000
- **Ollama API**: http://localhost:11434

## Manual Setup (Alternative)

If you prefer manual setup or the automated scripts don't work:

### 1. Create Development Configuration

```powershell
# Copy example configuration
Copy-Item config.example.json config.json

# Create data directories
New-Item -ItemType Directory -Path "data\logs" -Force
New-Item -ItemType Directory -Path "data\output\repositories" -Force
```

### 2. Build and Start Services

```powershell
# Build the development environment
docker-compose -f docker-compose.dev.yml build

# Start all services
docker-compose -f docker-compose.dev.yml up -d

# Check service status
docker-compose -f docker-compose.dev.yml ps
```

### 3. Download AI Model

```powershell
# Download the default Ollama model (this may take a few minutes)
docker-compose -f docker-compose.dev.yml exec ollama ollama pull llama2
```

## Development Workflow

### Daily Development

```powershell
# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# View logs (useful for debugging)
docker-compose -f docker-compose.dev.yml logs -f svn-monitor

# Stop when done
docker-compose -f docker-compose.dev.yml down
```

### Making Code Changes

The development setup includes hot reload, so code changes are automatically reflected:

1. Edit Python files in your favorite editor
2. Save the changes
3. The application will automatically restart
4. Refresh your browser to see changes

### Debugging

```powershell
# View application logs
docker-compose -f docker-compose.dev.yml logs svn-monitor

# View all service logs
docker-compose -f docker-compose.dev.yml logs

# Access container shell for debugging
docker-compose -f docker-compose.dev.yml exec svn-monitor bash
```

### Testing Configuration

```powershell
# Test configuration validity
docker-compose -f docker-compose.dev.yml exec svn-monitor python -c "
from config_manager import ConfigManager
cm = ConfigManager()
config = cm.load_config()
print('Configuration loaded successfully')
"
```

## Configuration for Development

### Repository Configuration with Branch Support

Edit `config.json` to add your repositories with branch selection:

```json
{
  "repositories": [
    {
      "id": "my-repo-1",
      "name": "My Development Repository",
      "url": "https://svn.mycompany.com/repos/myproject",
      "username": "myusername",
      "password": "mypassword",
      "enabled": true,
      "branch_path": "trunk",
      "monitor_all_branches": false,
      "assigned_users": ["user-id-1"]
    },
    {
      "id": "my-repo-2",
      "name": "Feature Branch Repository",
      "url": "https://svn.mycompany.com/repos/features",
      "username": "myusername",
      "password": "mypassword",
      "enabled": true,
      "branch_path": "branches/feature-authentication",
      "monitor_all_branches": false,
      "assigned_users": ["user-id-1"]
    },
    {
      "id": "my-repo-3",
      "name": "All Branches Repository",
      "url": "https://svn.mycompany.com/repos/monitoring",
      "username": "myusername",
      "password": "mypassword",
      "enabled": true,
      "branch_path": null,
      "monitor_all_branches": true,
      "assigned_users": ["user-id-1"]
    }
  ]
}
```

### Branch Selection Options

- **Specific Branch**: Set `branch_path` to the branch you want to monitor (e.g., "trunk", "branches/feature-x")
- **All Branches**: Set `monitor_all_branches` to `true` and `branch_path` to `null`
- **Repository Root**: Set both `branch_path` to `null` and `monitor_all_branches` to `false`

### Email Configuration for Development

For development, you can either:

1. **Use a local SMTP server** (like MailHog):
   ```json
   {
     "smtp_host": "localhost",
     "smtp_port": 1025,
     "use_tls": false
   }
   ```

2. **Use Gmail with App Password**:
   ```json
   {
     "smtp_host": "smtp.gmail.com",
     "smtp_port": 587,
     "username": "<EMAIL>",
     "password": "your-app-password",
     "use_tls": true
   }
   ```

## Troubleshooting

### Common Issues

**Docker Desktop Not Starting**
- Ensure Hyper-V is enabled (Windows Features)
- Check if WSL2 is properly installed
- Restart Docker Desktop service

**Port Already in Use**
```powershell
# Check what's using port 5000
netstat -ano | findstr :5000

# Kill the process if needed (replace PID)
taskkill /PID <PID> /F
```

**Container Build Failures**
```powershell
# Clean rebuild
docker-compose -f docker-compose.dev.yml down
docker system prune -f
docker-compose -f docker-compose.dev.yml build --no-cache
```

**Permission Issues**
```powershell
# Reset data directory permissions
icacls data /reset /T
icacls data /grant Everyone:(OI)(CI)F /T
```

**Ollama Model Download Issues**
```powershell
# Manual model download
docker-compose -f docker-compose.dev.yml exec ollama ollama pull llama2

# Check available models
docker-compose -f docker-compose.dev.yml exec ollama ollama list
```

### Performance Optimization

**For Better Performance on Windows:**

1. **Enable WSL2 Backend** in Docker Desktop settings
2. **Allocate More Resources** in Docker Desktop:
   - Memory: 4GB+ recommended
   - CPU: 2+ cores recommended
3. **Use WSL2 File System** for better I/O performance:
   ```powershell
   # Clone repository in WSL2
   wsl
   cd /home/<USER>
   git clone <repository-url>
   ```

### Development Tips

**VS Code Integration**
1. Install Docker extension for VS Code
2. Open the project folder in VS Code
3. Use the Docker extension to manage containers
4. Use integrated terminal for Docker commands

**Hot Reload Development**
- The development setup automatically reloads on code changes
- Edit files in your Windows editor
- Changes are reflected immediately in the running container

**Database Inspection**
```powershell
# View current configuration
docker-compose -f docker-compose.dev.yml exec svn-monitor python -c "
from config_manager import ConfigManager
import json
cm = ConfigManager()
config = cm.load_config()
print(json.dumps(config.__dict__, indent=2, default=str))
"
```

## Next Steps

1. **Configure Your Repositories**: Edit `config.json` with your actual SVN repositories
2. **Set Up Users**: Use the web interface at http://localhost:5000 to add users
3. **Test Monitoring**: Enable a repository and test the monitoring functionality
4. **Customize Settings**: Adjust monitoring intervals and notification preferences

## Support

- **Documentation**: Check the `docs/` folder for comprehensive guides
- **Logs**: Always check container logs for troubleshooting
- **Configuration**: Use the web interface for easier configuration management

For more detailed information, see the main documentation in the `docs/` folder.
