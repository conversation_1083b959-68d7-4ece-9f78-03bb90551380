#!/usr/bin/env python3
"""
RepoSense AI Binary Builder
Creates optimized binary distributions for various platforms
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path

class BinaryBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / "dist"
        self.build_dir = self.project_root / "build"
        
    def clean_build_dirs(self):
        """Clean previous build artifacts"""
        print("🧹 Cleaning build directories...")
        for dir_path in [self.dist_dir, self.build_dir]:
            if dir_path.exists():
                shutil.rmtree(dir_path)
                print(f"   Removed {dir_path}")
    
    def install_dependencies(self):
        """Install build dependencies"""
        print("📦 Installing build dependencies...")
        subprocess.run([
            sys.executable, "-m", "pip", "install", 
            "pyinstaller", "setuptools", "wheel"
        ], check=True)
    
    def build_binary(self, target_platform=None):
        """Build binary using PyInstaller"""
        print(f"🔨 Building binary for {platform.system()}...")
        print(f"📁 Project root: {self.project_root}")

        # Determine binary name based on platform
        binary_name = "reposense-ai"
        if platform.system() == "Windows":
            binary_name += ".exe"

        # Determine path separator for --add-data
        path_sep = ";" if platform.system() == "Windows" else ":"

        # Check if entry point exists
        entry_point = self.project_root / "reposense_ai_app.py"
        print(f"🔍 Looking for entry point: {entry_point}")
        if not entry_point.exists():
            print("❌ Entry point not found!")
            print("📂 Current directory contents:")
            for item in self.project_root.iterdir():
                if item.name.endswith('.py'):
                    print(f"   {item.name}")
            raise FileNotFoundError(f"Entry point not found: {entry_point}")
        else:
            print(f"✅ Entry point found: {entry_point}")

        # Check if required directories exist
        required_dirs = ["templates", "static", "docs", "marketing"]
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                print(f"✅ Found directory: {dir_name}")
            else:
                print(f"⚠️  Directory not found: {dir_name} (will be skipped)")

        # Check if we can run pyinstaller with better detection
        pyinstaller_cmd = None
        possible_commands = [
            f"{sys.executable} -m PyInstaller",  # Works for Windows Store Python
            "pyinstaller",
            sys.executable.replace("python.exe", "Scripts\\pyinstaller.exe"),
            sys.executable.replace("python.exe", "Scripts\\pyinstaller")
        ]

        for cmd in possible_commands:
            try:
                if cmd.startswith(sys.executable):
                    test_cmd = cmd.split() + ["--version"]
                else:
                    test_cmd = [cmd, "--version"]

                result = subprocess.run(test_cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    pyinstaller_cmd = cmd
                    print(f"✅ PyInstaller found: {cmd}")
                    print(f"   Version: {result.stdout.strip()}")
                    break
            except Exception:
                continue

        if not pyinstaller_cmd:
            print("❌ PyInstaller not found. Please install it first:")
            print(f"   {sys.executable} -m pip install pyinstaller")
            raise FileNotFoundError("PyInstaller not found")

        # PyInstaller command - use the detected command
        if pyinstaller_cmd.startswith(sys.executable):
            cmd = pyinstaller_cmd.split()
        else:
            cmd = [pyinstaller_cmd]

        cmd.extend([
            "--onefile",
            "--name", binary_name.replace(".exe", ""),

            # Include data files (Windows uses ; separator, Unix uses :)
            "--add-data", f"templates{path_sep}templates",
            "--add-data", f"static{path_sep}static",
            "--add-data", f"docs{path_sep}docs",
            "--add-data", f"marketing{path_sep}marketing",

            # Hidden imports for dynamic loading
            "--hidden-import", "repository_backends.svn_backend",
            "--hidden-import", "repository_backends.git_backend",
            "--hidden-import", "repository_backends.base",
            "--hidden-import", "flask",
            "--hidden-import", "sqlite3",
            "--hidden-import", "requests",
            "--hidden-import", "jinja2",
            "--hidden-import", "werkzeug",
            "--hidden-import", "markupsafe",

            # Collect all Flask and Jinja2 files
            "--collect-all", "flask",
            "--collect-all", "jinja2",
            "--collect-all", "werkzeug",
            "--collect-all", "markupsafe",

            # Optimization options
            "--optimize", "2",

            # Exclude GUI packages that aren't needed for web app - expanded list
            "--exclude-module", "PyQt5",
            "--exclude-module", "PyQt6",
            "--exclude-module", "PySide2",
            "--exclude-module", "PySide6",
            "--exclude-module", "tkinter",
            "--exclude-module", "turtle",
            "--exclude-module", "matplotlib",
            "--exclude-module", "numpy",
            "--exclude-module", "scipy",
            "--exclude-module", "pandas",
            "--exclude-module", "IPython",
            "--exclude-module", "jupyter",
            "--exclude-module", "notebook",
            "--exclude-module", "nbformat",
            "--exclude-module", "jsonschema",
            "--exclude-module", "pygame",
            "--exclude-module", "zmq",
            "--exclude-module", "tornado",
            "--exclude-module", "black",
            "--exclude-module", "blib2to3",

            # Entry point
            str(entry_point)
        ])

        # Add Windows-specific options
        if platform.system() == "Windows":
            cmd.extend([
                "--console",  # Keep console window for logs
            ])
            # Add icon if it exists
            icon_path = self.project_root / "static" / "favicon.ico"
            if icon_path.exists():
                cmd.extend(["--icon", str(icon_path)])
        else:
            # Add strip option for Unix systems
            cmd.append("--strip")

        print("Running PyInstaller with command:")
        print(" ".join(cmd))

        # Run PyInstaller
        try:
            result = subprocess.run(cmd, check=True, cwd=self.project_root,
                                  capture_output=True, text=True)
            print("PyInstaller output:")
            print(result.stdout)
            if result.stderr:
                print("PyInstaller warnings:")
                print(result.stderr)
        except subprocess.CalledProcessError as e:
            print(f"PyInstaller failed with return code {e.returncode}")
            print("STDOUT:", e.stdout)
            print("STDERR:", e.stderr)
            raise

        print(f"✅ Binary built successfully: {self.dist_dir / binary_name}")
    
    def create_distribution_package(self):
        """Create complete distribution package"""
        print("📦 Creating distribution package...")
        
        # Create distribution directory
        dist_package = self.dist_dir / "reposense-ai-package"
        dist_package.mkdir(exist_ok=True)
        
        # Copy binary
        binary_name = "reposense-ai"
        if platform.system() == "Windows":
            binary_name += ".exe"
        
        binary_src = self.dist_dir / binary_name
        binary_dst = dist_package / binary_name
        shutil.copy2(binary_src, binary_dst)
        
        # Copy essential files (no longer copying old config files)
        essential_files = [
            "requirements.txt",
            "README.md"
        ]
        
        for essential_file in essential_files:
            src = self.project_root / essential_file
            if src.exists():
                shutil.copy2(src, dist_package / essential_file)
        
        # Copy documentation
        docs_src = self.project_root / "docs"
        docs_dst = dist_package / "docs"
        if docs_src.exists():
            shutil.copytree(docs_src, docs_dst)
        
        # Copy marketing materials
        marketing_src = self.project_root / "marketing"
        marketing_dst = dist_package / "marketing"
        if marketing_src.exists():
            shutil.copytree(marketing_src, marketing_dst)
        
        # Create startup scripts
        self.create_startup_scripts(dist_package)
        
        # Create installation guide
        self.create_installation_guide(dist_package)
        
        print(f"✅ Distribution package created: {dist_package}")
        return dist_package
    
    def create_startup_scripts(self, dist_dir):
        """Create platform-specific startup scripts"""
        
        # Unix/Linux startup script
        unix_script = dist_dir / "start-reposense-ai.sh"
        with open(unix_script, 'w', encoding='utf-8') as f:
            f.write("""#!/bin/bash
# RepoSense AI Startup Script

set -e

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Create data directory if it doesn't exist
mkdir -p data logs

# Initialize configuration if not exists
if [ ! -f data/config.json ]; then
    echo "Initializing configuration..."
    cp config.example.json data/config.json
    echo "Configuration initialized. Please customize data/config.json"
    echo "Press Enter to continue..."
    read
fi

# Set environment variables
export REPOSENSE_AI_CONFIG="$SCRIPT_DIR/data/config.json"
export REPOSENSE_AI_DATA_DIR="$SCRIPT_DIR/data"
export REPOSENSE_AI_LOG_DIR="$SCRIPT_DIR/logs"

# Start RepoSense AI
echo "Starting RepoSense AI..."
echo "Access the web interface at: http://localhost:5000"
echo "Press Ctrl+C to stop"

./reposense-ai
""")
        unix_script.chmod(0o755)
        
        # Windows startup script
        windows_script = dist_dir / "start-reposense-ai.bat"
        with open(windows_script, 'w', encoding='utf-8') as f:
            f.write("""@echo off
REM RepoSense AI Startup Script

cd /d "%~dp0"

REM Create data directory if it doesn't exist
if not exist data mkdir data
if not exist logs mkdir logs

REM Initialize configuration if not exists
if not exist data\\config.json (
    echo Initializing configuration...
    copy config.example.json data\\config.json
    echo Configuration initialized. Please customize data\\config.json
    pause
)

REM Set environment variables
set REPOSENSE_AI_CONFIG=%~dp0data\\config.json
set REPOSENSE_AI_DATA_DIR=%~dp0data
set REPOSENSE_AI_LOG_DIR=%~dp0logs

REM Start RepoSense AI
echo Starting RepoSense AI...
echo Access the web interface at: http://localhost:5000
echo Press Ctrl+C to stop

reposense-ai.exe
pause
""")
    
    def create_installation_guide(self, dist_dir):
        """Create installation and setup guide"""
        guide_path = dist_dir / "INSTALLATION.md"
        
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write("""# RepoSense AI - Binary Installation Guide

## Quick Start

### 1. Extract Package
Extract the repository-monitor-package to your desired location.

### 2. Run Startup Script

**Linux/macOS:**
```bash
./start-repository-monitor.sh
```

**Windows:**
```cmd
start-repository-monitor.bat
```

### 3. Access Web Interface
Open your browser and navigate to: http://localhost:5000

## Configuration

### Initial Setup
1. The first run will create `data/config.json` from the example
2. Customize the configuration file for your environment
3. Restart the application

### Key Configuration Options
- **repositories**: Add your SVN repositories
- **ollama**: Configure local AI service
- **users**: Set up user accounts
- **email**: Configure email notifications

## Directory Structure
```
repository-monitor-package/
├── repository-monitor(.exe)     # Main binary
├── start-repository-monitor.*   # Startup scripts
├── config.example.json         # Configuration template
├── data/                       # Application data (created on first run)
├── logs/                       # Application logs (created on first run)
├── docs/                       # Documentation
└── marketing/                  # Marketing materials
```

## System Requirements

### Minimum Requirements
- **OS**: Linux, macOS, or Windows 10+
- **RAM**: 512MB available memory
- **Disk**: 100MB free space
- **Network**: Internet access for AI services (optional for local Ollama)

### Recommended Requirements
- **OS**: Ubuntu 20.04+, macOS 11+, or Windows 11
- **RAM**: 2GB available memory
- **Disk**: 1GB free space
- **CPU**: 2+ cores for optimal performance

## Dependencies

### Required System Dependencies
- **SVN**: Subversion client for repository access
- **Git**: Git client (for future Git support)

### Installation Commands

**Ubuntu/Debian:**
```bash
sudo apt-get update
sudo apt-get install subversion git
```

**CentOS/RHEL:**
```bash
sudo yum install subversion git
```

**macOS:**
```bash
brew install subversion git
```

**Windows:**
- Install TortoiseSVN or SlikSVN
- Install Git for Windows

## Troubleshooting

### Common Issues

**Port 5000 already in use:**
- Modify the port in `data/config.json`
- Or stop the conflicting service

**SVN authentication issues:**
- Ensure SVN credentials are configured
- Test SVN access manually first

**AI service connection issues:**
- Check Ollama installation and configuration
- Verify network connectivity for cloud AI services

### Log Files
Check `logs/reposense_ai.log` for detailed error information.

## Support

For support and documentation:
- Documentation: `docs/` directory
- Configuration Guide: `docs/configuration.md`
- Deployment Guide: `docs/deployment.md`

## Security Notes

- RepoSense AI processes data locally by default
- Configure firewall rules for network access
- Use HTTPS in production environments
- Regularly update the binary for security patches
""")
        
        print(f"✅ Installation guide created: {guide_path}")

    def create_docker_files(self, dist_dir):
        """Create Docker deployment files"""

        # Create optimized Dockerfile for binary
        dockerfile_path = dist_dir / "Dockerfile"
        with open(dockerfile_path, 'w', encoding='utf-8') as f:
            f.write("""# RepoSense AI - Binary Docker Deployment
FROM ubuntu:22.04

# Install runtime dependencies
RUN apt-get update && apt-get install -y \\
    subversion \\
    git \\
    curl \\
    ca-certificates \\
    && rm -rf /var/lib/apt/lists/* \\
    && apt-get clean

# Create non-root user
RUN groupadd -r repository-monitor && \\
    useradd -r -g repository-monitor -d /app -s /bin/bash repository-monitor

# Set working directory
WORKDIR /app

# Copy binary and files
COPY repository-monitor* /app/
COPY config.example.json /app/
COPY docs /app/docs
COPY marketing /app/marketing
COPY start-repository-monitor.sh /app/

# Create data directories and set permissions
RUN mkdir -p /app/data /app/logs && \\
    chmod +x /app/repository-monitor* /app/start-repository-monitor.sh && \\
    chown -R repository-monitor:repository-monitor /app

# Switch to non-root user
USER repository-monitor

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:5000/health || exit 1

# Start the application
CMD ["./start-repository-monitor.sh"]
""")

        # Create docker-compose for binary deployment
        compose_path = dist_dir / "docker-compose.yml"
        with open(compose_path, 'w', encoding='utf-8') as f:
            f.write("""version: '3.8'

services:
  repository-monitor:
    build: .
    container_name: repository-monitor
    restart: unless-stopped

    ports:
      - "5000:5000"

    volumes:
      - reposense_ai_data:/app/data
      - reposense_ai_logs:/app/logs

    environment:
      - REPOSENSE_AI_CONFIG=/app/data/config.json
      - REPOSENSE_AI_DATA_DIR=/app/data
      - REPOSENSE_AI_LOG_DIR=/app/logs

volumes:
  reposense_ai_data:
  reposense_ai_logs:
""")

        print(f"✅ Docker files created in {dist_dir}")

def main():
    """Main build process"""
    builder = BinaryBuilder()
    
    print("🚀 RepoSense AI Binary Builder")
    print("=" * 50)
    
    try:
        # Clean previous builds
        builder.clean_build_dirs()
        
        # Install dependencies
        builder.install_dependencies()
        
        # Build binary
        builder.build_binary()
        
        # Create distribution package
        dist_package = builder.create_distribution_package()

        # Create Docker deployment files
        builder.create_docker_files(dist_package)
        
        print("\n✅ Build completed successfully!")
        print(f"📦 Distribution package: {dist_package}")
        print("\nNext steps:")
        print("1. Test the binary in the dist directory")
        print("2. Distribute the package to target systems")
        print("3. Follow INSTALLATION.md for deployment")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Build failed: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
