# RepoSense AI Documentation

Welcome to the RepoSense AI documentation. This system provides automated monitoring of repositories with AI-powered documentation generation and plugin-based architecture.

## Table of Contents

### Getting Started
1. [System Overview](#system-overview)
2. [Docker Setup](docker-setup.md) - Docker deployment and configuration
3. [Integration Guide](integration.md) - External service integration

### Core Documentation
5. [Architecture & Design](design.md) - System architecture and plugin development
6. [Configuration Guide](configuration.md) - Complete configuration reference
7. [User Guide](usage.md) - How to use the system
8. [Deployment Guide](deployment.md) - Production deployment

### Development & Reference
9. [Development Guide](development.md) - Contributing, development setup, Windows support, and hot reload workflow
10. [Release Notes](release-notes.md) - Version history and architectural changes
11. [Changelog](CHANGELOG.md) - Detailed change log with recent enhancements

## System Overview

RepoSense AI is a comprehensive solution for monitoring repositories with AI-powered documentation generation and plugin-based architecture. The system supports SVN repositories with Git support planned for future releases.

### Key Features

#### Core Functionality
- **Plugin Architecture**: Extensible backend system supporting multiple repository types
- **AI Documentation**: Automatic commit analysis and documentation generation with Ollama
- **Document Management**: Browse, search, and manage generated documentation
- **Repository Discovery**: Automatically discover repositories from servers
- **Modern Web Interface**: Responsive web UI with real-time monitoring
- **Docker Support**: Complete containerization with development environment
- **Configurable Settings**: JSON-based configuration with web interface management

#### Advanced Features (v2.1.0)
- **User Feedback System**: Code review tracking, documentation quality ratings, and risk assessment overrides
- **Side-by-Side Diff Viewer**: Advanced diff visualization with format switching
- **Hybrid AI Analysis**: Fast heuristics with LLM fallback for robust metadata extraction
- **Multi-Encoding Support**: Handles UTF-8, Latin-1, CP1252, and binary files gracefully
- **Accurate Progress Tracking**: Fixed progress calculation for all revision ranges
- **On-Demand Diff Generation**: Efficient diff creation using stored repository metadata

### Architecture Highlights

- **Plugin-Based**: Repository backends as pluggable components
- **Service-Oriented**: Individual services for monitoring, user management, documents, etc.
- **Event-Driven**: Background monitoring with configurable intervals
- **Scalable**: Designed to handle multiple repositories and users efficiently
- **Extensible**: Clean architecture for adding new repository types and features

## Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd repository-monitor
   ```

2. **Start with Docker (Recommended)**
   ```bash
   # Development environment
   docker-compose -f docker-compose.dev.yml up -d

   # Access web interface
   open http://localhost:5001
   ```

3. **Configure Repositories**
   - Open the web interface
   - Navigate to Configuration
   - Add your repositories and users
   - Start monitoring

3. **Deploy with Docker**
   ```bash
   docker-compose up -d
   ```

4. **Access Web Interface**
   - Open http://localhost:5000
   - Configure repositories and users
   - Start monitoring

## System Requirements

- **Docker & Docker Compose** (recommended)
- **Python 3.8+** (for local development)
- **SVN Client** (svn command-line tools)
- **Ollama Server** (for AI documentation generation)
- **SMTP Server** (for email notifications)

## Core Components

### Services
- **Monitor Service**: Core SVN monitoring and change detection
- **User Management Service**: User creation, roles, and repository associations
- **Repository Discovery Service**: Automatic repository discovery from SVN servers
- **Email Service**: Intelligent email notifications with user targeting
- **Ollama Service**: AI-powered documentation generation
- **File Manager**: Document and email storage with repository organization
- **Web Interface**: Flask-based configuration and monitoring interface

### Data Models
- **User**: Role-based user accounts with notification preferences
- **Repository**: SVN repository configurations with user associations
- **Config**: Central configuration management with persistence

### Web Interface
- **Dashboard**: System status and monitoring overview
- **Repository Management**: Add, configure, and monitor repositories
- **User Management**: Create and manage users with role-based access
- **Repository Discovery**: Scan SVN servers for available repositories
- **Configuration**: System-wide settings and preferences
- **Logs**: Monitoring activity and system logs

## Getting Help

- **Configuration Issues**: See [Configuration Guide](configuration.md)
- **Usage Questions**: Check [User Guide](usage.md)
- **Development**: Review [Development Guide](development.md)
- **API Integration**: Consult [API Reference](api.md)

## Documentation Structure

```
docs/
├── index.md              # This overview document
├── design.md             # System architecture and design
├── configuration.md      # Configuration guide and examples
├── usage.md              # User guide and workflows
├── api.md                # API reference and endpoints
├── deployment.md         # Deployment and infrastructure
├── development.md        # Development and contribution guide
├── README.md             # Original README (moved from root)
└── REFACTORING_SUMMARY.md # Development history
```

## Version Information

- **Current Version**: 2.0.0
- **Architecture**: Modular service-oriented design
- **Python Version**: 3.8+
- **Docker Support**: Yes
- **Web Interface**: Modern responsive design
- **AI Integration**: Ollama-powered documentation

## License

This project is licensed under the MIT License. See the LICENSE file for details.

## Contributing

We welcome contributions! Please see the [Development Guide](development.md) for information on how to contribute to this project.
