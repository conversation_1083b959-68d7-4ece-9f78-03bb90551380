#!/bin/bash
# Build Repository Monitor Linux binary

set -e

echo "🐧 Building Repository Monitor Linux Binary"
echo "=========================================="

# Check if we're in the right directory
if [ ! -f "repository_monitor_binary.py" ]; then
    echo "❌ Error: repository_monitor_binary.py not found!"
    echo "Make sure you're running this from the project root directory."
    exit 1
fi

# Create build environment
echo "🔧 Setting up build environment..."
python3 -m venv build_env
source build_env/bin/activate

# Install dependencies
echo "📦 Installing dependencies..."
pip install --upgrade pip
pip install -r requirements.txt
pip install pyinstaller

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf build/ dist/

# Build binary
echo "🔨 Building Linux binary..."
pyinstaller --onefile \
    --name repository-monitor \
    --add-data "templates:templates" \
    --add-data "static:static" \
    --add-data "docs:docs" \
    --add-data "marketing:marketing" \
    --hidden-import=repository_backends.svn_backend \
    --hidden-import=repository_backends.git_backend \
    --hidden-import=repository_backends.base \
    --hidden-import=flask \
    --hidden-import=sqlite3 \
    --hidden-import=requests \
    --hidden-import=jinja2 \
    --collect-all=flask \
    --collect-all=jinja2 \
    --exclude-module=PyQt5 \
    --exclude-module=PySide6 \
    --exclude-module=tkinter \
    --exclude-module=matplotlib \
    --exclude-module=numpy \
    repository_monitor_binary.py

# Check if binary was created
if [ -f "dist/repository-monitor" ]; then
    echo "✅ Linux binary built successfully!"
    echo "📦 Binary location: $(pwd)/dist/repository-monitor"
    echo "📏 Binary size: $(du -h dist/repository-monitor | cut -f1)"
    
    # Make executable
    chmod +x dist/repository-monitor
    
    # Create distribution package
    echo "📦 Creating distribution package..."
    mkdir -p dist/linux-package
    cp dist/repository-monitor dist/linux-package/
    cp config.example.json dist/linux-package/
    
    # Create startup script
    cat > dist/linux-package/start-repository-monitor.sh << 'EOF'
#!/bin/bash
set -e

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Create data directory if it doesn't exist
mkdir -p data logs

# Initialize configuration if not exists
if [ ! -f data/config.json ]; then
    echo "Initializing configuration..."
    cp config.example.json data/config.json
    echo "Configuration initialized. Please customize data/config.json"
fi

# Set environment variables
export REPOSITORY_MONITOR_CONFIG="$SCRIPT_DIR/data/config.json"
export REPOSITORY_MONITOR_DATA_DIR="$SCRIPT_DIR/data"
export REPOSITORY_MONITOR_LOG_DIR="$SCRIPT_DIR/logs"

# Start Repository Monitor
echo "Starting Repository Monitor..."
echo "Access the web interface at: http://localhost:5000"
echo "Press Ctrl+C to stop"

./repository-monitor
EOF
    
    chmod +x dist/linux-package/start-repository-monitor.sh
    
    echo "✅ Distribution package created: $(pwd)/dist/linux-package/"
    echo ""
    echo "To deploy:"
    echo "  1. Copy dist/linux-package/ to your Linux server"
    echo "  2. Run: ./start-repository-monitor.sh"
    echo ""
    echo "For Docker deployment, use the binary in a minimal container."
    
else
    echo "❌ Binary build failed!"
    exit 1
fi

# Cleanup
deactivate
rm -rf build_env

echo "🎉 Build completed successfully!"
