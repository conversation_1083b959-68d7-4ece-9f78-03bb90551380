#!/bin/bash
# Deploy RepoSense AI to GitHub Container Registry (ghcr.io)

set -e

echo "📦 RepoSense AI - GitHub Container Registry Deployment"
echo "=========================================================="

# Configuration
GITHUB_USERNAME="${GITHUB_USERNAME:-your-github-username}"
GITHUB_TOKEN="${GITHUB_TOKEN:-your-github-token}"
IMAGE_NAME="repository-monitor"
VERSION="${VERSION:-latest}"
REGISTRY="ghcr.io"
FULL_IMAGE_NAME="${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}:${VERSION}"

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker daemon is not running"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "repository_monitor_binary.py" ] && [ ! -f "Dockerfile.linux-binary" ]; then
    echo "❌ Error: RepoSense AI source files not found!"
    echo "Make sure you're running this from the RepoSense AI directory."
    exit 1
fi

echo "📋 Build Configuration:"
echo "   GitHub Username: ${GITHUB_USERNAME}"
echo "   Registry: ${REGISTRY}"
echo "   Image Name: ${IMAGE_NAME}"
echo "   Version: ${VERSION}"
echo "   Full Image: ${FULL_IMAGE_NAME}"
echo ""

# Login to GitHub Container Registry
echo "🔐 Logging into GitHub Container Registry..."
if [ -n "${GITHUB_TOKEN}" ]; then
    echo "${GITHUB_TOKEN}" | docker login ${REGISTRY} -u ${GITHUB_USERNAME} --password-stdin
else
    echo "Please enter your GitHub Personal Access Token:"
    docker login ${REGISTRY} -u ${GITHUB_USERNAME}
fi

# Build multi-architecture images
echo "🏗️ Building multi-architecture Docker images..."

# Create and use buildx builder
docker buildx create --name repository-monitor-ghcr-builder --use 2>/dev/null || docker buildx use repository-monitor-ghcr-builder

# Build and push binary version
docker buildx build \
    --platform linux/amd64,linux/arm64 \
    --file Dockerfile.linux-binary \
    --tag ${FULL_IMAGE_NAME} \
    --tag ${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}:v2.1.0 \
    --tag ${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}:binary \
    --push \
    .

echo "✅ Binary image built and pushed successfully!"

# Build and push source version
echo "🪶 Building source version..."
docker buildx build \
    --platform linux/amd64,linux/arm64 \
    --file Dockerfile.production \
    --tag ${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}:source \
    --tag ${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}:v2.1.0-source \
    --push \
    .

echo "✅ Source version built and pushed successfully!"

# Also build latest tag if not already latest
if [ "${VERSION}" != "latest" ]; then
    docker buildx build \
        --platform linux/amd64,linux/arm64 \
        --file Dockerfile.linux-binary \
        --tag ${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}:latest \
        --push \
        .
    echo "✅ Latest tag also pushed!"
fi

# Verify the images
echo "🔍 Verifying pushed images..."
docker buildx imagetools inspect ${FULL_IMAGE_NAME}

echo ""
echo "🎉 Deployment to GitHub Container Registry completed successfully!"
echo ""
echo "📦 Available Images:"
echo "   ${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}:latest (binary)"
echo "   ${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}:v2.1.0 (binary)"
echo "   ${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}:binary (binary)"
echo "   ${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}:source (source-based)"
echo "   ${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}:v2.1.0-source (source-based)"
echo ""
echo "🚀 Usage Examples:"
echo "   docker run -d -p 5000:5000 ${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}:latest"
echo "   docker-compose:"
echo "     image: ${REGISTRY}/${GITHUB_USERNAME}/${IMAGE_NAME}:latest"
echo ""
echo "📖 GitHub Packages: https://github.com/${GITHUB_USERNAME}?tab=packages"
echo ""
echo "🔧 To make images public:"
echo "   1. Go to https://github.com/${GITHUB_USERNAME}?tab=packages"
echo "   2. Click on ${IMAGE_NAME} package"
echo "   3. Go to Package settings"
echo "   4. Change visibility to Public"
