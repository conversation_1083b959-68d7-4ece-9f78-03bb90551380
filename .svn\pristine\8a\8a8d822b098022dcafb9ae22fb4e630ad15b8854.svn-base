# Quick Start Script for Repository Monitor
# This script starts only the Repository Monitor, assuming your main Docker Compose is already running

param(
    [switch]$Build,
    [switch]$Logs,
    [switch]$Stop,
    [switch]$Status
)

$ErrorActionPreference = "Stop"

Write-Host "Repository Monitor Quick Start" -ForegroundColor Cyan
Write-Host "==============================" -ForegroundColor Cyan

# Check if main services are running
Write-Host "Checking existing Docker services..." -ForegroundColor Yellow
$ollamaRunning = docker ps --format "table {{.Names}}" | Select-String "ollama-server-local"
if (-not $ollamaRunning) {
    Write-Host "❌ Error: ollama-server-local not found!" -ForegroundColor Red
    Write-Host "Please start your main docker-compose.yml first:" -ForegroundColor Yellow
    Write-Host "  docker-compose up -d" -ForegroundColor White
    exit 1
}

Write-Host "✓ Found ollama-server-local" -ForegroundColor Green

# Handle different actions
if ($Stop) {
    Write-Host "Stopping Repository Monitor..." -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml down
    Write-Host "✓ Repository Monitor stopped" -ForegroundColor Green
    exit 0
}

if ($Status) {
    Write-Host "Repository Monitor Status:" -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml ps

    Write-Host "`nService URLs:" -ForegroundColor Yellow
    Write-Host "  Repository Monitor: http://localhost:5001" -ForegroundColor White
    Write-Host "  Open WebUI:  http://localhost:3000" -ForegroundColor White
    Write-Host "  LLM Proxy:   http://localhost:11440" -ForegroundColor White
    Write-Host "  Ollama API:  http://localhost:11434" -ForegroundColor White
    exit 0
}

if ($Build) {
    Write-Host "Building Repository Monitor..." -ForegroundColor Yellow
    docker-compose -f docker-compose.dev.yml build --no-cache
}

# Check if config exists
if (-not (Test-Path "config.json")) {
    if (Test-Path "config.example.json") {
        Write-Host "Creating config.json from example..." -ForegroundColor Yellow
        Copy-Item "config.example.json" "config.json"
        Write-Host "✓ Created config.json - please edit with your settings" -ForegroundColor Green
    } else {
        Write-Host "❌ Error: No configuration file found!" -ForegroundColor Red
        Write-Host "Please create config.json or config.example.json" -ForegroundColor Yellow
        exit 1
    }
}

# Start Repository Monitor
Write-Host "Starting Repository Monitor..." -ForegroundColor Yellow
docker-compose -f docker-compose.dev.yml up -d

# Wait for startup
Write-Host "Waiting for Repository Monitor to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check status
$status = docker-compose -f docker-compose.dev.yml ps repository-monitor
if ($status -match "Up") {
    Write-Host "✓ Repository Monitor started successfully!" -ForegroundColor Green
    Write-Host "`nService URLs:" -ForegroundColor Cyan
    Write-Host "  Repository Monitor: http://localhost:5000" -ForegroundColor White
    Write-Host "  Open WebUI:  http://localhost:3000" -ForegroundColor White

    if ($Logs) {
        Write-Host "`nShowing logs (Ctrl+C to exit):" -ForegroundColor Yellow
        docker-compose -f docker-compose.dev.yml logs -f repository-monitor
    } else {
        Write-Host "`nTo view logs: .\start-repository-monitor.ps1 -Logs" -ForegroundColor Yellow
        Write-Host "To stop:      .\start-repository-monitor.ps1 -Stop" -ForegroundColor Yellow
        Write-Host "To rebuild:   .\start-repository-monitor.ps1 -Build" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ Error: Repository Monitor failed to start!" -ForegroundColor Red
    Write-Host "Check logs with: docker-compose -f docker-compose.dev.yml logs repository-monitor" -ForegroundColor Yellow
    exit 1
}
