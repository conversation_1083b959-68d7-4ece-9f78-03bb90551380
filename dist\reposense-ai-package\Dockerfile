# RepoSense AI - Binary Docker Deployment
FROM ubuntu:22.04

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    subversion \
    git \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd -r repository-monitor && \
    useradd -r -g repository-monitor -d /app -s /bin/bash repository-monitor

# Create non-root user
RUN groupadd -r reposense-ai && \
    useradd -r -g reposense-ai -d /app -s /bin/bash reposense-ai

# Set working directory
WORKDIR /app

# Copy binary and files
COPY reposense-ai* /app/
COPY config.example.json /app/
COPY docs /app/docs
COPY marketing /app/marketing
COPY start-reposense-ai.sh /app/

# Create data directories and set permissions
RUN mkdir -p /app/data /app/logs && \
    chmod +x /app/reposense-ai* /app/start-reposense-ai.sh && \
    chown -R reposense-ai:reposense-ai /app

# Switch to non-root user
USER reposense-ai

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Start the application
CMD ["./start-reposense-ai.sh"]
