#!/usr/bin/env python3
"""
Test script to verify the encoding fix for SVN diff with binary files
"""

import requests
import json

def test_encoding_fix():
    """Test the encoding fix for revision 69 that had UTF-8 decode issues"""
    
    base_url = "http://localhost:5001"
    
    # Test document ID for revision 69 that was causing encoding issues
    doc_id = "e65f7062-d497-4a3d-aca5-56ddf1964361_69"
    
    print("🧪 Testing Encoding Fix for SVN Diff")
    print("=" * 50)
    
    # Test 1: Try to get diff for revision 69 (the problematic one)
    print(f"\n1. Testing diff for revision 69 (previously failing):")
    try:
        response = requests.get(f"{base_url}/api/documents/{doc_id}/diff?format=unified")
        if response.status_code == 200:
            result = response.json()
            diff_content = result.get('diff', '')
            print(f"   ✅ Status: {response.status_code}")
            print(f"   ✅ Diff length: {len(diff_content)} characters")
            
            # Check if we got a proper response (not an error message)
            if "Error running SVN diff: 'utf-8' codec can't decode" in diff_content:
                print(f"   ❌ Still getting UTF-8 decode error")
            elif "Binary file content detected" in diff_content:
                print(f"   ✅ Properly detected binary content")
            elif "No differences found" in diff_content:
                print(f"   ✅ No differences found (expected for some revisions)")
            elif diff_content.startswith("Index:") or diff_content.startswith("==="):
                print(f"   ✅ Got valid diff content")
            else:
                print(f"   ℹ️  Got response: {diff_content[:100]}...")
                
        else:
            print(f"   ❌ Error: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 2: Try side-by-side format as well
    print(f"\n2. Testing side-by-side diff format:")
    try:
        response = requests.get(f"{base_url}/api/documents/{doc_id}/diff?format=side-by-side")
        if response.status_code == 200:
            result = response.json()
            diff_content = result.get('diff', '')
            print(f"   ✅ Status: {response.status_code}")
            print(f"   ✅ Format: {result.get('format', 'unknown')}")
            
            # Check if we got a proper response
            if "Error running SVN diff: 'utf-8' codec can't decode" in diff_content:
                print(f"   ❌ Still getting UTF-8 decode error")
            elif "Binary file content detected" in diff_content:
                print(f"   ✅ Properly detected binary content")
            elif "<table" in diff_content and "diff-table" in diff_content:
                print(f"   ✅ Got valid side-by-side HTML diff")
            else:
                print(f"   ℹ️  Got response: {diff_content[:100]}...")
                
        else:
            print(f"   ❌ Error: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 3: Test document view with diff
    print(f"\n3. Testing document view with diff inclusion:")
    try:
        response = requests.get(f"{base_url}/documents/{doc_id}?include_diff=true&diff_format=unified")
        if response.status_code == 200:
            content = response.text
            print(f"   ✅ Status: {response.status_code}")
            print(f"   ✅ Contains diff section: {'diff-content' in content}")
            
            # Check for encoding error in the HTML
            if "'utf-8' codec can't decode" in content:
                print(f"   ❌ UTF-8 decode error still present in document view")
            else:
                print(f"   ✅ No encoding errors in document view")
                
        else:
            print(f"   ❌ Error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 4: Test a few other revisions to make sure we didn't break anything
    print(f"\n4. Testing other revisions for regression:")
    test_revisions = ["e65f7062-d497-4a3d-aca5-56ddf1964361_5", "e65f7062-d497-4a3d-aca5-56ddf1964361_3"]
    
    for test_doc_id in test_revisions:
        try:
            response = requests.get(f"{base_url}/api/documents/{test_doc_id}/diff?format=unified")
            if response.status_code == 200:
                result = response.json()
                diff_content = result.get('diff', '')
                revision = test_doc_id.split('_')[-1]
                print(f"   ✅ Revision {revision}: {len(diff_content)} chars, no encoding errors")
            else:
                print(f"   ⚠️  Revision {test_doc_id.split('_')[-1]}: {response.status_code}")
        except Exception as e:
            print(f"   ❌ Revision {test_doc_id.split('_')[-1]}: Exception {e}")
    
    print("\n" + "=" * 50)
    print("✅ Encoding Fix Testing Complete!")

if __name__ == "__main__":
    test_encoding_fix()
