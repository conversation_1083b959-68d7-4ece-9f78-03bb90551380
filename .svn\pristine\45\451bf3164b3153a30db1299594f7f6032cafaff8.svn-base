{"users": [], "repositories": [], "ollama_host": "http://ollama:11434", "ollama_model": "llama2", "check_interval": 300, "svn_server_url": null, "svn_server_username": null, "svn_server_password": null, "smtp_host": "localhost", "smtp_port": 587, "smtp_username": null, "smtp_password": null, "email_from": "<EMAIL>", "email_recipients": [], "output_dir": "/app/data/output", "generate_docs": true, "send_emails": true, "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "c4523911715b8ced47fcabbd084821f48d83bec3594b7e6e0d5cc326b4d676d4", "web_log_entries": 300}