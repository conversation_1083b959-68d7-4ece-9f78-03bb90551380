#!/usr/bin/env python3

from ollama_client import OllamaClient
from models import Config, CommitInfo

def test_diff_inclusion():
    """Test that the improved prompt includes diff content in documentation"""
    
    # Create config
    config = Config()
    config.ollama_host = 'http://ollama-server-local:11434'
    config.ollama_model = 'llama3:latest'

    # Create client
    ollama_client = OllamaClient(config)

    # Create a test commit with a simple diff
    test_diff = """--- a/test/file.py
+++ b/test/file.py
@@ -1,3 +1,6 @@
 def hello():
     print("Hello World")
+
+def goodbye():
+    print("Goodbye World")
"""

    test_commit = CommitInfo(
        revision='TEST',
        author='test_user',
        date='2024-01-01',
        message='Test commit for diff inclusion',
        changed_paths=['/test/file.py'],
        diff=test_diff,
        repository_id='test',
        repository_name='test_repo'
    )

    print('Testing documentation generation with diff...')
    print(f'Test diff:\n{test_commit.diff}')

    # Generate documentation
    documentation = ollama_client.generate_documentation(test_commit)

    if documentation:
        print('\n=== GENERATED DOCUMENTATION ===')
        print(documentation)
        
        # Check if diff is included
        if '```diff' in documentation or '--- a/' in documentation or '+++ b/' in documentation:
            print('\n✅ SUCCESS: Diff content appears to be included in documentation!')
        else:
            print('\n❌ ISSUE: Diff content does not appear to be included in documentation')
    else:
        print('No documentation generated')

if __name__ == "__main__":
    test_diff_inclusion()
