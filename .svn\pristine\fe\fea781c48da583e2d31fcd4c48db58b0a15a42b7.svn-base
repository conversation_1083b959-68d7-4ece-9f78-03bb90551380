@echo off
echo === SVN Monitor Development Setup for Windows ===
echo.

REM Check if <PERSON><PERSON> is running
echo Checking Docker Desktop status...
docker version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker Desktop is not running or not installed
    echo Please start Docker Desktop and try again
    pause
    exit /b 1
)
echo Docker Desktop is running

REM Check docker-compose
echo Checking Docker Compose...
docker-compose version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Docker Compose is not available
    pause
    exit /b 1
)
echo Docker Compose is available

REM Create directories
echo Creating development directories...
if not exist "data" mkdir data
if not exist "data\logs" mkdir data\logs
if not exist "data\output" mkdir data\output
if not exist "data\output\repositories" mkdir data\output\repositories
echo Directories created

REM Setup configuration
echo Setting up development configuration...
if not exist "config.json" (
    if exist "config.example.json" (
        copy "config.example.json" "config.json" >nul
        echo Created config.json from example
        echo WARNING: Please edit config.json with your development settings
    ) else (
        echo ERROR: config.example.json not found
        pause
        exit /b 1
    )
) else (
    echo config.json already exists
)

REM Check if existing Docker services are running
echo Checking existing Docker services...
docker ps --format "table {{.Names}}" | findstr "ollama-server-local" >nul
if errorlevel 1 (
    echo WARNING: ollama-server-local not found. Make sure your main Docker Compose is running.
    echo Please start your main docker-compose.yml first, then run this script again.
    pause
    exit /b 1
)

echo Found existing Ollama server, proceeding with SVN Monitor setup...

REM Build SVN Monitor (no need to pull Ollama since it's already running)
echo Building SVN Monitor development environment...
docker-compose -f docker-compose.dev.yml build

REM Start SVN Monitor service
echo Starting SVN Monitor service...
docker-compose -f docker-compose.dev.yml up -d

REM Wait for services
echo Waiting for services to start...
timeout /t 10 /nobreak >nul

REM Check status
echo Checking service status...
docker-compose -f docker-compose.dev.yml ps

echo.
echo === Development Environment Setup Complete ===
echo.
echo Services:
echo   • SVN Monitor Web Interface: http://localhost:5000
echo   • Ollama API: http://localhost:11434
echo.
echo Useful Commands:
echo   • View logs: docker-compose -f docker-compose.dev.yml logs -f
echo   • Stop services: docker-compose -f docker-compose.dev.yml down
echo   • Restart services: docker-compose -f docker-compose.dev.yml restart
echo.
echo Next Steps:
echo   1. Edit config.json with your SVN repository details
echo   2. Access http://localhost:5000 to configure users and repositories
echo   3. Test the monitoring functionality
echo.
echo Press any key to open the web interface...
pause >nul
start http://localhost:5000
