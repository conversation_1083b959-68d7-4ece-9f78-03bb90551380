# Optional document processing libraries for enhanced format support
# Install with: pip install -r requirements-docs.txt

# Microsoft Word document support
python-docx>=0.8.11        # For .docx files (recommended)
docx2txt>=0.8              # Alternative .docx reader
textract>=1.6.3            # Universal document extractor (supports .doc and .docx)

# RTF document support
striprtf>=0.0.26           # For Rich Text Format files

# OpenDocument support
odfpy>=1.4.1               # For .odt files

# PDF text extraction (if needed for content analysis)
PyPDF2>=3.0.1              # For PDF text extraction
pdfplumber>=0.7.6          # Alternative PDF processor

# Additional text processing
chardet>=5.0.0             # Character encoding detection
