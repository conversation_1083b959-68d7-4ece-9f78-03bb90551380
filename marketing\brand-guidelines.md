# RepoSense AI - Brand Guidelines

## Visual Identity & Brand Standards

---

## 🎨 **Brand Identity**

### **Brand Essence**
RepoSense AI embodies **private AI intelligence**, **professional excellence**, and **developer empowerment**. Our brand represents the perfect fusion of cutting-edge private AI technology with practical, user-friendly solutions that keep your code secure.

### **Brand Personality**
- **Intelligent**: AI-powered, data-driven, insightful
- **Professional**: Enterprise-grade, reliable, trustworthy
- **Approachable**: User-friendly, intuitive, accessible
- **Innovative**: Forward-thinking, modern, progressive
- **Efficient**: Streamlined, optimized, productive

### **Brand Promise**
"Making sense of your code with private AI intelligence that transforms repository management while keeping your sensitive source code completely secure on your own infrastructure."

---

## 🎯 **Logo & Visual Identity**

### **Primary Logo**
```
🔍 RepoSense AI
   Intelligent Repository Management
```

### **Logo Variations**
- **Full Logo**: Complete logo with tagline for primary use
- **Logo Mark**: Icon only for small applications
- **Horizontal**: Side-by-side layout for wide formats
- **Stacked**: Vertical layout for narrow formats
- **Monochrome**: Single-color versions for special applications

### **Logo Usage Guidelines**
- **Minimum Size**: 120px width for digital, 1 inch for print
- **Clear Space**: Minimum clear space equal to the height of the "R" in Repository
- **Backgrounds**: Ensure sufficient contrast on all backgrounds
- **Modifications**: Never modify, stretch, or alter the logo proportions

---

## 🌈 **Color Palette**

### **Primary Colors**

**Repository Blue** - `#2563EB`
- **Usage**: Primary brand color, headers, CTAs, links
- **RGB**: 37, 99, 235
- **CMYK**: 84, 58, 0, 8
- **Pantone**: 2727 C

**Intelligence Green** - `#059669`
- **Usage**: Success states, positive metrics, AI indicators
- **RGB**: 5, 150, 105
- **CMYK**: 97, 0, 30, 41
- **Pantone**: 3415 C

**Professional Gray** - `#374151`
- **Usage**: Body text, secondary elements, professional contexts
- **RGB**: 55, 65, 81
- **CMYK**: 32, 20, 0, 68
- **Pantone**: 432 C

### **Secondary Colors**

**Warning Orange** - `#F59E0B`
- **Usage**: Warnings, attention states, risk indicators
- **RGB**: 245, 158, 11
- **CMYK**: 0, 36, 95, 4

**Error Red** - `#DC2626`
- **Usage**: Errors, critical states, high-risk indicators
- **RGB**: 220, 38, 38
- **CMYK**: 0, 83, 83, 14

**Info Blue** - `#3B82F6`
- **Usage**: Information, neutral states, secondary actions
- **RGB**: 59, 130, 246
- **CMYK**: 76, 47, 0, 4

### **Neutral Colors**

**Light Gray** - `#F3F4F6` (Backgrounds)
**Medium Gray** - `#9CA3AF` (Borders, dividers)
**Dark Gray** - `#1F2937` (High contrast text)
**White** - `#FFFFFF` (Primary background)

---

## 📝 **Typography**

### **Primary Typeface: Inter**
- **Usage**: All digital applications, web interface, documentation
- **Weights**: Light (300), Regular (400), Medium (500), Semibold (600), Bold (700)
- **Characteristics**: Modern, readable, professional, optimized for screens

### **Secondary Typeface: JetBrains Mono**
- **Usage**: Code snippets, technical documentation, terminal outputs
- **Weights**: Regular (400), Medium (500), Bold (700)
- **Characteristics**: Monospace, developer-friendly, high readability

### **Fallback Fonts**
- **Sans-serif**: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif
- **Monospace**: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", monospace

### **Typography Scale**
- **H1**: 2.25rem (36px) - Page titles
- **H2**: 1.875rem (30px) - Section headers
- **H3**: 1.5rem (24px) - Subsection headers
- **H4**: 1.25rem (20px) - Component headers
- **Body**: 1rem (16px) - Regular text
- **Small**: 0.875rem (14px) - Captions, metadata

---

## 🖼️ **Visual Style**

### **Design Principles**
- **Clean & Modern**: Minimal clutter, contemporary aesthetics
- **Professional**: Enterprise-appropriate, trustworthy appearance
- **Accessible**: WCAG 2.1 AA compliant, inclusive design
- **Consistent**: Unified visual language across all touchpoints

### **UI Elements**

**Buttons**
- **Primary**: Repository Blue background, white text, 8px border radius
- **Secondary**: White background, Repository Blue border and text
- **Danger**: Error Red background, white text
- **Disabled**: Light Gray background, Medium Gray text

**Cards & Containers**
- **Background**: White with subtle shadow (0 1px 3px rgba(0,0,0,0.1))
- **Border Radius**: 8px for cards, 4px for small elements
- **Padding**: 16px standard, 24px for large cards
- **Borders**: 1px solid Light Gray

**Icons**
- **Style**: Outline style, 2px stroke weight
- **Size**: 16px, 20px, 24px standard sizes
- **Color**: Inherit from parent text color
- **Library**: Heroicons or similar consistent icon set

---

## 📱 **Application Guidelines**

### **Web Interface**
- **Layout**: Clean, spacious design with generous white space
- **Navigation**: Consistent sidebar with clear hierarchy
- **Content**: Card-based layout with clear visual separation
- **Responsive**: Mobile-first design with breakpoints at 768px, 1024px, 1280px

### **Documentation**
- **Headers**: Use brand colors for section headers
- **Code Blocks**: Dark background with syntax highlighting
- **Callouts**: Colored left border with appropriate background tint
- **Images**: Consistent styling with subtle borders and shadows

### **Marketing Materials**
- **Headers**: Repository Blue for primary headers
- **Backgrounds**: White or Light Gray with subtle gradients
- **Charts**: Use brand color palette consistently
- **Photography**: Professional, technology-focused imagery

---

## 💬 **Voice & Tone**

### **Brand Voice Characteristics**
- **Professional**: Knowledgeable, competent, trustworthy
- **Approachable**: Friendly, helpful, accessible
- **Confident**: Assured, capable, reliable
- **Clear**: Direct, concise, understandable

### **Tone Guidelines**

**For Technical Documentation**
- Clear, precise, instructional
- Use active voice and imperative mood
- Provide context and examples
- Avoid jargon without explanation

**For Marketing Materials**
- Confident but not boastful
- Focus on benefits and outcomes
- Use concrete examples and metrics
- Professional yet engaging

**For User Interface**
- Concise and actionable
- Helpful and informative
- Consistent terminology
- Error messages that guide toward solutions

### **Writing Style**
- **Sentence Structure**: Vary length, prefer shorter sentences
- **Paragraphs**: Keep concise, one main idea per paragraph
- **Lists**: Use bullet points and numbered lists for clarity
- **Headings**: Descriptive and scannable

---

## 🚫 **Brand Don'ts**

### **Visual Don'ts**
- ❌ Don't use colors outside the approved palette
- ❌ Don't stretch or distort the logo
- ❌ Don't use low-contrast color combinations
- ❌ Don't mix different icon styles
- ❌ Don't use decorative fonts for body text

### **Voice Don'ts**
- ❌ Don't use overly technical jargon without explanation
- ❌ Don't make exaggerated claims without evidence
- ❌ Don't use humor that might be misunderstood
- ❌ Don't use passive voice excessively
- ❌ Don't be overly casual in professional contexts

---

## 📋 **Brand Application Checklist**

### **Before Publishing Any Material**
- ✅ Logo used correctly with proper sizing and spacing
- ✅ Colors match approved palette (use hex codes)
- ✅ Typography follows established hierarchy
- ✅ Voice and tone appropriate for audience
- ✅ Visual elements consistent with brand guidelines
- ✅ Accessibility standards met (contrast, readability)
- ✅ Content reviewed for accuracy and clarity

### **Quality Assurance**
- ✅ Test on multiple devices and screen sizes
- ✅ Verify color accuracy across different displays
- ✅ Check for spelling and grammar errors
- ✅ Ensure all links and interactive elements work
- ✅ Validate against brand guidelines

---

## 📞 **Brand Governance**

### **Brand Approval Process**
1. **Review**: All materials reviewed against brand guidelines
2. **Approval**: Marketing team approval for external materials
3. **Archive**: Approved materials stored in brand asset library
4. **Updates**: Regular review and updates of brand guidelines

### **Brand Asset Library**
- **Location**: `/marketing/assets/` directory
- **Contents**: Logos, color swatches, fonts, templates
- **Access**: Available to all team members
- **Updates**: Maintained by marketing team

These brand guidelines ensure consistent, professional representation of RepoSense AI across all touchpoints while maintaining the intelligent, approachable, and innovative brand personality that resonates with our target audience of development professionals.
