# RepoSense AI

**Private AI-Powered Repository Intelligence Platform**

Making sense of your code with intelligent analysis, comprehensive documentation generation, and advanced monitoring capabilities - all while keeping your data secure on your infrastructure.

[![Docker](https://img.shields.io/badge/Docker-Ready-blue?logo=docker)](https://www.docker.com/)
[![Python](https://img.shields.io/badge/Python-3.8+-green?logo=python)](https://python.org)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

## ✨ Features

### Core Functionality
- 🔒 **Private Local AI** - Your code never leaves your infrastructure (any LLM supported)
- 🔌 **Plugin Architecture** - Extensible backend system (SVN supported, Git planned)
- 🤖 **AI Documentation** - Automatic commit analysis with flexible LLM integration
- 📊 **Document Management** - Browse, search, and manage generated documentation
- 🌐 **Modern Web Interface** - Responsive UI with real-time monitoring
- 🐳 **Docker Ready** - Complete containerization with hot-reload development
- 🔍 **Repository Discovery** - Automatic repository detection and configuration

### Advanced Features (v2.1.0)
- 🔒 **Complete Data Privacy** - Local AI processing with universal LLM support (Ollama, OpenAI, Claude)
- 👥 **User Feedback System** - Code review tracking, documentation ratings, and risk assessment overrides
- 📋 **Side-by-Side Diff Viewer** - Advanced diff visualization with format switching
- 🔄 **Hybrid AI Analysis** - Fast heuristics with LLM fallback for robust metadata extraction
- 🛡️ **Robust Error Handling** - Multi-encoding support and binary file detection
- 📈 **Accurate Progress Tracking** - Fixed progress calculation for all revision ranges
- 🔐 **Enhanced Authentication** - Seamless SVN credential integration for diff viewing

## 🚀 Quick Start

```bash
# Clone the repository
git clone <repository-url>
cd repository-monitor

# Start with Docker (recommended)
docker-compose -f docker-compose.dev.yml up -d

# Access the web interface
open http://localhost:5001
```

## 📚 Documentation

| Guide | Description |
|-------|-------------|
| [📖 Getting Started](docs/index.md) | Complete setup and usage guide |
| [🐳 Docker Setup](docs/docker-setup.md) | Docker deployment and configuration |
| [🪟 Windows Development](docs/windows-development.md) | Windows-specific setup instructions |
| [🔧 Configuration](docs/configuration.md) | Configuration options and examples |
| [🏗️ Architecture](docs/design.md) | System design and plugin development |
| [🔗 Integration](docs/integration.md) | External service integration guide |

## 🛠️ System Requirements

- **Docker & Docker Compose** (recommended)
- **Python 3.8+** (for local development)
- **Git** (for cloning and development)

## 🏗️ Architecture

RepoSense AI uses a modern plugin-based architecture:

```
┌─────────────────────────────────────┐
│           Web Interface             │
├─────────────────────────────────────┤
│         Document Management         │
├─────────────────────────────────────┤
│          Monitor Service            │
├─────────────────────────────────────┤
│       Repository Backends           │
│    ┌─────────┐  ┌─────────────┐     │
│    │   SVN   │  │ Git (Future) │     │
│    └─────────┘  └─────────────┘     │
└─────────────────────────────────────┘
```

## 🔧 Configuration

Simple JSON configuration:

```json
{
  "repositories": [
    {
      "id": "my-project",
      "url": "http://svn-server/svn/project",
      "enabled": true
    }
  ],
  "ollama": {
    "host": "localhost",
    "port": 11434,
    "model": "llama2"
  }
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

See [Development Guide](docs/development.md) for detailed instructions.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Ollama](https://ollama.ai/) for AI integration
- [Flask](https://flask.palletsprojects.com/) for web framework
- [Bootstrap](https://getbootstrap.com/) for UI components
