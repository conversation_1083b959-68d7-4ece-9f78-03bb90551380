<!DOCTYPE html>
<html>
<head>
    <title>Download Fix Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1>Download Function Test</h1>
        
        <div class="card">
            <div class="card-header">
                <h5>Document Content</h5>
            </div>
            <div class="card-body">
                <pre id="rawContent">This is the main document content.
It has multiple lines.
And represents the actual document text.</pre>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header d-flex justify-content-between">
                <h5>Side-by-Side Diff (with CSS)</h5>
                <button class="btn btn-primary" onclick="downloadDocument()">Download</button>
            </div>
            <div class="card-body">
                <!-- This simulates the side-by-side diff with CSS -->
                <div id="diffContentCode" style="max-height: 600px; overflow-y: auto;">
                    <style>
                        .side-by-side-diff {
                            font-family: 'Courier New', monospace;
                            font-size: 12px;
                            border: 1px solid #ddd;
                            border-radius: 4px;
                            overflow: hidden;
                        }
                        .diff-header {
                            background-color: #f8f9fa;
                            padding: 8px 12px;
                            border-bottom: 1px solid #ddd;
                            font-weight: bold;
                        }
                    </style>
                    
                    <div class="side-by-side-diff">
                        <div class="diff-header">📄 README.md</div>
                        <table class="diff-table">
                            <tr>
                                <td class="line-number">1</td>
                                <td class="line-added">+This is a test file to verify repository monitoring is working.</td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <!-- Hidden element with raw diff content for copying/downloading -->
                <div id="rawDiffContent" style="display: none;">Index: README.md
===================================================================
--- README.md	(revision 0)
+++ README.md	(revision 1)
@@ -0,0 +1 @@
+This is a test file to verify repository monitoring is working.</div>
            </div>
        </div>
        
        <div class="mt-3">
            <p><strong>Test Instructions:</strong></p>
            <ol>
                <li>Click the "Download" button above</li>
                <li>Check the downloaded file content</li>
                <li>The diff section should contain clean diff text, not CSS</li>
                <li>Should see "Index: README.md" not ".side-by-side-diff"</li>
            </ol>
        </div>
    </div>

    <script>
        function downloadDocument() {
            let content = document.getElementById('rawContent').textContent;
            const filename = 'test-document.md';

            // If diff content is available and displayed, append it to the download
            // Try to get raw diff content first (for side-by-side format)
            let diffElement = document.getElementById('rawDiffContent');
            
            // Fallback to regular diff content (for unified format)
            if (!diffElement || !diffElement.textContent.trim()) {
                diffElement = document.getElementById('diffContentCode');
            }
            
            if (diffElement && diffElement.textContent.trim()) {
                const cleanDiffContent = diffElement.textContent.trim();
                content += '\n\n---\n\n## Code Changes (Diff)\n\n```diff\n' + cleanDiffContent + '\n```';
            }

            const blob = new Blob([content], { type: 'text/markdown' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        }
    </script>
</body>
</html>
