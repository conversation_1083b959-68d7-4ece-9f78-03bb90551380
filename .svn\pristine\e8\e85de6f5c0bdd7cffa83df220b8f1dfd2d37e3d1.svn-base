version: '3.8'

services:
  repository-monitor:
    build: .
    image: repository-monitor:latest
    container_name: repository-monitor
    restart: unless-stopped
    ports:
      - "5000:5000"  # Web interface
    volumes:
      - ./data:/app/data  # Persistent data storage for configs, logs, and repository outputs
      - ./config:/app/config:ro  # Optional: for external config files
    environment:
      - TZ=UTC
    networks:
      - repository-monitor-network
    depends_on:
      - ollama
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  ollama:
    image: ollama/ollama:latest
    container_name: ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    networks:
      - repository-monitor-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

volumes:
  ollama_data:
    driver: local

networks:
  repository-monitor-network:
    driver: bridge
