{% extends "base.html" %}

{% block title %}{{ document.display_name }} - Repository Monitor{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">{{ document.display_name }}</h1>
            <p class="page-subtitle">{{ document.repository_name }} - {{ document.commit_message }}</p>
        </div>
        <div>
            <a href="{{ url_for('documents_page') }}" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left"></i> Back to Documents
            </a>
            <button class="btn btn-outline-primary" onclick="downloadDocument()">
                <i class="fas fa-download"></i> Download
            </button>
        </div>
    </div>
</div>

<!-- Document Metadata -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm border-info" style="border-width: 2px;">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Document Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Repository:</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-primary">{{ document.repository_name }}</span>
                            </dd>
                            <dt class="col-sm-4">Revision:</dt>
                            <dd class="col-sm-8"><strong>{{ document.revision }}</strong></dd>
                            <dt class="col-sm-4">Author:</dt>
                            <dd class="col-sm-8">{{ document.author }}</dd>
                            <dt class="col-sm-4">Date:</dt>
                            <dd class="col-sm-8">{{ document.date.strftime('%Y-%m-%d %H:%M:%S UTC') }}</dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Filename:</dt>
                            <dd class="col-sm-8"><code>{{ document.filename }}</code></dd>
                            <dt class="col-sm-4">Size:</dt>
                            <dd class="col-sm-8">{{ (document.size / 1024)|round(1) }} KB</dd>
                            <dt class="col-sm-4">Path:</dt>
                            <dd class="col-sm-8"><small class="text-muted">{{ document.relative_path }}</small></dd>
                            <dt class="col-sm-4">Commit Message:</dt>
                            <dd class="col-sm-8">{{ document.commit_message }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Document Content -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm border-primary" style="border-width: 2px;">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <button class="btn btn-link text-white p-0 text-decoration-none"
                            type="button"
                            data-bs-toggle="collapse"
                            data-bs-target="#documentContent"
                            aria-expanded="true"
                            aria-controls="documentContent"
                            id="collapseToggle">
                        <i class="fas fa-file-alt me-2"></i>
                        <span>Document Content</span>
                        <i class="fas fa-chevron-up ms-2" id="collapseIcon"></i>
                    </button>
                </h5>
                <div>
                    <button class="btn btn-sm btn-outline-light" onclick="toggleRawView()" id="toggleRawBtn">
                        <i class="fas fa-code"></i> Raw View
                    </button>
                    <button class="btn btn-sm btn-light" onclick="copyToClipboard()">
                        <i class="fas fa-copy"></i> Copy
                    </button>
                </div>
            </div>
            <div class="collapse show" id="documentContent">
                <div class="card-body p-0">
                    <!-- Rendered Markdown View -->
                    <div id="renderedView" class="p-4">
                        <div class="markdown-content">
                            {{ content|markdown }}
                        </div>
                    </div>

                    <!-- Raw Text View -->
                    <div id="rawView" class="p-0" style="display: none;">
                        <pre class="mb-0 p-4" style="background-color: #f8f9fa; border: none; border-radius: 0;"><code id="rawContent">{{ content }}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card shadow-sm border-secondary" style="border-width: 2px;">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0"><i class="fas fa-tools me-2"></i> Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-danger" onclick="deleteDocument()">
                        <i class="fas fa-trash"></i> Delete Document
                    </button>
                    <a href="{{ url_for('documents_page') }}?repository={{ document.repository_id }}" class="btn btn-outline-info">
                        <i class="fas fa-folder"></i> View Repository Documents
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteDocumentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this document?</p>
                <p><strong>{{ document.display_name }}</strong></p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    This action cannot be undone.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                    <i class="fas fa-trash"></i> Delete Document
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.markdown-content {
    line-height: 1.6;
    color: #333;
}

.markdown-content h1 {
    border-bottom: 2px solid #e1e4e8;
    padding-bottom: 0.3em;
    margin-bottom: 1em;
    color: #24292e;
}

.markdown-content h2 {
    border-bottom: 1px solid #e1e4e8;
    padding-bottom: 0.3em;
    margin-top: 1.5em;
    margin-bottom: 1em;
    color: #24292e;
}

.markdown-content h3, .markdown-content h4 {
    margin-top: 1.25em;
    margin-bottom: 0.75em;
    color: #24292e;
}

.markdown-content pre {
    background-color: #f6f8fa;
    border-radius: 6px;
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
}

.markdown-content code {
    background-color: rgba(175, 184, 193, 0.2);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-size: 85%;
}

.markdown-content pre code {
    background-color: transparent;
    padding: 0;
}

.markdown-content ul {
    padding-left: 2em;
    margin-bottom: 1em;
}

.markdown-content li {
    margin-bottom: 0.25em;
}

.markdown-content strong {
    font-weight: 600;
}

.markdown-content p {
    margin-bottom: 1em;
}

.markdown-content blockquote {
    padding: 0 1em;
    color: #6a737d;
    border-left: 0.25em solid #dfe2e5;
    margin-bottom: 1em;
}
</style>
{% endblock %}

{% block scripts %}
<script>
let rawViewVisible = false;

// Handle collapse/expand icon changes
document.addEventListener('DOMContentLoaded', function() {
    const documentContent = document.getElementById('documentContent');
    const collapseIcon = document.getElementById('collapseIcon');

    documentContent.addEventListener('shown.bs.collapse', function () {
        collapseIcon.classList.remove('fa-chevron-down');
        collapseIcon.classList.add('fa-chevron-up');
    });

    documentContent.addEventListener('hidden.bs.collapse', function () {
        collapseIcon.classList.remove('fa-chevron-up');
        collapseIcon.classList.add('fa-chevron-down');
    });
});

function toggleRawView() {
    const renderedView = document.getElementById('renderedView');
    const rawView = document.getElementById('rawView');
    const toggleBtn = document.getElementById('toggleRawBtn');
    
    if (rawViewVisible) {
        renderedView.style.display = 'block';
        rawView.style.display = 'none';
        toggleBtn.innerHTML = '<i class="fas fa-code"></i> Raw View';
        rawViewVisible = false;
    } else {
        renderedView.style.display = 'none';
        rawView.style.display = 'block';
        toggleBtn.innerHTML = '<i class="fas fa-eye"></i> Rendered View';
        rawViewVisible = true;
    }
}

function copyToClipboard() {
    const content = document.getElementById('rawContent').textContent;
    navigator.clipboard.writeText(content).then(function() {
        // Show success feedback
        const btn = event.target.closest('button');
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        btn.classList.remove('btn-outline-primary');
        btn.classList.add('btn-success');
        
        setTimeout(() => {
            btn.innerHTML = originalHTML;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-primary');
        }, 2000);
    }).catch(function(err) {
        console.error('Could not copy text: ', err);
        alert('Failed to copy to clipboard');
    });
}

function downloadDocument() {
    const content = document.getElementById('rawContent').textContent;
    const filename = '{{ document.filename }}';
    
    const blob = new Blob([content], { type: 'text/markdown' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}

function deleteDocument() {
    new bootstrap.Modal(document.getElementById('deleteDocumentModal')).show();
}

function confirmDelete() {
    fetch('/api/documents/{{ document.id }}/delete', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = '{{ url_for("documents_page") }}';
        } else {
            alert('Error: ' + data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the document.');
    });
}
</script>
{% endblock %}
