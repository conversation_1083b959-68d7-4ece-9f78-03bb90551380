## Summary
This commit adds two alternative prime number algorithms (<PERSON><PERSON> of Sundaram and Miller<PERSON>bin probabilistic test) and enhances the functionality. The Sieve of Sundaram algorithm is an alternate to the basic trial division method, while the Miller-Rabin test provides a probabilistic algorithm for large numbers.

## Technical Details
1. **New algorithm implementation**: Added `miller_rabin_is_prime` function to perform the Miller-Rabin primality test on large numbers.
2. **Alternative Sieve Algorithm**: Implemented the Sieve of Sundaram algorithm for finding all primes up to a given limit, an alternative approach to the Sieve of Eratosthenes.
3. **Improved main demo**: Updated the main demo to compare algorithm results and include examples with larger numbers.

## Impact Assessment
1. **Codebase impact**: The changes are minor and primarily affect the prime number calculation functionality, without introducing new code structures or dependencies.
2. **Users/system impact**: Users may notice improvements in performance when testing large numbers with the Miller-Rabin test.
3. **Documentation Impact**: This commit does not directly impact documentation, but it provides a justification for adding alternative algorithms and enhancing functionality.

## Code Review Recommendation
Yes, this commit should be code reviewed due to its complexity, risk level (low), and potential for introducing bugs in the existing prime number calculation logic. The Sieve of Sundaram algorithm is an interesting addition and should be thoroughly tested before being considered a permanent part of the project.

## Documentation Impact
1. **Code documentation**: Update docstrings to reflect new algorithms and ensure consistency between implementations.
2. **User guides/tutorials**: Consider updating user guides or tutorials that explain prime number calculations, highlighting the different algorithmic approaches and their benefits/drawbacks for specific use cases.

## Recommendations
1. **Performance testing**: Implement a performance test suite to ensure efficiency of the new algorithms in different scenarios (e.g., small numbers vs. large numbers).
2. **Validation checks**: Incorporate validation checks for the Miller-Rabin test, ensuring it accurately identifies primes and doesn't falsely reject them.