{"repositories": [{"id": "example-repo", "name": "Example Repository", "url": "https://svn.example.com/repos/project/trunk", "username": null, "password": null, "enabled": false, "last_revision": 0, "email_recipients": ["<EMAIL>"]}], "ollama_host": "http://ollama:11434", "ollama_model": "llama2", "check_interval": 300, "smtp_host": "localhost", "smtp_port": 587, "smtp_username": null, "smtp_password": null, "email_from": "<EMAIL>", "email_recipients": ["<EMAIL>"], "output_dir": "/app/data", "generate_docs": true, "send_emails": false, "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "change-this-secret-key-in-production"}