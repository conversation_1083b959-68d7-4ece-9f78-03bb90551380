#!/usr/bin/env python3
"""
Test script to verify source file document creation functionality
"""

import sys
import os
sys.path.append('/app')

from datetime import datetime
from document_database import DocumentDatabase, DocumentRecord
import time

def test_source_file_creation():
    """Test creating source file documents manually"""

    # Initialize database
    db = DocumentDatabase('/app/data/documents.db')

    print("Testing source file document creation manually...")

    # Create test source file documents directly
    test_files = [
        {"path": "/README.md", "name": "README.md"},
        {"path": "/test_script.py", "name": "test_script.py"},
        {"path": "/prime_calculator.py", "name": "prime_calculator.py"}
    ]

    for i, file_info in enumerate(test_files, 1):
        try:
            # Create a source file document record
            source_doc_id = f"test-repo-id_{i}_{file_info['name'].replace('.', '_')}"

            source_doc_record = DocumentRecord(
                id=source_doc_id,
                repository_id="test-repo-id",
                repository_name="test_repo",
                revision=i,
                date=datetime.now(),
                filename=file_info['name'],
                filepath=file_info['path'],  # Use the actual repository path
                size=1000,  # Mock size
                author="test_author",
                commit_message=f"Test commit {i}",
                changed_paths=[file_info['path']],  # This file is the only changed file for this record
                code_review_recommended=None,
                code_review_priority=None,
                documentation_impact=None,
                risk_level=None,
                file_modified_time=time.time(),
                processed_time=datetime.now(),
                repository_url="http://test-repo.com/svn",
                repository_type="svn"
            )

            # Save the source file document to database
            if db.upsert_document(source_doc_record):
                print(f"✅ Created source file document: {source_doc_id}")
            else:
                print(f"❌ Failed to save source file document: {source_doc_id}")

        except Exception as e:
            print(f"❌ Error creating document for source file {file_info['path']}: {e}")
            import traceback
            traceback.print_exc()

    # Check if any documents were created
    print("\nChecking database for created documents...")
    import sqlite3
    conn = sqlite3.connect('/app/data/documents.db')
    cursor = conn.cursor()
    cursor.execute('SELECT id, filename, filepath, changed_paths FROM documents WHERE repository_id = ?', ("test-repo-id",))
    docs = cursor.fetchall()

    if docs:
        print(f"✅ Found {len(docs)} documents:")
        for doc in docs:
            print(f"  - {doc[0]}: {doc[1]} ({doc[2]})")
            print(f"    Changed paths: {doc[3]}")
    else:
        print("❌ No documents found in database")

    conn.close()

    print("\nTest completed!")

if __name__ == "__main__":
    test_source_file_creation()
