# Repository Monitor - Production Binary Deployment
# Docker Compose configuration for production binary deployment

version: '3.8'

services:
  repository-monitor:
    build:
      context: .
      dockerfile: Dockerfile.production
    image: repository-monitor:latest
    container_name: repository-monitor-prod
    restart: unless-stopped
    
    ports:
      - "5000:5000"
    
    volumes:
      # Persistent data storage
      - repository_data:/app/data
      - repository_logs:/app/logs
      
      # Configuration (optional override)
      # - ./config.production.json:/app/data/config.json:ro
      
      # SVN credentials (if needed)
      # - ~/.subversion:/home/<USER>/.subversion:ro
      
      # Git credentials (if needed)
      # - ~/.gitconfig:/home/<USER>/.gitconfig:ro
      # - ~/.git-credentials:/home/<USER>/.git-credentials:ro
    
    environment:
      # Application configuration
      - REPOSITORY_MONITOR_CONFIG=/app/data/config.json
      - REPOSITORY_MONITOR_DATA_DIR=/app/data
      - REPOSITORY_MONITOR_LOG_DIR=/app/logs
      
      # Flask configuration
      - FLASK_ENV=production
      - FLASK_DEBUG=false
      
      # Security settings
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    
    # Resource limits for production
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    # Network configuration
    networks:
      - repository-monitor-network

  # Optional: Ollama service for local AI
  ollama:
    image: ollama/ollama:latest
    container_name: repository-monitor-ollama
    restart: unless-stopped
    
    ports:
      - "11434:11434"
    
    volumes:
      - ollama_data:/root/.ollama
    
    environment:
      - OLLAMA_HOST=0.0.0.0
    
    # GPU support (uncomment if available)
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]
    
    networks:
      - repository-monitor-network

  # Optional: Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: repository-monitor-nginx
    restart: unless-stopped
    
    ports:
      - "80:80"
      - "443:443"
    
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    
    depends_on:
      - repository-monitor
    
    networks:
      - repository-monitor-network

volumes:
  repository_data:
    driver: local
  repository_logs:
    driver: local
  ollama_data:
    driver: local

networks:
  repository-monitor-network:
    driver: bridge
