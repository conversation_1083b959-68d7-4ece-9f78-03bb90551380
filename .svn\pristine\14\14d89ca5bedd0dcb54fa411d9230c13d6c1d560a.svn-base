# Add this service to your existing docker-compose.yml

  # Repository Monitor - AI-Powered Repository Documentation
  repository-monitor:
    container_name: repository-monitor
    build:
      context: ./repository-monitor  # Path to Repository Monitor source
      dockerfile: Dockerfile.production  # Source deployment
      # OR: dockerfile: Dockerfile.linux-binary  # Binary deployment
    restart: unless-stopped
    networks:
      - ollama-network
    ports:
      - "5000:5000"  # Repository Monitor web interface
    volumes:
      # Persistent data storage
      - repository_monitor_data:/app/data
      - repository_monitor_logs:/app/logs
      
      # Configuration (create this file)
      - ./repository-monitor/config.production.json:/app/data/config.json:ro
      
      # SVN credentials (if needed)
      - ~/.subversion:/home/<USER>/.subversion:ro
      
      # Git credentials (if needed) 
      - ~/.gitconfig:/home/<USER>/.gitconfig:ro
      - ~/.git-credentials:/home/<USER>/.git-credentials:ro
    environment:
      # Application configuration
      - REPOSITORY_MONITOR_CONFIG=/app/data/config.json
      - REPOSITORY_MONITOR_DATA_DIR=/app/data
      - REPOSITORY_MONITOR_LOG_DIR=/app/logs
      
      # Flask configuration
      - FLASK_ENV=production
      - FLASK_DEBUG=false
      
      # Connect to your existing Ollama service
      - OLLAMA_BASE_URL=http://ollama:11434
      
      # Security settings
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    depends_on:
      - ollama

# Add this volume to your volumes section:
volumes:
  repository_monitor_data:
  repository_monitor_logs:
  # ... your existing volumes ...
