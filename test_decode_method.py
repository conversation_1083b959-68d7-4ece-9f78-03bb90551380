#!/usr/bin/env python3
"""
Test script to verify the decode method works correctly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from diff_service import DiffService

def test_decode_method():
    """Test the _decode_svn_output method with various encoding scenarios"""
    
    print("🧪 Testing SVN Output Decoding Method")
    print("=" * 50)
    
    # Create a DiffService instance
    diff_service = DiffService()
    
    # Test 1: Valid UTF-8 content
    print("\n1. Testing valid UTF-8 content:")
    utf8_content = "Index: test.txt\n===================================================================\n--- test.txt\t(revision 1)\n+++ test.txt\t(revision 2)\n@@ -1 +1 @@\n-old line\n+new line\n".encode('utf-8')
    result = diff_service._decode_svn_output(utf8_content)
    print(f"   ✅ UTF-8 decoded successfully: {len(result)} chars")
    print(f"   ✅ Contains expected content: {'Index:' in result}")
    
    # Test 2: Content with problematic byte (0xe2)
    print("\n2. Testing content with problematic byte 0xe2:")
    # Create content that includes the problematic byte sequence
    problematic_content = b"Some text before\xe2\x80\x93 and after"  # em dash in UTF-8
    result = diff_service._decode_svn_output(problematic_content)
    print(f"   ✅ Problematic content decoded: {len(result)} chars")
    print(f"   ✅ Result: {repr(result)}")
    
    # Test 3: Invalid UTF-8 sequence
    print("\n3. Testing invalid UTF-8 sequence:")
    invalid_utf8 = b"Valid text\xe2\x28\xa1 more text"  # Invalid UTF-8 sequence
    result = diff_service._decode_svn_output(invalid_utf8)
    print(f"   ✅ Invalid UTF-8 handled: {len(result)} chars")
    print(f"   ✅ Result: {repr(result)}")
    
    # Test 4: Binary content detection
    print("\n4. Testing binary content detection:")
    binary_content = b"\x00\x01\x02\x03\x04\x05PDF content here\xff\xfe"
    result = diff_service._decode_svn_output(binary_content)
    print(f"   ✅ Binary content result: {result}")
    print(f"   ✅ Detected as binary: {'Binary file content detected' in result}")
    
    # Test 5: Empty content
    print("\n5. Testing empty content:")
    empty_content = b""
    result = diff_service._decode_svn_output(empty_content)
    print(f"   ✅ Empty content handled: '{result}'")
    
    # Test 6: Latin-1 encoded content
    print("\n6. Testing Latin-1 encoded content:")
    latin1_content = "Café résumé naïve".encode('latin-1')
    result = diff_service._decode_svn_output(latin1_content)
    print(f"   ✅ Latin-1 decoded: {result}")
    
    print("\n" + "=" * 50)
    print("✅ Decode Method Testing Complete!")

if __name__ == "__main__":
    test_decode_method()
