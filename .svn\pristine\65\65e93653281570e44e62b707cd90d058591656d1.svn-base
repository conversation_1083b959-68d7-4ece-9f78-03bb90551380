#!/usr/bin/env python3
"""
RepoSense AI Configuration Validator and Fixer

This script validates and fixes configuration issues by:
1. Checking environment variables (highest priority)
2. Checking configuration files (medium priority)
3. Providing clear recommendations

Configuration Priority:
1. Environment Variables (OLLAMA_BASE_URL, OLLAMA_MODEL)
2. Configuration File (config.dev.json, config.json)
3. Application Defaults (fallback only)
"""

import json
import os
import sys
from pathlib import Path

def check_environment_variables():
    """Check for problematic environment variables"""
    print("=== Environment Variables ===")
    
    problematic_vars = [
        'OLLAMA_BASE_URL',
        'OLLAMA_HOST', 
        'OLLAMA_MODEL'
    ]
    
    issues_found = []
    
    for var in problematic_vars:
        value = os.environ.get(var)
        if value:
            print(f"{var} = {value}")
            if 'ollama-server-local' in value:
                issues_found.append(f"{var} contains 'ollama-server-local'")
        else:
            print(f"{var} = (not set)")
    
    return issues_found

def check_config_files():
    """Check all possible config files"""
    print("\n=== Configuration Files ===")
    
    config_paths = [
        "/app/data/config.json",
        "data/config.json"
    ]
    
    issues_found = []
    
    for config_path in config_paths:
        if os.path.exists(config_path):
            print(f"\nFound: {config_path}")
            try:
                with open(config_path, 'r') as f:
                    config_data = json.load(f)
                
                ollama_host = config_data.get('ollama_host', 'NOT_SET')
                ollama_model = config_data.get('ollama_model', 'NOT_SET')
                
                print(f"  ollama_host: {ollama_host}")
                print(f"  ollama_model: {ollama_model}")
                
                if 'ollama-server-local' in str(ollama_host):
                    issues_found.append(f"{config_path} has ollama-server-local in ollama_host")
                
            except Exception as e:
                print(f"  ERROR reading file: {e}")
                issues_found.append(f"Cannot read {config_path}: {e}")
        else:
            print(f"Not found: {config_path}")
    
    return issues_found

def fix_config_file(config_path, correct_host, correct_model):
    """Fix a specific config file"""
    print(f"\nFixing {config_path}...")
    
    try:
        # Read current config
        with open(config_path, 'r') as f:
            config_data = json.load(f)
        
        # Update values
        old_host = config_data.get('ollama_host', 'NOT_SET')
        old_model = config_data.get('ollama_model', 'NOT_SET')
        
        config_data['ollama_host'] = correct_host
        config_data['ollama_model'] = correct_model
        
        # Write back
        with open(config_path, 'w') as f:
            json.dump(config_data, f, indent=2)
        
        print(f"  ✅ Updated ollama_host: {old_host} -> {correct_host}")
        print(f"  ✅ Updated ollama_model: {old_model} -> {correct_model}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Failed to fix {config_path}: {e}")
        return False

def main():
    print("🔧 RepoSense AI Ollama Configuration Fixer")
    print("=" * 50)
    
    # Check for issues
    env_issues = check_environment_variables()
    config_issues = check_config_files()
    
    all_issues = env_issues + config_issues
    
    if not all_issues:
        print("\n✅ No issues found!")
        return
    
    print(f"\n⚠️  Found {len(all_issues)} issues:")
    for i, issue in enumerate(all_issues, 1):
        print(f"  {i}. {issue}")
    
    # Ask for fix
    print(f"\n🔧 Recommended fixes:")
    print(f"  1. Set correct Ollama host (e.g., http://************:11434 or http://ollama:11434)")
    print(f"  2. Set correct model (e.g., codeqwen:7b-chat-v1.5-q8_0)")
    
    # Interactive fix
    try:
        correct_host = input(f"\nEnter correct Ollama host URL: ").strip()
        if not correct_host:
            print("❌ No host provided, exiting")
            return
        
        correct_model = input(f"Enter correct Ollama model name: ").strip()
        if not correct_model:
            print("❌ No model provided, exiting")
            return
        
        # Fix config files
        config_paths = [
            "/app/config.dev.json",
            "/app/config.json", 
            "/app/data/config.json",
            "config.dev.json",
            "config.json",
            "data/config.json"
        ]
        
        fixed_count = 0
        for config_path in config_paths:
            if os.path.exists(config_path):
                if fix_config_file(config_path, correct_host, correct_model):
                    fixed_count += 1
        
        print(f"\n✅ Fixed {fixed_count} configuration files")
        
        # Environment variable warnings
        if env_issues:
            print(f"\n⚠️  Environment variable issues still need manual fixing:")
            for issue in env_issues:
                print(f"  - {issue}")
            print(f"\nTo fix environment variables:")
            print(f"  1. Update your .env files")
            print(f"  2. Update docker-compose.yml environment section")
            print(f"  3. Restart the container")
        
        print(f"\n🚀 Next steps:")
        print(f"  1. Restart RepoSense AI container")
        print(f"  2. Check the web interface configuration page")
        print(f"  3. Test Ollama connection")
        
    except KeyboardInterrupt:
        print(f"\n❌ Cancelled by user")
    except Exception as e:
        print(f"\n❌ Error during interactive fix: {e}")

if __name__ == "__main__":
    main()
