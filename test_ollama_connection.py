#!/usr/bin/env python3
"""
Test script to verify Ollama connection and configuration
"""

import sys
import os
import requests
import json
from pathlib import Path

# Add current directory to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent))

from config_manager import Config<PERSON>anager
from ollama_client import OllamaClient

def test_direct_connection(host, model):
    """Test direct connection to Ollama API"""
    print(f"\n=== Testing Direct Connection ===")
    print(f"Host: {host}")
    print(f"Model: {model}")
    
    # Test /api/tags endpoint
    try:
        tags_url = f"{host}/api/tags"
        print(f"Testing {tags_url}...")
        response = requests.get(tags_url, timeout=10)
        response.raise_for_status()
        
        data = response.json()
        models = [m['name'] for m in data.get('models', [])]
        print(f"✅ /api/tags successful. Available models: {models}")
        
        if model not in models:
            print(f"⚠️  Warning: Model '{model}' not found in available models")
            return False
        else:
            print(f"✅ Model '{model}' is available")
            
    except Exception as e:
        print(f"❌ /api/tags failed: {e}")
        return False
    
    # Test /api/generate endpoint
    try:
        generate_url = f"{host}/api/generate"
        print(f"Testing {generate_url}...")
        
        payload = {
            "model": model,
            "prompt": "Hello, this is a test. Please respond with 'Test successful'.",
            "stream": False
        }
        
        response = requests.post(generate_url, json=payload, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        print(f"✅ /api/generate successful. Response: {result.get('response', 'No response')[:100]}...")
        return True
        
    except Exception as e:
        print(f"❌ /api/generate failed: {e}")
        return False

def test_config_loading():
    """Test configuration loading"""
    print(f"\n=== Testing Configuration Loading ===")
    
    # Test different config paths
    config_paths = [
        "/app/config.dev.json",
        "config.dev.json", 
        "data/config.json",
        "config.json"
    ]
    
    for config_path in config_paths:
        if os.path.exists(config_path):
            print(f"Found config file: {config_path}")
            try:
                config_manager = ConfigManager(config_path)
                config = config_manager.load_config()
                print(f"✅ Config loaded successfully")
                print(f"   Ollama Host: {config.ollama_host}")
                print(f"   Ollama Model: {config.ollama_model}")
                return config
            except Exception as e:
                print(f"❌ Failed to load config: {e}")
        else:
            print(f"Config file not found: {config_path}")
    
    return None

def test_environment_variables():
    """Test environment variable overrides"""
    print(f"\n=== Testing Environment Variables ===")
    
    env_vars = ['OLLAMA_BASE_URL', 'OLLAMA_MODEL', 'REPOSENSE_AI_WEB_HOST', 'REPOSENSE_AI_WEB_PORT']
    
    for var in env_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var} = {value}")
        else:
            print(f"   {var} = (not set)")

def test_ollama_client(config):
    """Test OllamaClient class"""
    print(f"\n=== Testing OllamaClient ===")
    
    try:
        client = OllamaClient(config)
        print(f"✅ OllamaClient created successfully")
        print(f"   Using host: {client.config.ollama_host}")
        print(f"   Using model: {client.config.ollama_model}")
        
        # Test connection
        if client.test_connection():
            print(f"✅ Connection test successful")
        else:
            print(f"❌ Connection test failed")
            return False
        
        # Test generation
        print("Testing content generation...")
        result = client.call_ollama("Hello, please respond with 'Test successful'.")
        if result:
            print(f"✅ Content generation successful: {result[:100]}...")
            return True
        else:
            print(f"❌ Content generation failed")
            return False
            
    except Exception as e:
        print(f"❌ OllamaClient test failed: {e}")
        return False

def main():
    print("🔍 RepoSense AI Ollama Connection Test")
    print("=" * 50)
    
    # Test environment variables
    test_environment_variables()
    
    # Test configuration loading
    config = test_config_loading()
    if not config:
        print("❌ Cannot proceed without valid configuration")
        return False
    
    # Test direct connection
    direct_success = test_direct_connection(config.ollama_host, config.ollama_model)
    
    # Test OllamaClient
    client_success = test_ollama_client(config)
    
    print(f"\n=== Test Summary ===")
    print(f"Direct Connection: {'✅ PASS' if direct_success else '❌ FAIL'}")
    print(f"OllamaClient:      {'✅ PASS' if client_success else '❌ FAIL'}")
    
    if direct_success and client_success:
        print(f"\n🎉 All tests passed! Ollama connection is working correctly.")
        return True
    else:
        print(f"\n❌ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
