#!/usr/bin/env python3
"""
Test script for user feedback functionality
"""

import requests
import json

def test_user_feedback():
    """Test all user feedback API endpoints"""
    
    # Test document ID (from previous tests)
    doc_id = "e65f7062-d497-4a3d-aca5-56ddf1964361_5"
    base_url = "http://localhost:5001"
    
    print("🧪 Testing User Feedback API Endpoints")
    print("=" * 50)
    
    # Test 1: Code Review Feedback
    print("\n1. Testing Code Review Feedback:")
    try:
        data = {
            "status": "approved",
            "comments": "Code looks good, well-structured prime number algorithms",
            "reviewer": "Test Reviewer"
        }
        response = requests.post(f"{base_url}/api/documents/{doc_id}/feedback/code-review", 
                               json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Status: {response.status_code}")
            print(f"   ✅ Message: {result.get('message', 'No message')}")
        else:
            print(f"   ❌ Error: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 2: Documentation Quality Feedback
    print("\n2. Testing Documentation Quality Feedback:")
    try:
        data = {
            "rating": 4,
            "comments": "Good documentation, covers all the new functions well",
            "updated_by": "Test User"
        }
        response = requests.post(f"{base_url}/api/documents/{doc_id}/feedback/documentation", 
                               json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Status: {response.status_code}")
            print(f"   ✅ Message: {result.get('message', 'No message')}")
        else:
            print(f"   ❌ Error: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 3: Risk Assessment Override
    print("\n3. Testing Risk Assessment Override:")
    try:
        data = {
            "risk_override": "LOW",
            "comments": "These are just mathematical functions, low risk",
            "updated_by": "Test Assessor"
        }
        response = requests.post(f"{base_url}/api/documents/{doc_id}/feedback/risk-assessment", 
                               json=data)
        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ Status: {response.status_code}")
            print(f"   ✅ Message: {result.get('message', 'No message')}")
        else:
            print(f"   ❌ Error: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 4: Invalid Data Validation
    print("\n4. Testing Input Validation:")
    try:
        # Test invalid code review status
        data = {"status": "invalid_status"}
        response = requests.post(f"{base_url}/api/documents/{doc_id}/feedback/code-review", 
                               json=data)
        if response.status_code == 400:
            print(f"   ✅ Correctly rejected invalid status: {response.status_code}")
        else:
            print(f"   ❌ Should have rejected invalid status: {response.status_code}")
        
        # Test invalid rating
        data = {"rating": 10}  # Should be 1-5
        response = requests.post(f"{base_url}/api/documents/{doc_id}/feedback/documentation", 
                               json=data)
        if response.status_code == 400:
            print(f"   ✅ Correctly rejected invalid rating: {response.status_code}")
        else:
            print(f"   ❌ Should have rejected invalid rating: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 5: Check if feedback is visible in document view
    print("\n5. Testing Document View with Feedback:")
    try:
        response = requests.get(f"{base_url}/documents/{doc_id}")
        if response.status_code == 200:
            content = response.text
            print(f"   ✅ Status: {response.status_code}")
            print(f"   ✅ Contains feedback section: {'User Feedback & Review' in content}")
            print(f"   ✅ Contains approved status: {'approved' in content}")
            print(f"   ✅ Contains rating: {'rating' in content.lower()}")
        else:
            print(f"   ❌ Error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 50)
    print("✅ User Feedback Testing Complete!")

if __name__ == "__main__":
    test_user_feedback()
