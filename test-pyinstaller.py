#!/usr/bin/env python3
"""
Test script to diagnose PyInstaller installation and execution issues
"""

import sys
import subprocess
import os
from pathlib import Path

def test_python_installation():
    """Test Python installation"""
    print("🐍 Python Installation Test")
    print("-" * 30)
    print(f"Python executable: {sys.executable}")
    print(f"Python version: {sys.version}")
    print(f"Python path: {sys.path[0]}")
    print()

def test_pip():
    """Test pip installation"""
    print("📦 Pip Test")
    print("-" * 30)
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Pip available: {result.stdout.strip()}")
        else:
            print(f"❌ Pip error: {result.stderr}")
    except Exception as e:
        print(f"❌ Pip test failed: {e}")
    print()

def test_pyinstaller_installation():
    """Test PyInstaller installation"""
    print("🔨 PyInstaller Installation Test")
    print("-" * 30)
    
    # Try to import PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller module found: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller module not found")
        return False
    
    # Try different ways to run PyInstaller
    commands_to_test = [
        ["pyinstaller", "--version"],
        [sys.executable, "-m", "PyInstaller", "--version"],
        [sys.executable, "-m", "pyinstaller", "--version"]
    ]
    
    for cmd in commands_to_test:
        try:
            print(f"Testing command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ Command works: {result.stdout.strip()}")
                return True
            else:
                print(f"❌ Command failed: {result.stderr.strip()}")
        except FileNotFoundError:
            print(f"❌ Command not found: {' '.join(cmd)}")
        except Exception as e:
            print(f"❌ Command error: {e}")
    
    return False

def find_pyinstaller_executable():
    """Find PyInstaller executable"""
    print("🔍 PyInstaller Executable Search")
    print("-" * 30)
    
    # Get Python installation directory
    python_dir = Path(sys.executable).parent
    print(f"Python directory: {python_dir}")
    
    # Common locations for PyInstaller
    possible_locations = [
        python_dir / "Scripts" / "pyinstaller.exe",
        python_dir / "Scripts" / "pyinstaller",
        python_dir / "bin" / "pyinstaller",
        Path.home() / ".local" / "bin" / "pyinstaller"
    ]
    
    for location in possible_locations:
        print(f"Checking: {location}")
        if location.exists():
            print(f"✅ Found: {location}")
            return str(location)
        else:
            print(f"❌ Not found: {location}")
    
    print("❌ PyInstaller executable not found in common locations")
    return None

def test_simple_pyinstaller():
    """Test PyInstaller with a simple script"""
    print("🧪 Simple PyInstaller Test")
    print("-" * 30)
    
    # Create a simple test script
    test_script = Path("test_hello.py")
    test_script.write_text('''
print("Hello from PyInstaller test!")
input("Press Enter to exit...")
''')
    
    try:
        # Try to build with PyInstaller
        cmd = [sys.executable, "-m", "PyInstaller", "--onefile", "--console", str(test_script)]
        print(f"Running: {' '.join(cmd)}")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ Simple PyInstaller test successful!")
            # Check if executable was created
            exe_path = Path("dist") / "test_hello.exe"
            if exe_path.exists():
                print(f"✅ Executable created: {exe_path}")
            else:
                print("⚠️  Executable not found in expected location")
        else:
            print(f"❌ PyInstaller test failed:")
            print(f"STDOUT: {result.stdout}")
            print(f"STDERR: {result.stderr}")
    
    except Exception as e:
        print(f"❌ PyInstaller test error: {e}")
    
    finally:
        # Clean up
        if test_script.exists():
            test_script.unlink()
        
        # Clean up build artifacts
        import shutil
        for cleanup_dir in ["build", "dist"]:
            if Path(cleanup_dir).exists():
                shutil.rmtree(cleanup_dir)
        
        spec_file = Path("test_hello.spec")
        if spec_file.exists():
            spec_file.unlink()

def main():
    """Run all tests"""
    print("🚀 PyInstaller Diagnostic Tool")
    print("=" * 50)
    print()
    
    test_python_installation()
    test_pip()
    
    if not test_pyinstaller_installation():
        print("🔧 Attempting to install PyInstaller...")
        try:
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
            print("✅ PyInstaller installation completed")
            print()
            test_pyinstaller_installation()
        except Exception as e:
            print(f"❌ Failed to install PyInstaller: {e}")
    
    find_pyinstaller_executable()
    print()
    test_simple_pyinstaller()
    
    print()
    print("=" * 50)
    print("🏁 Diagnostic completed")
    print()
    print("If PyInstaller is working, you can now try:")
    print("  python build-binary-simple.py")

if __name__ == "__main__":
    main()
