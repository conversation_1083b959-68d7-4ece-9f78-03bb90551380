#!/bin/bash
# Build RepoSense AI Linux binary

set -e

echo "🐧 Building RepoSense AI Linux Binary"
echo "=========================================="

# Check if we're in the right directory
if [ ! -f "reposense_ai_binary.py" ]; then
    echo "❌ Error: reposense_ai_binary.py not found!"
    echo "Make sure you're running this from the project root directory."
    exit 1
fi

# Create build environment
echo "🔧 Setting up build environment..."
python3 -m venv build_env
source build_env/bin/activate

# Install dependencies
echo "📦 Installing dependencies..."
pip install --upgrade pip
pip install -r requirements.txt
pip install pyinstaller

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf build/ dist/

# Build binary
echo "🔨 Building Linux binary..."
pyinstaller --onefile \
    --name reposense-ai \
    --add-data "templates:templates" \
    --add-data "static:static" \
    --add-data "docs:docs" \
    --add-data "marketing:marketing" \
    --hidden-import=repository_backends.svn_backend \
    --hidden-import=repository_backends.git_backend \
    --hidden-import=repository_backends.base \
    --hidden-import=flask \
    --hidden-import=sqlite3 \
    --hidden-import=requests \
    --hidden-import=jinja2 \
    --collect-all=flask \
    --collect-all=jinja2 \
    --exclude-module=PyQt5 \
    --exclude-module=PySide6 \
    --exclude-module=tkinter \
    --exclude-module=matplotlib \
    --exclude-module=numpy \
    reposense_ai_binary.py

# Check if binary was created
if [ -f "dist/reposense-ai" ]; then
    echo "✅ Linux binary built successfully!"
    echo "📦 Binary location: $(pwd)/dist/reposense-ai"
    echo "📏 Binary size: $(du -h dist/reposense-ai | cut -f1)"
    
    # Make executable
    chmod +x dist/reposense-ai
    
    # Create distribution package
    echo "📦 Creating distribution package..."
    mkdir -p dist/linux-package
    cp dist/reposense-ai dist/linux-package/
    cp config.example.json dist/linux-package/
    
    # Create startup script
    cat > dist/linux-package/start-reposense-ai.sh << 'EOF'
#!/bin/bash
set -e

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Create data directory if it doesn't exist
mkdir -p data logs

# Initialize configuration if not exists
if [ ! -f data/config.json ]; then
    echo "Initializing configuration..."
    cp config.example.json data/config.json
    echo "Configuration initialized. Please customize data/config.json"
fi

# Set environment variables
export REPOSENSE_AI_CONFIG="$SCRIPT_DIR/data/config.json"
export REPOSENSE_AI_DATA_DIR="$SCRIPT_DIR/data"
export REPOSENSE_AI_LOG_DIR="$SCRIPT_DIR/logs"

# Start RepoSense AI
echo "Starting RepoSense AI..."
echo "Access the web interface at: http://localhost:5000"
echo "Press Ctrl+C to stop"

./reposense-ai
EOF
    
    chmod +x dist/linux-package/start-reposense-ai.sh
    
    echo "✅ Distribution package created: $(pwd)/dist/linux-package/"
    echo ""
    echo "To deploy:"
    echo "  1. Copy dist/linux-package/ to your Linux server"
    echo "  2. Run: ./start-reposense-ai.sh"
    echo ""
    echo "For Docker deployment, use the binary in a minimal container."
    
else
    echo "❌ Binary build failed!"
    exit 1
fi

# Cleanup
deactivate
rm -rf build_env

echo "🎉 Build completed successfully!"
