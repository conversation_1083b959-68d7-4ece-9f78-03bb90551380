# Deployment Guide

This guide covers production deployment of the RepoSense AI system using Docker, including security considerations, performance optimization, and maintenance procedures.

## Production Deployment with Docker

### Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- Minimum 2GB RAM
- 10GB available disk space
- Network access to SVN servers
- SMTP server for notifications
- Ollama server for AI features

### Quick Production Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd reposense-ai
   ```

2. **Initialize configuration**
   ```bash
   # Run setup script to create data/config.json
   ./setup.sh

   # Edit data/config.json with production settings
   # Or use environment variables for deployment-specific overrides
   ```

3. **Create data directories**
   ```bash
   mkdir -p data/logs data/output
   chmod 755 data
   ```

4. **Deploy with Docker Compose**
   ```bash
   # Single docker-compose.yml for all environments
   docker-compose up -d
   ```

5. **Configure environment variables (optional)**
   ```bash
   # Create .env file for production overrides
   cat > .env << EOF
   OLLAMA_BASE_URL=http://your-ollama-server:11434
   OLLAMA_MODEL=your-preferred-model
   REPOSENSE_AI_WEB_HOST=0.0.0.0
   REPOSENSE_AI_WEB_PORT=5000
   EOF
   ```

### Production Docker Compose

Create `docker-compose.prod.yml`:

```yaml
version: '3.8'

services:
  repository-monitor:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: repository-monitor-prod
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      - ./config.json:/app/config.json:ro
      - ./data:/app/data
      - /etc/localtime:/etc/localtime:ro
    environment:
      - PYTHONUNBUFFERED=1
      - SVN_MONITOR_ENV=production
    depends_on:
      - ollama
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  ollama:
    image: ollama/ollama:latest
    container_name: ollama-prod
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - svn-monitor
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "3"

volumes:
  ollama_data:
    driver: local

networks:
  default:
    name: svn-monitor-network
```

### Nginx Configuration

Create `nginx.conf` for reverse proxy:

```nginx
events {
    worker_connections 1024;
}

http {
    upstream svn-monitor {
        server svn-monitor:5000;
    }

    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers HIGH:!aNULL:!MD5;

        client_max_body_size 16M;

        location / {
            proxy_pass http://svn-monitor;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }

        location /health {
            proxy_pass http://svn-monitor/health;
            access_log off;
        }
    }
}
```

### Production Configuration

**Security Settings**
```json
{
  "web": {
    "host": "0.0.0.0",
    "port": 5000,
    "secret_key": "generate-strong-random-key-here",
    "debug": false,
    "max_content_length": 16777216
  },
  "logging": {
    "level": "WARNING",
    "file": "/app/data/logs/svn_monitor.log",
    "max_size_mb": 50,
    "backup_count": 10
  }
}
```

**Performance Settings**
```json
{
  "monitoring": {
    "interval_seconds": 600,
    "max_retries": 5,
    "retry_delay": 120,
    "batch_size": 5,
    "parallel_processing": true
  },
  "ollama": {
    "timeout": 60,
    "max_tokens": 4000
  }
}
```

## Security Hardening

### Container Security

1. **Run as non-root user**
   ```dockerfile
   RUN adduser --disabled-password --gecos '' appuser
   USER appuser
   ```

2. **Use security options**
   ```yaml
   security_opt:
     - no-new-privileges:true
   read_only: true
   tmpfs:
     - /tmp
   ```

3. **Limit resources**
   ```yaml
   deploy:
     resources:
       limits:
         cpus: '1.0'
         memory: 1G
       reservations:
         cpus: '0.5'
         memory: 512M
   ```

### Network Security

1. **Use custom networks**
   ```yaml
   networks:
     svn-monitor-net:
       driver: bridge
       ipam:
         config:
           - subnet: **********/16
   ```

2. **Firewall configuration**
   ```bash
   # Allow only necessary ports
   ufw allow 22/tcp    # SSH
   ufw allow 80/tcp    # HTTP
   ufw allow 443/tcp   # HTTPS
   ufw enable
   ```

### Data Protection

1. **Encrypt sensitive data**
   ```bash
   # Use encrypted volumes
   docker volume create --driver local \
     --opt type=tmpfs \
     --opt device=tmpfs \
     --opt o=size=100m,uid=1000 \
     svn-monitor-secrets
   ```

2. **Secure file permissions**
   ```bash
   chmod 600 config.json
   chmod 700 data/
   chown -R 1000:1000 data/
   ```

## Monitoring and Observability

### Health Checks

Add health check endpoint to Flask app:

```python
@app.route('/health')
def health_check():
    return {
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'version': '2.0.0'
    }
```

### Logging Configuration

**Structured logging**
```json
{
  "logging": {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "handlers": {
      "file": {
        "filename": "/app/data/logs/svn_monitor.log",
        "max_size_mb": 50,
        "backup_count": 10
      },
      "console": {
        "enabled": true,
        "level": "WARNING"
      }
    }
  }
}
```

### Metrics Collection

**Prometheus metrics** (optional):
```python
from prometheus_client import Counter, Histogram, generate_latest

# Add metrics endpoints
@app.route('/metrics')
def metrics():
    return generate_latest()
```

## Backup and Recovery

### Configuration Backup

```bash
#!/bin/bash
# backup-config.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups/repository-monitor"

mkdir -p $BACKUP_DIR
cp config.json $BACKUP_DIR/config_$DATE.json
tar -czf $BACKUP_DIR/data_$DATE.tar.gz data/

# Keep only last 30 days
find $BACKUP_DIR -name "*.json" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### Automated Backups

```yaml
# Add to docker-compose.prod.yml
  backup:
    image: alpine:latest
    volumes:
      - ./:/app
      - /backups:/backups
    command: |
      sh -c "
        apk add --no-cache tar gzip
        while true; do
          sleep 86400  # 24 hours
          /app/backup-config.sh
        done
      "
```

### Recovery Procedures

1. **Configuration recovery**
   ```bash
   # Stop services
   docker-compose down
   
   # Restore configuration
   cp /backups/repository-monitor/config_YYYYMMDD_HHMMSS.json config.json

   # Restore data
   tar -xzf /backups/repository-monitor/data_YYYYMMDD_HHMMSS.tar.gz
   
   # Restart services
   docker-compose up -d
   ```

## Performance Optimization

### Resource Allocation

**CPU and Memory**
```yaml
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 2G
    reservations:
      cpus: '1.0'
      memory: 1G
```

### Database Optimization

**Configuration tuning**
```json
{
  "monitoring": {
    "interval_seconds": 900,  # Reduce frequency for large repos
    "batch_size": 3,          # Process fewer repos simultaneously
    "parallel_processing": false  # Disable for resource-constrained systems
  }
}
```

### Caching Strategy

**File system caching**
```bash
# Mount with appropriate options
volumes:
  - type: bind
    source: ./data
    target: /app/data
    bind:
      propagation: cached
```

## Maintenance Procedures

### Regular Maintenance

**Daily tasks**
```bash
#!/bin/bash
# daily-maintenance.sh

# Check disk space
df -h /app/data

# Rotate logs
docker-compose exec svn-monitor logrotate /etc/logrotate.conf

# Check container health
docker-compose ps
```

**Weekly tasks**
```bash
#!/bin/bash
# weekly-maintenance.sh

# Update containers
docker-compose pull
docker-compose up -d

# Clean up old images
docker image prune -f

# Backup configuration
./backup-config.sh
```

### Updates and Upgrades

1. **Backup current state**
   ```bash
   ./backup-config.sh
   ```

2. **Pull latest changes**
   ```bash
   git pull origin main
   ```

3. **Update containers**
   ```bash
   docker-compose build --no-cache
   docker-compose up -d
   ```

4. **Verify functionality**
   ```bash
   curl -f http://localhost:5000/health
   ```

## Troubleshooting

### Common Issues

**Container won't start**
```bash
# Check logs
docker-compose logs svn-monitor

# Check configuration
docker-compose config

# Verify file permissions
ls -la config.json data/
```

**High resource usage**
```bash
# Monitor resource usage
docker stats

# Check application logs
tail -f data/logs/svn_monitor.log

# Adjust monitoring intervals
```

**Network connectivity issues**
```bash
# Test SVN connectivity
docker-compose exec svn-monitor svn info https://your-repo-url

# Test email connectivity
docker-compose exec repository-monitor telnet smtp.gmail.com 587
```

### Emergency Procedures

**Service recovery**
```bash
# Quick restart
docker-compose restart repository-monitor

# Full rebuild
docker-compose down
docker-compose build --no-cache
docker-compose up -d
```

**Data corruption recovery**
```bash
# Stop services
docker-compose down

# Restore from backup
tar -xzf /backups/repository-monitor/data_latest.tar.gz

# Restart with clean state
docker-compose up -d
```

## Security Considerations

- The application runs as non-root user (UID 1000)
- Sensitive data (passwords) should be stored in environment files
- Consider using Docker secrets for production deployments
- Repository credentials are stored in the configuration file
- Use HTTPS in production with proper SSL certificates
- Implement proper firewall rules to restrict access
- Regular security updates for base images and dependencies

## Performance Tuning

### Ollama Performance
- **GPU Support**: Add GPU support to docker-compose.yml if available
- **Memory**: Increase Docker memory limits for larger models
- **Models**: Use smaller models (like `llama2:7b`) for faster responses
- **Concurrent Requests**: Configure Ollama for multiple concurrent requests

### Application Performance
- **Check Interval**: Adjust `check_interval` based on your needs (default: 300 seconds)
- **Batch Processing**: The app processes commits sequentially for consistency
- **Database Optimization**: Consider external database for large-scale deployments
- **Caching**: Implement Redis caching for frequently accessed data

### Development Environment Performance Optimizations

The development environment includes several performance optimizations:

1. **Ollama Connection Caching**: Connection status is cached for 30 seconds to avoid slow dashboard loads
2. **Ollama Models Caching**: Available models are cached for 5 minutes to reduce API calls
3. **Reduced Timeouts**: Connection tests use 2-second timeouts instead of 5 seconds
4. **Performance Monitoring**: Load times are tracked and logged for debugging
5. **API Endpoints**: Performance metrics available at `/api/performance`

#### Performance Metrics
- Dashboard loads in ~50ms (vs 4+ seconds before optimization)
- API status calls load in ~0.01ms when cached
- Ollama connection tests complete in <2 seconds
- Models API loads in ~50ms initially, then cached for 5 minutes

#### Performance Tips
1. **Selective Editing**: Only edit files you need to change
2. **Log Management**: Use `--tail` to limit log output
3. **Browser Cache**: Disable cache during development (F12 → Network → Disable cache)
4. **Container Resources**: Monitor Docker Desktop resource usage

## Production Deployment Best Practices

For production use:

1. **Use external databases** for configuration storage and better scalability
2. **Implement proper logging** aggregation (ELK stack, Fluentd, etc.)
3. **Set up monitoring** with health checks and alerting (Prometheus, Grafana)
4. **Use secrets management** for credentials (Docker secrets, Kubernetes secrets)
5. **Configure resource limits** in docker-compose.yml to prevent resource exhaustion
6. **Set up automated backups** of the data volume and configuration
7. **Implement load balancing** for high availability deployments
8. **Use container orchestration** (Kubernetes, Docker Swarm) for scaling
9. **Set up SSL/TLS termination** with proper certificates
10. **Implement proper network segmentation** and security policies

### High Availability Setup Example

```yaml
services:
  repository-monitor:
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
```

## Conclusion

This deployment guide provides a comprehensive foundation for running RepoSense AI in production. Adapt the configurations based on your specific requirements and infrastructure constraints.

For additional support and advanced configurations, refer to the other documentation files in this repository.
