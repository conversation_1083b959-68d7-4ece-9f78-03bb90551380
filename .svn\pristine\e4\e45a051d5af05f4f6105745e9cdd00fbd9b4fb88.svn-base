# SVN Monitor Documentation

Welcome to the SVN Monitor documentation. This system provides automated monitoring of SVN repositories with AI-powered documentation generation and intelligent email notifications.

## Table of Contents

1. [System Overview](#system-overview)
2. [Architecture & Design](design.md)
3. [Configuration Guide](configuration.md)
4. [User Guide](usage.md)
5. [API Reference](api.md)
6. [Deployment Guide](deployment.md)
7. [Development](development.md)

## System Overview

SVN Monitor is a comprehensive solution for monitoring Subversion repositories, automatically generating documentation for changes, and notifying relevant stakeholders. The system combines traditional SVN monitoring with modern AI capabilities to provide intelligent insights into code changes.

### Key Features

- **Multi-Repository Monitoring**: Monitor multiple SVN repositories simultaneously
- **User Management**: Role-based user system with flexible notification preferences
- **Repository Discovery**: Automatically discover repositories from SVN servers
- **AI-Powered Documentation**: Generate intelligent documentation using Ollama AI models
- **Smart Notifications**: Context-aware email notifications with user targeting
- **Modern Web Interface**: Responsive web UI for configuration and monitoring
- **Docker Support**: Containerized deployment with Docker Compose
- **Flexible Configuration**: JSON-based configuration with web interface

### Architecture Highlights

- **Modular Design**: Clean separation of concerns with dedicated services
- **Service-Oriented**: Individual services for monitoring, user management, email, etc.
- **Event-Driven**: Background monitoring with configurable intervals
- **Scalable**: Designed to handle multiple repositories and users efficiently
- **Extensible**: Plugin-friendly architecture for future enhancements

## Quick Start

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd svn-checkin-monitor
   ```

2. **Configure Environment**
   ```bash
   cp config.example.json config.json
   # Edit config.json with your settings
   ```

3. **Deploy with Docker**
   ```bash
   docker-compose up -d
   ```

4. **Access Web Interface**
   - Open http://localhost:5000
   - Configure repositories and users
   - Start monitoring

## System Requirements

- **Docker & Docker Compose** (recommended)
- **Python 3.8+** (for local development)
- **SVN Client** (svn command-line tools)
- **Ollama Server** (for AI documentation generation)
- **SMTP Server** (for email notifications)

## Core Components

### Services
- **Monitor Service**: Core SVN monitoring and change detection
- **User Management Service**: User creation, roles, and repository associations
- **Repository Discovery Service**: Automatic repository discovery from SVN servers
- **Email Service**: Intelligent email notifications with user targeting
- **Ollama Service**: AI-powered documentation generation
- **File Manager**: Document and email storage with repository organization
- **Web Interface**: Flask-based configuration and monitoring interface

### Data Models
- **User**: Role-based user accounts with notification preferences
- **Repository**: SVN repository configurations with user associations
- **Config**: Central configuration management with persistence

### Web Interface
- **Dashboard**: System status and monitoring overview
- **Repository Management**: Add, configure, and monitor repositories
- **User Management**: Create and manage users with role-based access
- **Repository Discovery**: Scan SVN servers for available repositories
- **Configuration**: System-wide settings and preferences
- **Logs**: Monitoring activity and system logs

## Getting Help

- **Configuration Issues**: See [Configuration Guide](configuration.md)
- **Usage Questions**: Check [User Guide](usage.md)
- **Development**: Review [Development Guide](development.md)
- **API Integration**: Consult [API Reference](api.md)

## Documentation Structure

```
docs/
├── index.md              # This overview document
├── design.md             # System architecture and design
├── configuration.md      # Configuration guide and examples
├── usage.md              # User guide and workflows
├── api.md                # API reference and endpoints
├── deployment.md         # Deployment and infrastructure
├── development.md        # Development and contribution guide
├── README.md             # Original README (moved from root)
└── REFACTORING_SUMMARY.md # Development history
```

## Version Information

- **Current Version**: 2.0.0
- **Architecture**: Modular service-oriented design
- **Python Version**: 3.8+
- **Docker Support**: Yes
- **Web Interface**: Modern responsive design
- **AI Integration**: Ollama-powered documentation

## License

This project is licensed under the MIT License. See the LICENSE file for details.

## Contributing

We welcome contributions! Please see the [Development Guide](development.md) for information on how to contribute to this project.
