# SVN Monitor - Docker Setup

This document explains how to run the SVN Monitor application using Docker.

## Quick Start

1. **Clone the repository and navigate to the directory:**
   ```bash
   git clone <repository-url>
   cd svn-checkin-monitor
   ```

2. **Run the setup script:**
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

3. **Access the web interface:**
   Open http://localhost:5000 in your browser

## Manual Setup

If you prefer to set up manually:

### Prerequisites

- Docker
- Docker Compose

### 1. Create Data Directory

```bash
mkdir -p data
```

### 2. Start Services

```bash
docker compose -f docker_compose.yml up -d --build
```

### 3. Configure Repositories

1. Open http://localhost:5000
2. Navigate to "Repositories"
3. Add your SVN repositories
4. Configure monitoring settings

## Architecture

The Docker setup includes two services:

### SVN Monitor Service
- **Port:** 5000 (web interface)
- **Volumes:** 
  - `./data:/app/data` - Persistent storage for configs, logs, and outputs
  - `./config:/app/config:ro` - Optional external config directory
- **Features:**
  - Multi-repository monitoring
  - Web-based configuration
  - Automatic documentation generation
  - Email notifications

### Ollama Service
- **Port:** 11434 (API)
- **Volume:** `ollama_data:/root/.ollama` - Model storage
- **Purpose:** Provides AI capabilities for documentation generation

## Configuration

### Configuration Options

#### Getting Started with Configuration

1. **Use the example files:**
   ```bash
   # For detailed configuration with comments
   cp config.example.json data/config.json

   # For minimal clean configuration
   cp config.minimal.json data/config.json
   ```

2. **Configure through web interface:** Visit http://localhost:5000/config

#### Repository Configuration

Repositories can be configured through:
- **Web Interface:** http://localhost:5000/repositories (recommended)
- **Direct JSON editing:** Edit `repositories` array in `data/config.json`

Each repository supports:
- **Name:** Display name for the repository
- **URL:** SVN repository URL (https:// or svn:// protocols)
- **Username/Password:** Authentication credentials (optional for anonymous access)
- **Enabled:** Whether to actively monitor this repository

#### Application Configuration

Main settings available at http://localhost:5000/config:

- **Ollama Settings:** AI model and host configuration
- **Email Settings:** SMTP configuration for notifications
- **Monitoring Settings:** Check intervals and feature toggles
- **Output Settings:** Where to store generated documentation

### Configuration Files

- **config.example.json** - Detailed example with all options and comments
- **config.minimal.json** - Clean minimal configuration template
- **data/config.json** - Your actual configuration (created by setup script)

### File Structure

```
./
├── config.example.json      # Detailed configuration example
├── config.minimal.json      # Minimal configuration template
└── data/
    ├── config.json          # Main configuration file
    ├── svn_monitor.log      # Application logs
    └── repositories/        # Repository-specific outputs
        ├── <repo-id>/
        │   ├── docs/       # Generated documentation
        │   └── emails/     # Email copies
        └── ...
```

## Environment Variables

You can override configuration using environment variables in `docker-compose.yml`:

```yaml
environment:
  - TZ=UTC                    # Timezone
  - OLLAMA_HOST=http://ollama:11434  # Ollama service URL
```

## Monitoring Multiple Repositories

The application supports monitoring multiple SVN repositories simultaneously:

1. Each repository is monitored independently
2. Outputs are organized by repository
3. Different authentication can be used per repository
4. Repositories can be enabled/disabled individually

## Troubleshooting

### Check Service Status

```bash
docker-compose ps
```

### View Logs

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f svn-monitor
docker-compose logs -f ollama
```

### Restart Services

```bash
docker-compose restart
```

### Reset Everything

```bash
docker-compose down -v
docker-compose up -d --build
```

### Common Issues

1. **Ollama model not downloading:**
   - Check Ollama logs: `docker-compose logs ollama`
   - Ensure internet connectivity
   - Wait for initial model download (can take 10+ minutes)

2. **SVN authentication issues:**
   - Verify credentials in repository configuration
   - Check SVN server accessibility from container
   - Review application logs for specific errors

3. **Web interface not accessible:**
   - Ensure port 5000 is not in use by another application
   - Check firewall settings
   - Verify container is running: `docker-compose ps`

## Security Considerations

- Change the default `web_secret_key` in production
- Use HTTPS reverse proxy for production deployments
- Store sensitive credentials securely
- Regularly update Docker images

## Backup and Restore

### Backup

```bash
# Backup data directory
tar -czf svn-monitor-backup.tar.gz data/

# Backup Docker volumes
docker run --rm -v svn-monitor_ollama_data:/data -v $(pwd):/backup alpine tar czf /backup/ollama-backup.tar.gz -C /data .
```

### Restore

```bash
# Restore data directory
tar -xzf svn-monitor-backup.tar.gz

# Restore Docker volumes
docker run --rm -v svn-monitor_ollama_data:/data -v $(pwd):/backup alpine tar xzf /backup/ollama-backup.tar.gz -C /data
```
