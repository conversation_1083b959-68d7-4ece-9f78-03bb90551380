#!/bin/bash
# Deploy Repository Monitor to Docker Hub

set -e

echo "🐳 Repository Monitor - Docker Hub Deployment"
echo "============================================"

# Configuration
DOCKER_USERNAME="${DOCKER_USERNAME:-your-dockerhub-username}"
IMAGE_NAME="repository-monitor"
VERSION="${VERSION:-latest}"
FULL_IMAGE_NAME="${DOCKER_USERNAME}/${IMAGE_NAME}:${VERSION}"

# Check if Docker is installed and running
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker daemon is not running"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "repository_monitor_binary.py" ] && [ ! -f "Dockerfile.linux-binary" ]; then
    echo "❌ Error: Repository Monitor source files not found!"
    echo "Make sure you're running this from the Repository Monitor directory."
    exit 1
fi

echo "📋 Build Configuration:"
echo "   Docker Username: ${DOCKER_USERNAME}"
echo "   Image Name: ${IMAGE_NAME}"
echo "   Version: ${VERSION}"
echo "   Full Image: ${FULL_IMAGE_NAME}"
echo ""

# Login to Docker Hub
echo "🔐 Logging into Docker Hub..."
echo "Please enter your Docker Hub credentials:"
docker login

# Build multi-architecture images (AMD64 and ARM64)
echo "🏗️ Building multi-architecture Docker images..."

# Create and use buildx builder
docker buildx create --name repository-monitor-builder --use 2>/dev/null || docker buildx use repository-monitor-builder

# Build and push multi-architecture image
docker buildx build \
    --platform linux/amd64,linux/arm64 \
    --file Dockerfile.linux-binary \
    --tag ${FULL_IMAGE_NAME} \
    --tag ${DOCKER_USERNAME}/${IMAGE_NAME}:v2.1.0 \
    --push \
    .

echo "✅ Multi-architecture image built and pushed successfully!"

# Also build latest tag if not already latest
if [ "${VERSION}" != "latest" ]; then
    docker buildx build \
        --platform linux/amd64,linux/arm64 \
        --file Dockerfile.linux-binary \
        --tag ${DOCKER_USERNAME}/${IMAGE_NAME}:latest \
        --push \
        .
    echo "✅ Latest tag also pushed!"
fi

# Create and push a lightweight version (source-based for comparison)
echo "🪶 Building lightweight source version..."
docker buildx build \
    --platform linux/amd64,linux/arm64 \
    --file Dockerfile.production \
    --tag ${DOCKER_USERNAME}/${IMAGE_NAME}:source \
    --tag ${DOCKER_USERNAME}/${IMAGE_NAME}:v2.1.0-source \
    --push \
    .

echo "✅ Source version also pushed!"

# Verify the images
echo "🔍 Verifying pushed images..."
docker buildx imagetools inspect ${FULL_IMAGE_NAME}

echo ""
echo "🎉 Deployment to Docker Hub completed successfully!"
echo ""
echo "📦 Available Images:"
echo "   ${DOCKER_USERNAME}/${IMAGE_NAME}:latest (binary)"
echo "   ${DOCKER_USERNAME}/${IMAGE_NAME}:v2.1.0 (binary)"
echo "   ${DOCKER_USERNAME}/${IMAGE_NAME}:source (source-based)"
echo "   ${DOCKER_USERNAME}/${IMAGE_NAME}:v2.1.0-source (source-based)"
echo ""
echo "🚀 Usage Examples:"
echo "   docker run -d -p 5000:5000 ${DOCKER_USERNAME}/${IMAGE_NAME}:latest"
echo "   docker-compose:"
echo "     image: ${DOCKER_USERNAME}/${IMAGE_NAME}:latest"
echo ""
echo "📖 Docker Hub: https://hub.docker.com/r/${DOCKER_USERNAME}/${IMAGE_NAME}"
