#!/usr/bin/env python3
"""
SVN repository backend plugin
Handles all interactions with SVN repositories
"""

import logging
import subprocess
import xml.etree.ElementTree as ET
from typing import List, Optional, Dict, Any, Iterator
from urllib.parse import urljoin
from datetime import datetime
import requests
import re

from .base import RepositoryBackend, RepositoryInfo
from models import CommitInfo, RepositoryConfig


class SVNBackend(RepositoryBackend):
    """SVN repository backend implementation"""
    
    def __init__(self, config):
        super().__init__(config)
        self.logger = logging.getLogger(__name__)
    
    @property
    def backend_type(self) -> str:
        return 'svn'
    
    def get_command_base(self, repo: RepositoryConfig) -> List[str]:
        """Get base SVN command with authentication for a specific repository"""
        cmd = ["svn"]
        if repo.username:
            cmd.extend(["--username", repo.username])
        if repo.password:
            cmd.extend(["--password", repo.password])
        # Comprehensive SSL certificate handling
        cmd.extend([
            "--non-interactive",
            "--trust-server-cert",
            "--trust-server-cert-failures=unknown-ca,cn-mismatch,expired,not-yet-valid,other"
        ])
        return cmd
    
    def get_latest_revision(self, repo: RepositoryConfig) -> Optional[str]:
        """Get the latest revision number from SVN repository"""
        try:
            cmd = self.get_command_base(repo)
            cmd.extend(["info", repo.url, "--xml"])

            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            root = ET.fromstring(result.stdout)
            commit_elem = root.find(".//commit")
            if commit_elem is not None:
                revision_str = commit_elem.get("revision")
                if revision_str:
                    self.logger.info(f"Latest revision for {repo.name}: {revision_str}")
                    return revision_str

            self.logger.error(f"No commit information found for {repo.name}")
            return None

        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error getting latest revision for {repo.name}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error getting revision for {repo.name}: {e}")
            return None
    
    def get_commit_info(self, repo: RepositoryConfig, revision: str) -> Optional[CommitInfo]:
        """Get detailed information about a specific commit"""
        try:
            # Get commit log
            cmd = self.get_command_base(repo)
            cmd.extend(["log", repo.url, "-r", revision, "--xml", "-v"])

            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            root = ET.fromstring(result.stdout)

            entry = root.find("logentry")
            if entry is None:
                return None

            # Extract commit information
            revision_attr = entry.get("revision", revision)
            author = entry.findtext("author", "Unknown")
            date = entry.findtext("date", "")
            message = entry.findtext("msg", "")

            # Extract changed paths
            changed_paths = []
            paths_elem = entry.find("paths")
            if paths_elem is not None:
                for path_elem in paths_elem.findall("path"):
                    if path_elem.text:
                        changed_paths.append(path_elem.text)

            # Get diff
            diff = self.get_diff(repo, revision) or ""

            return CommitInfo(
                revision=revision_attr,
                author=author,
                date=date,
                message=message,
                changed_paths=changed_paths,
                diff=diff,
                repository_id=repo.id,
                repository_name=repo.name
            )

        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error getting commit info for {repo.name} r{revision}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error getting commit info for {repo.name} r{revision}: {e}")
            return None
    
    def get_diff(self, repo: RepositoryConfig, revision: str) -> Optional[str]:
        """Get the diff for a specific revision"""
        try:
            cmd = self.get_command_base(repo)
            cmd.extend(["diff", repo.url, "-c", revision])

            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return result.stdout

        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error getting diff for {repo.name} r{revision}: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error getting diff for {repo.name} r{revision}: {e}")
            return None
    
    def test_connection(self, repo: RepositoryConfig) -> bool:
        """Test if the repository is accessible"""
        try:
            cmd = self.get_command_base(repo)
            cmd.extend(["info", repo.url])
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            return result.returncode == 0
            
        except Exception as e:
            self.logger.error(f"Error testing connection to {repo.name}: {e}")
            return False
    
    def discover_repositories(self, base_url: str, username: Optional[str] = None, 
                            password: Optional[str] = None, max_depth: int = 3) -> List[RepositoryInfo]:
        """Discover repositories from an SVN server"""
        discovered_repos = []
        
        try:
            self.logger.info(f"Starting repository discovery from {base_url} with max depth {max_depth}")
            discovered_repos = self._discover_recursive(base_url, username, password, 0, max_depth)
            self.logger.info(f"Discovered {len(discovered_repos)} repositories")

        except Exception as e:
            self.logger.error(f"Error during repository discovery: {e}")
            self.logger.error(f"Exception details: {str(e)}")
            
        return discovered_repos
    
    def _discover_recursive(self, url: str, username: Optional[str], password: Optional[str],
                          current_depth: int, max_depth: int) -> List[RepositoryInfo]:
        """Recursively discover repositories"""
        repositories: list[RepositoryInfo] = []
        
        if current_depth >= max_depth:
            return repositories
            
        try:
            self.logger.debug(f"Checking directory: {url} (depth {current_depth})")

            # Special handling for different SVN server types at root level
            if current_depth == 0:
                server_type = self._detect_server_type(url, username, password)
                self.logger.debug(f"Detected server type: {server_type} at {url}")

                # If HTTPS URL had SSL issues during detection, try HTTP first
                if url.startswith('https://') and server_type == 'standard':
                    http_url = url.replace('https://', 'http://')
                    try:
                        # Test if HTTP works better
                        http_server_type = self._detect_server_type(http_url, username, password)
                        if http_server_type != 'standard':
                            self.logger.debug(f"HTTP version works better, switching to {http_url}")
                            url = http_url
                            server_type = http_server_type
                    except Exception:
                        pass

                if server_type in ['visualsvn', 'apache_dav']:
                    try:
                        return self._discover_xml_repositories(url, username, password, current_depth, max_depth)
                    except Exception as e:
                        if "HTTP/DAV protocol" in str(e):
                            self.logger.warning(f"Server detected as {server_type} but doesn't support HTTP/DAV. Falling back to standard discovery.")
                        else:
                            self.logger.warning(f"XML repository discovery failed: {e}. Falling back to standard discovery.")
                elif server_type == 'standard':
                    # Continue with standard recursive discovery
                    pass

            try:
                entries = self._get_svn_list(url, username, password)
                self.logger.debug(f"Found {len(entries)} entries in {url}")
            except Exception as e:
                # If directory listing fails, check if the URL itself is a repository
                if current_depth == 0 and ("HTTP/DAV protocol" in str(e) or "might be a repository root" in str(e)):
                    self.logger.debug(f"Directory listing failed for {url}, checking if it's a repository root")
                    if self._test_svn_info(url, username, password):
                        self.logger.info(f"Base URL {url} is itself a repository")
                        repo_info = self._create_repository_info(url, username, password)
                        if repo_info:
                            repositories.append(repo_info)
                        return repositories
                # Re-raise the exception if it's not a repository root
                raise

            for entry in entries:
                entry_url = urljoin(url.rstrip('/') + '/', entry['name'])
                
                if entry['kind'] == 'dir':
                    # Check if this directory is a repository root
                    if self._is_repository_root(entry_url, username, password):
                        repo_info = RepositoryInfo(
                            name=entry['name'],
                            url=entry_url,
                            path=entry_url.replace(url.rstrip('/'), '').lstrip('/'),
                            last_revision=str(entry.get('commit_revision', 0)),
                            last_author=entry.get('commit_author', ''),
                            last_date=entry.get('commit_date', ''),
                            size=entry.get('size', 0),
                            repository_type='svn'
                        )
                        repositories.append(repo_info)
                        self.logger.debug(f"Found repository: {entry_url}")
                    else:
                        # Recurse into subdirectories
                        sub_repos = self._discover_recursive(entry_url, username, password, 
                                                           current_depth + 1, max_depth)
                        repositories.extend(sub_repos)
                        
        except Exception as e:
            self.logger.warning(f"Could not list directory {url}: {e}")
            
        return repositories

    def _test_svn_info(self, url: str, username: Optional[str], password: Optional[str]) -> bool:
        """Test if a URL is a valid SVN repository by running svn info with protocol fallback"""
        urls_to_try = [url]

        # Add protocol fallback URLs
        if url.startswith('https://'):
            urls_to_try.append(url.replace('https://', 'http://'))
        elif url.startswith('http://'):
            urls_to_try.append(url.replace('http://', 'https://'))

        for try_url in urls_to_try:
            try:
                cmd = ['svn', 'info', '--xml', try_url]

                if username:
                    cmd.extend(['--username', username])
                if password:
                    cmd.extend(['--password', password])

                cmd.extend([
                    '--non-interactive',
                    '--trust-server-cert',
                    '--trust-server-cert-failures=unknown-ca,cn-mismatch,expired,not-yet-valid,other'
                ])

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=15)
                if result.returncode == 0:
                    self.logger.debug(f"SVN info test succeeded with URL: {try_url}")
                    return True
                else:
                    self.logger.debug(f"SVN info test failed for URL: {try_url} - {result.stderr}")

            except Exception as e:
                self.logger.debug(f"SVN info test error for URL: {try_url} - {e}")
                continue

        return False

    def _detect_server_type(self, url: str, username: Optional[str], password: Optional[str]) -> str:
        """Detect the type of SVN server for optimal discovery strategy"""
        try:
            import requests

            # Check if config specifies a server type
            if hasattr(self, 'config') and self.config:
                server_type = getattr(self.config, 'svn_server_type', 'auto')
                if server_type != 'auto':
                    self.logger.debug(f"Using configured server type: {server_type}")
                    return server_type

            # Auto-detect server type
            auth = (username, password) if username and password else None

            # First try with SSL verification disabled for self-signed certificates
            try:
                response = requests.head(url, auth=auth, timeout=10, verify=False)
                server_header = response.headers.get('Server', '').lower()
                self.logger.debug(f"Server detection successful with SSL verification disabled: {server_header}")

                if 'visualsvn' in server_header:
                    return 'visualsvn'
                elif 'apache' in server_header:
                    # Try to determine if it's Apache with DAV/SVN
                    try:
                        get_response = requests.get(url, auth=auth, timeout=10, verify=False)
                        if '<dir name=' in get_response.text:
                            return 'apache_dav'
                    except Exception:
                        pass
                    return 'apache'
                else:
                    return 'standard'
            except requests.exceptions.SSLError as ssl_error:
                self.logger.debug(f"SSL error even with verification disabled for {url}: {ssl_error}")
                # If HTTPS still fails due to SSL, try HTTP version
                if url.startswith('https://'):
                    http_url = url.replace('https://', 'http://')
                    try:
                        response = requests.head(http_url, auth=auth, timeout=10)
                        server_header = response.headers.get('Server', '').lower()
                        self.logger.debug(f"Server detection successful via HTTP fallback: {server_header}")

                        if 'visualsvn' in server_header:
                            return 'visualsvn'
                        elif 'apache' in server_header:
                            return 'apache'
                        else:
                            return 'standard'
                    except Exception as http_error:
                        self.logger.debug(f"HTTP fallback also failed: {http_error}")
                return 'standard'
            except requests.exceptions.RequestException as e:
                self.logger.debug(f"HTTP request failed during server detection: {e}")
                # If HTTP fails, the server might only support svn:// protocol
                return 'standard'

        except Exception as e:
            self.logger.debug(f"Error detecting server type: {e}")
            return 'standard'

    def _discover_xml_repositories(self, base_url: str, username: Optional[str], password: Optional[str],
                                 current_depth: int = 0, max_depth: int = 3) -> List[RepositoryInfo]:
        """Discover repositories by parsing XML directory listing (VisualSVN, Apache DAV, etc.)"""
        repositories: list[RepositoryInfo] = []

        # Respect depth limits
        if current_depth >= max_depth:
            self.logger.debug(f"Reached maximum depth {max_depth} at {base_url}")
            return repositories

        self.logger.debug(f"XML repository discovery at {base_url} (depth {current_depth}/{max_depth})")

        # First, check if the base URL itself is a repository
        if current_depth == 0 and self._test_svn_info(base_url, username, password):
            self.logger.info(f"Base URL {base_url} is itself a repository, not a directory containing repositories")
            repo_info = self._create_repository_info(base_url, username, password)
            if repo_info:
                repositories.append(repo_info)
            return repositories

        try:
            import requests
            import xml.etree.ElementTree as ET

            # Get the XML directory listing (with SSL verification disabled for self-signed certificates)
            auth = (username, password) if username and password else None
            response = requests.get(base_url, auth=auth, timeout=30, verify=False)

            if response.status_code != 200:
                self.logger.warning(f"Could not access SVN server at {base_url}: HTTP {response.status_code}")
                return repositories

            self.logger.debug(f"XML content length: {len(response.text)}")
            self.logger.debug(f"XML content preview: {response.text[:200]}...")

            # Parse the XML content to find directory entries
            try:
                # The content is SVN XML format with <dir> elements
                # Need to handle the XML properly - it might already be well-formed
                xml_content = response.text.strip()
                if not xml_content.startswith('<?xml') and not xml_content.startswith('<'):
                    # If it doesn't start with XML declaration or tag, wrap it
                    xml_content = f"<root>{xml_content}</root>"

                root = ET.fromstring(xml_content)

                # Debug: Log the XML structure we're working with
                self.logger.debug(f"XML root tag: {root.tag}")
                self.logger.debug(f"XML root attributes: {root.attrib}")
                all_elements = [elem.tag for elem in root.iter()]
                self.logger.debug(f"All XML elements found: {set(all_elements)}")

                # Look for directory elements - try different possible formats
                dir_elements = root.findall('.//dir')  # Standard format
                if not dir_elements:
                    # Try alternative formats that Apache DAV might use
                    dir_elements = root.findall('.//entry[@kind="dir"]')  # SVN XML format
                if not dir_elements:
                    dir_elements = root.findall('.//item[@type="dir"]')  # Alternative format

                self.logger.debug(f"Found {len(dir_elements)} directory entries in XML")

                for dir_elem in dir_elements:
                    repo_name = dir_elem.get('name')
                    if repo_name:
                        repo_url = f"{base_url.rstrip('/')}/{repo_name}"
                        self.logger.debug(f"Testing potential repository: {repo_url} (depth {current_depth})")

                        # Test if this is actually a valid SVN repository
                        if self._test_svn_info(repo_url, username, password):
                            self.logger.debug(f"Found SVN repository root: {repo_url}")

                            # Check for standard SVN branch structure (trunk, branches, tags)
                            branch_repos = self._discover_svn_branches(repo_url, repo_name, username, password)
                            if branch_repos:
                                repositories.extend(branch_repos)
                                self.logger.debug(f"Found {len(branch_repos)} branches in {repo_url}")
                            else:
                                # If no standard branches found, add the repository root itself
                                repo_info = RepositoryInfo(
                                    name=repo_name,
                                    url=repo_url,
                                    path=repo_name,
                                    last_revision="0",
                                    last_author="",
                                    last_date="",
                                    size=0,
                                    repository_type='svn'
                                )
                                repositories.append(repo_info)
                        else:
                            self.logger.debug(f"Directory {repo_url} is not a valid SVN repository")

                            # If not a repository and we haven't reached max depth, recurse into subdirectory
                            if current_depth + 1 < max_depth:
                                self.logger.info(f"Recursing into subdirectory {repo_url} at depth {current_depth + 1}/{max_depth}")
                                try:
                                    sub_repos = self._discover_xml_repositories(repo_url, username, password,
                                                                              current_depth + 1, max_depth)
                                    repositories.extend(sub_repos)
                                    self.logger.debug(f"Found {len(sub_repos)} repositories in subdirectory {repo_url}")
                                except Exception as e:
                                    self.logger.debug(f"Failed to recurse into {repo_url}: {e}")
                            else:
                                self.logger.info(f"DEPTH LIMIT REACHED: Skipping recursion into {repo_url} - max depth {max_depth} reached at current depth {current_depth}")

                # If we didn't find any directories at depth 0, the XML format might not support directory listing
                # Fall back to standard SVN list approach
                if current_depth == 0 and len(repositories) == 0 and len(dir_elements) == 0:
                    self.logger.debug("No directories found in XML format, falling back to standard SVN list approach")
                    try:
                        entries = self._get_svn_list(base_url, username, password)
                        for entry in entries:
                            if entry['kind'] == 'dir':
                                entry_url = f"{base_url.rstrip('/')}/{entry['name']}"
                                if self._test_svn_info(entry_url, username, password):
                                    # Check for SVN branch structure
                                    branch_repos = self._discover_svn_branches(entry_url, entry['name'], username, password)
                                    if branch_repos:
                                        repositories.extend(branch_repos)
                                        self.logger.debug(f"Fallback: Found {len(branch_repos)} branches in {entry_url}")
                                    else:
                                        # If no branches found, add the repository root
                                        repo_info = self._create_repository_info(entry_url, username, password)
                                        if repo_info:
                                            repositories.append(repo_info)
                                elif current_depth + 1 < max_depth:
                                    # Recurse into subdirectory
                                    self.logger.info(f"Fallback: Recursing into subdirectory {entry_url} at depth {current_depth + 1}/{max_depth}")
                                    sub_repos = self._discover_xml_repositories(entry_url, username, password,
                                                                              current_depth + 1, max_depth)
                                    repositories.extend(sub_repos)
                                else:
                                    self.logger.info(f"Fallback: DEPTH LIMIT REACHED - Skipping {entry_url} at max depth {max_depth}")
                    except Exception as fallback_error:
                        self.logger.debug(f"Fallback to SVN list also failed: {fallback_error}")

            except ET.ParseError as e:
                self.logger.error(f"Error parsing XML from SVN server: {e}")

        except ImportError:
            self.logger.error("requests library is required for XML-based repository discovery")
        except Exception as e:
            self.logger.error(f"Error discovering XML-listed repositories: {e}")

        return repositories

    def _discover_svn_branches(self, repo_url: str, repo_name: str, username: Optional[str], password: Optional[str]) -> List[RepositoryInfo]:
        """Discover SVN branches (trunk, branches/*, tags/*) within a repository"""
        branches = []

        try:
            # Get the directory listing of the repository root
            entries = self._get_svn_list(repo_url, username, password)
            entry_names = [entry['name'] for entry in entries if entry['kind'] == 'dir']

            self.logger.debug(f"Repository {repo_url} contains directories: {entry_names}")

            # Check for trunk
            if 'trunk' in entry_names:
                trunk_url = f"{repo_url}/trunk"
                if self._test_svn_info(trunk_url, username, password):
                    trunk_info = RepositoryInfo(
                        name=f"{repo_name}/trunk",
                        url=trunk_url,
                        path=f"{repo_name}/trunk",
                        last_revision="0",
                        last_author="",
                        last_date="",
                        size=0,
                        repository_type='svn'
                    )
                    branches.append(trunk_info)
                    self.logger.debug(f"Found trunk: {trunk_url}")

            # Check for branches directory
            if 'branches' in entry_names:
                branches_url = f"{repo_url}/branches"
                try:
                    branch_entries = self._get_svn_list(branches_url, username, password)
                    for branch_entry in branch_entries:
                        if branch_entry['kind'] == 'dir':
                            branch_name = branch_entry['name']
                            branch_url = f"{branches_url}/{branch_name}"
                            if self._test_svn_info(branch_url, username, password):
                                branch_info = RepositoryInfo(
                                    name=f"{repo_name}/branches/{branch_name}",
                                    url=branch_url,
                                    path=f"{repo_name}/branches/{branch_name}",
                                    last_revision="0",
                                    last_author="",
                                    last_date="",
                                    size=0,
                                    repository_type='svn'
                                )
                                branches.append(branch_info)
                                self.logger.debug(f"Found branch: {branch_url}")
                except Exception as e:
                    self.logger.debug(f"Could not list branches in {branches_url}: {e}")

            # Check for tags directory (optional, usually not monitored actively)
            if 'tags' in entry_names:
                tags_url = f"{repo_url}/tags"
                try:
                    tag_entries = self._get_svn_list(tags_url, username, password)
                    # Only add a few recent tags, not all (tags can be numerous)
                    recent_tags = tag_entries[:5] if len(tag_entries) > 5 else tag_entries
                    for tag_entry in recent_tags:
                        if tag_entry['kind'] == 'dir':
                            tag_name = tag_entry['name']
                            tag_url = f"{tags_url}/{tag_name}"
                            if self._test_svn_info(tag_url, username, password):
                                tag_info = RepositoryInfo(
                                    name=f"{repo_name}/tags/{tag_name}",
                                    url=tag_url,
                                    path=f"{repo_name}/tags/{tag_name}",
                                    last_revision="0",
                                    last_author="",
                                    last_date="",
                                    size=0,
                                    repository_type='svn'
                                )
                                branches.append(tag_info)
                                self.logger.debug(f"Found tag: {tag_url}")
                except Exception as e:
                    self.logger.debug(f"Could not list tags in {tags_url}: {e}")

        except Exception as e:
            self.logger.debug(f"Error discovering branches in {repo_url}: {e}")

        return branches

    def _get_svn_list(self, url: str, username: Optional[str], password: Optional[str]) -> List[Dict]:
        """Get SVN directory listing with protocol fallback"""
        urls_to_try = [url]

        # Add protocol fallback URLs with intelligent ordering
        if url.startswith('https://'):
            # Try HTTP first if HTTPS might have SSL issues
            http_url = url.replace('https://', 'http://')
            urls_to_try.append(http_url)
            # Also try svn:// protocol if HTTP/DAV fails
            urls_to_try.append(url.replace('https://', 'svn://'))
        elif url.startswith('http://'):
            # Try HTTPS as fallback for HTTP
            urls_to_try.append(url.replace('http://', 'https://'))
            # Also try svn:// protocol if HTTP/DAV fails
            urls_to_try.append(url.replace('http://', 'svn://'))

        last_error = None
        http_dav_error = False

        for try_url in urls_to_try:
            cmd = ['svn', 'list', '--xml', try_url]

            if username:
                cmd.extend(['--username', username])
            if password:
                cmd.extend(['--password', password])

            # Enhanced SSL certificate handling for self-signed certificates
            cmd.extend([
                '--non-interactive',
                '--trust-server-cert',
                '--trust-server-cert-failures=unknown-ca,cn-mismatch,expired,not-yet-valid,other'
            ])

            # Additional flags for problematic SSL configurations
            if try_url.startswith('https://'):
                cmd.extend([
                    '--trust-server-cert-failures=unknown-ca',
                    '--config-option', 'servers:global:ssl-ignore-unknown-ca=yes',
                    '--config-option', 'servers:global:ssl-trust-default-ca=no'
                ])

            try:
                self.logger.debug(f"Running SVN command: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

                if result.returncode == 0:
                    self.logger.debug(f"SVN command succeeded with URL: {try_url}")
                    try:
                        return self._parse_svn_list_xml(result.stdout)
                    except Exception as parse_error:
                        self.logger.debug(f"Failed to parse XML output from {try_url}: {parse_error}")
                        self.logger.debug(f"XML output was: {result.stdout[:200]}...")

                        # Check if this might indicate the URL itself is a repository
                        if "might be a repository root" in str(parse_error):
                            self.logger.debug(f"URL {try_url} might be a repository root, testing with svn info")
                            if self._test_svn_info(try_url, username, password):
                                self.logger.info(f"Confirmed {try_url} is a repository root")
                                # Return empty list since this URL is a repo, not a directory listing
                                return []

                        # Try non-XML approach as fallback
                        try:
                            return self._try_non_xml_list(try_url, username, password)
                        except Exception as fallback_error:
                            self.logger.debug(f"Non-XML fallback also failed: {fallback_error}")
                            # If this is an HTTP URL and we got partial XML, try web-based detection
                            if try_url.startswith(('http://', 'https://')) and '<list' in result.stdout:
                                try:
                                    return self._try_web_based_detection(try_url, username, password)
                                except Exception as web_error:
                                    self.logger.debug(f"Web-based detection also failed: {web_error}")
                            last_error = f"XML parsing failed: {parse_error}, fallback failed: {fallback_error}"
                            continue
                else:
                    error_msg = f"SVN command failed: {result.stderr}"
                    self.logger.debug(f"SVN command failed with return code {result.returncode} for URL: {try_url}")
                    self.logger.debug(f"SVN stderr: {result.stderr}")
                    self.logger.debug(f"SVN stdout: {result.stdout}")

                    # Check for HTTP/DAV protocol error
                    if "E175003" in result.stderr and "HTTP/DAV protocol" in result.stderr:
                        http_dav_error = True
                        self.logger.debug(f"Detected HTTP/DAV protocol error for {try_url}")

                    # Check for SSL certificate errors (including self-signed certificate issues)
                    if any(ssl_indicator in result.stderr.lower() for ssl_indicator in
                           ["ssl", "certificate", "tls", "handshake", "verify failed"]):
                        self.logger.debug(f"Detected SSL/certificate error for {try_url}: {result.stderr.strip()}")

                        # Provide specific guidance for self-signed certificates
                        if "self-signed" in result.stderr.lower():
                            self.logger.warning(f"Self-signed certificate detected for {try_url}. "
                                              f"Consider using HTTP or configuring certificate trust.")

                        # If this is HTTPS and we have an HTTP fallback, prioritize it
                        if try_url.startswith('https://'):
                            http_fallback = try_url.replace('https://', 'http://')
                            if http_fallback in urls_to_try:
                                # Move HTTP fallback to front of remaining URLs
                                remaining_urls = [u for u in urls_to_try if u != try_url and u != http_fallback]
                                urls_to_try = [http_fallback] + remaining_urls
                                self.logger.debug("Prioritizing HTTP fallback due to SSL/certificate error")

                    last_error = error_msg

            except subprocess.TimeoutExpired:
                error_msg = "SVN command timed out"
                self.logger.debug(f"SVN command timed out for URL: {try_url}")
                last_error = error_msg
                continue
            except Exception as e:
                error_msg = f"Error running SVN command: {e}"
                self.logger.debug(f"SVN command error for URL: {try_url}: {e}")
                last_error = error_msg
                continue

        # If we get here, all URLs failed
        if http_dav_error:
            # Provide more helpful error message for HTTP/DAV issues
            self.logger.warning(f"SVN server at {url} does not support HTTP/DAV protocol. "
                              f"Tried protocols: {', '.join(urls_to_try)}")

            # Check if SSL/certificate issues were involved
            ssl_issues = any(ssl_indicator in str(last_error).lower() for ssl_indicator in
                           ["ssl", "certificate", "tls", "self-signed"])

            error_msg = "SVN server does not support HTTP/DAV protocol. "
            if ssl_issues:
                error_msg += "Additionally, SSL/certificate issues were detected (possibly self-signed certificate). "
                error_msg += "Consider using HTTP instead of HTTPS, or configuring certificate trust. "
            error_msg += f"The server at {url} may require svn:// protocol access or "
            error_msg += "may not be configured for web-based SVN access. "
            error_msg += "Consider manually adding repositories if you know their exact URLs. "
            error_msg += f"Last error: {last_error}"

            raise Exception(error_msg)
        elif last_error:
            raise Exception(last_error)
        else:
            raise Exception("SVN command failed for all attempted URLs")

    def _parse_svn_list_xml(self, xml_output: str) -> List[Dict]:
        """Parse SVN list XML output"""
        entries = []

        try:
            # Check if XML output looks incomplete
            if xml_output.strip().startswith('<lists>') and not xml_output.strip().endswith('</lists>'):
                # Try to extract the path from the partial XML
                import re
                path_match = re.search(r'path="([^"]+)"', xml_output)
                if path_match:
                    path = path_match.group(1)
                    self.logger.debug(f"Found partial XML with path: {path}")
                    # This might indicate the URL itself is a repository
                    raise Exception(f"Incomplete XML output - {path} might be a repository root rather than a directory listing")
                else:
                    raise Exception("Incomplete XML output - server may not fully support XML listing")

            root = ET.fromstring(xml_output)

            for entry in root.findall(".//entry"):
                name = entry.findtext("name", "")
                kind = entry.get("kind", "")
                size = entry.findtext("size")

                # Parse commit info if available
                commit = entry.find("commit")
                commit_info = {}
                if commit is not None:
                    commit_info = {
                        'commit_revision': int(commit.get("revision", 0)),
                        'commit_author': commit.findtext("author", ""),
                        'commit_date': commit.findtext("date", "")
                    }

                entry_info = {
                    'name': name,
                    'kind': kind,
                    'size': int(size) if size else 0,
                    **commit_info
                }

                entries.append(entry_info)

        except ET.ParseError as e:
            self.logger.error(f"Error parsing SVN XML output: {e}")

        return entries

    def _try_non_xml_list(self, url: str, username: Optional[str], password: Optional[str]) -> List[Dict]:
        """Try to get directory listing without XML format as fallback"""
        cmd = ['svn', 'list', url]  # No --xml flag

        if username:
            cmd.extend(['--username', username])
        if password:
            cmd.extend(['--password', password])

        cmd.extend([
            '--non-interactive',
            '--trust-server-cert',
            '--trust-server-cert-failures=unknown-ca,cn-mismatch,expired,not-yet-valid,other'
        ])

        self.logger.debug(f"Trying non-XML SVN list: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

        if result.returncode != 0:
            raise Exception(f"Non-XML SVN list failed: {result.stderr}")

        # Parse plain text output
        entries = []
        for line in result.stdout.strip().split('\n'):
            line = line.strip()
            if line:
                # Simple heuristic: directories end with '/', files don't
                if line.endswith('/'):
                    entries.append({
                        'name': line[:-1],  # Remove trailing slash
                        'kind': 'dir',
                        'size': 0
                    })
                else:
                    entries.append({
                        'name': line,
                        'kind': 'file',
                        'size': 0
                    })

        self.logger.debug(f"Non-XML list found {len(entries)} entries")
        return entries

    def _try_web_based_detection(self, url: str, username: Optional[str], password: Optional[str]) -> List[Dict]:
        """Try to detect repositories through web-based SVN interfaces"""
        try:
            import requests

            auth = (username, password) if username and password else None

            # Try to get the web page content (with SSL verification disabled for self-signed certificates)
            response = requests.get(url, auth=auth, timeout=10, verify=False)
            response.raise_for_status()

            content = response.text.lower()
            entries = []

            # Look for common patterns in web-based SVN browsers
            if any(pattern in content for pattern in ['viewvc', 'websvn', 'svn web', 'subversion']):
                self.logger.debug(f"Detected web-based SVN interface at {url}")

                # Try to extract directory/repository names from HTML
                import re

                # Look for href links that might be repositories or directories
                href_pattern = r'href=["\']([^"\']+)["\'][^>]*>([^<]+)</a>'
                matches = re.findall(href_pattern, content, re.IGNORECASE)

                for href, text in matches:
                    # Skip common navigation links
                    if any(skip in text.lower() for skip in ['parent', 'up', 'back', '..']):
                        continue

                    # Look for directory-like entries
                    if href.endswith('/') or 'dir' in text.lower() or len(text.strip()) > 0:
                        clean_name = text.strip()
                        if clean_name and not clean_name.startswith('['):
                            entries.append({
                                'name': clean_name,
                                'kind': 'dir' if href.endswith('/') else 'unknown',
                                'size': 0
                            })

                self.logger.debug(f"Web-based detection found {len(entries)} potential entries")
                return entries

            # If no web interface detected, return empty list
            return []

        except Exception as e:
            self.logger.debug(f"Web-based detection failed: {e}")
            raise

    def _create_repository_info(self, url: str, username: Optional[str], password: Optional[str]) -> Optional[RepositoryInfo]:
        """Create a RepositoryInfo object for a discovered repository"""
        try:
            # Extract repository name from URL
            repo_name = url.rstrip('/').split('/')[-1]
            if not repo_name:
                repo_name = url.rstrip('/').split('/')[-2]

            # Get additional repository information
            last_revision = None
            last_author = None
            last_date = None

            try:
                # Try to get latest revision info
                cmd = ['svn', 'info', url, '--xml']
                if username:
                    cmd.extend(['--username', username])
                if password:
                    cmd.extend(['--password', password])
                cmd.extend(['--non-interactive', '--trust-server-cert',
                           '--trust-server-cert-failures=unknown-ca,cn-mismatch,expired,not-yet-valid,other'])

                result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    import xml.etree.ElementTree as ET
                    root = ET.fromstring(result.stdout)
                    entry = root.find('entry')
                    if entry is not None:
                        commit = entry.find('commit')
                        if commit is not None:
                            last_revision = commit.get('revision')
                            last_author = commit.findtext('author', 'Unknown')
                            last_date = commit.findtext('date', '')
            except Exception as e:
                self.logger.debug(f"Could not get detailed info for {url}: {e}")

            return RepositoryInfo(
                name=repo_name,
                url=url,
                path=url,
                last_revision=last_revision,
                last_author=last_author,
                last_date=last_date,
                repository_type='svn'
            )

        except Exception as e:
            self.logger.error(f"Error creating repository info for {url}: {e}")
            return None

    def _is_repository_root(self, url: str, username: Optional[str], password: Optional[str]) -> bool:
        """Check if a URL is a repository root by looking for standard SVN structure or testing SVN info"""
        try:
            # First try to get SVN info directly - this is the most reliable way
            if self._test_svn_info(url, username, password):
                self.logger.debug(f"Confirmed {url} is a repository via svn info")
                return True

            # Fallback: check for standard SVN layout (trunk, branches, tags)
            entries = self._get_svn_list(url, username, password)
            entry_names = [entry['name'] for entry in entries if entry['kind'] == 'dir']
            self.logger.debug(f"Checking if {url} is repository root. Found directories: {entry_names}")

            # Check for standard SVN layout (trunk, branches, tags)
            has_trunk = 'trunk' in entry_names
            has_branches = 'branches' in entry_names
            has_tags = 'tags' in entry_names

            is_repo = has_trunk or (has_branches and has_tags)
            self.logger.debug(f"Repository check for {url}: trunk={has_trunk}, branches={has_branches}, tags={has_tags}, is_repo={is_repo}")

            return is_repo

        except Exception as e:
            self.logger.debug(f"Error checking repository root {url}: {e}")
            return False

    def get_repository_info(self, repo: RepositoryConfig) -> Dict[str, Any]:
        """Get detailed information about a repository"""
        try:
            cmd = ['svn', 'info', '--xml', repo.url]

            if repo.username:
                cmd.extend(['--username', repo.username])
            if repo.password:
                cmd.extend(['--password', repo.password])

            cmd.extend([
                '--non-interactive',
                '--trust-server-cert',
                '--trust-server-cert-failures=unknown-ca,cn-mismatch,expired,not-yet-valid,other'
            ])

            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                raise Exception(f"SVN info failed: {result.stderr}")

            return self._parse_svn_info_xml(result.stdout)

        except Exception as e:
            self.logger.error(f"Error getting repository info: {e}")
            return {}

    def _parse_svn_info_xml(self, xml_output: str) -> Dict[str, Any]:
        """Parse SVN info XML output"""
        info = {}

        try:
            root = ET.fromstring(xml_output)
            entry = root.find("entry")

            if entry is not None:
                info['url'] = entry.findtext("url", "")
                info['root'] = entry.findtext("repository/root", "")
                info['uuid'] = entry.findtext("repository/uuid", "")

                # Commit info
                commit = entry.find("commit")
                if commit is not None:
                    info['revision'] = commit.get("revision", "")
                    info['author'] = commit.findtext("author", "")
                    info['date'] = commit.findtext("date", "")

        except ET.ParseError as e:
            self.logger.error(f"Error parsing SVN info XML: {e}")

        return info

    def validate_repository_config(self, repo: RepositoryConfig) -> List[str]:
        """Validate SVN repository configuration"""
        errors = super().validate_repository_config(repo)

        # SVN-specific validation
        if repo.url and not (repo.url.startswith('http://') or
                           repo.url.startswith('https://') or
                           repo.url.startswith('svn://') or
                           repo.url.startswith('file://')):
            errors.append("SVN URL must start with http://, https://, svn://, or file://")

        return errors

    # Optimized historical scanning methods for SVN

    def get_revision_range(self, repo: RepositoryConfig, start_revision: Optional[str] = None,
                          end_revision: Optional[str] = None) -> List[str]:
        """
        Get list of SVN revisions in a range efficiently

        Args:
            repo: Repository configuration
            start_revision: Starting revision (inclusive), None for first revision
            end_revision: Ending revision (inclusive), None for latest revision

        Returns:
            List of revision identifiers in chronological order
        """
        try:
            # Get the actual latest revision from the repository
            latest = self.get_latest_revision(repo)
            if not latest:
                return []

            latest_num = int(latest)

            # Get end revision if not specified
            if not end_revision:
                end_revision = latest

            # Default start to revision 1 if not specified
            if not start_revision:
                start_revision = "1"

            # Convert to integers for range generation
            start_num = int(start_revision)
            end_num = int(end_revision)

            # SVN repositories start at revision 1, not 0
            if start_num < 1:
                self.logger.info(f"Start revision {start_num} adjusted to 1 (SVN minimum) for {repo.name}")
                start_num = 1

            # Validate that start revision is not greater than latest
            if start_num > latest_num:
                self.logger.warning(f"Start revision {start_num} is greater than latest revision {latest_num} for {repo.name}")
                return []

            # Clamp end revision to the actual latest revision
            if end_num > latest_num:
                self.logger.info(f"End revision {end_num} clamped to latest revision {latest_num} for {repo.name}")
                end_num = latest_num

            # Generate revision list (only existing revisions)
            return [str(rev) for rev in range(start_num, end_num + 1)]

        except (ValueError, TypeError) as e:
            self.logger.error(f"Error generating revision range for {repo.name}: {e}")
            return []

    def get_revisions_by_date_range(self, repo: RepositoryConfig, start_date: datetime,
                                   end_date: datetime) -> List[str]:
        """
        Get list of SVN revisions within a date range efficiently using svn log

        Args:
            repo: Repository configuration
            start_date: Starting date (inclusive)
            end_date: Ending date (inclusive)

        Returns:
            List of revision identifiers in chronological order
        """
        try:
            cmd = self.get_command_base(repo)

            # Format dates for SVN (ISO format)
            start_str = start_date.strftime('%Y-%m-%d')
            end_str = end_date.strftime('%Y-%m-%d')

            # Use SVN log with date range
            cmd.extend([
                "log", repo.url,
                "-r", f"{{{start_str}}}:{{{end_str}}}",
                "--xml", "--quiet"  # quiet mode for just revision numbers
            ])

            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            root = ET.fromstring(result.stdout)

            # Extract revision numbers
            revisions = []
            for entry in root.findall("logentry"):
                revision = entry.get("revision")
                if revision:
                    revisions.append(revision)

            # Sort revisions numerically
            revisions.sort(key=int)
            return revisions

        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error getting revisions by date range for {repo.name}: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Unexpected error getting revisions by date for {repo.name}: {e}")
            return []

    def get_commit_batch(self, repo: RepositoryConfig, revisions: List[str]) -> Iterator[CommitInfo]:
        """
        Get commit information for multiple SVN revisions efficiently using batch log command

        Args:
            repo: Repository configuration
            revisions: List of revision identifiers

        Yields:
            CommitInfo objects for each valid revision
        """
        if not revisions:
            return

        try:
            # Process revisions in batches to avoid command line length limits
            batch_size = 50  # Reasonable batch size for SVN

            for i in range(0, len(revisions), batch_size):
                batch = revisions[i:i + batch_size]

                # For SVN, we need to use range syntax or individual revisions
                # Convert batch to range if consecutive, otherwise use individual calls
                if len(batch) > 1 and self._is_consecutive_range(batch):
                    # Use range syntax for consecutive revisions
                    revision_range = f"{batch[0]}:{batch[-1]}"
                else:
                    # Use individual revision calls for non-consecutive revisions
                    for revision in batch:
                        yield from self._get_single_commit(repo, revision)
                    continue

                cmd = self.get_command_base(repo)
                cmd.extend([
                    "log", repo.url,
                    "-r", revision_range,
                    "--xml", "-v"  # verbose mode for changed paths
                ])

                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                    root = ET.fromstring(result.stdout)

                    # Process each log entry
                    for entry in root.findall("logentry"):
                        revision_attr = entry.get("revision")
                        if not revision_attr:
                            continue
                        revision = revision_attr  # Now guaranteed to be str

                        author = entry.findtext("author", "Unknown")
                        date = entry.findtext("date", "")
                        message = entry.findtext("msg", "")

                        # Extract changed paths
                        changed_paths = []
                        paths_elem = entry.find("paths")
                        if paths_elem is not None:
                            for path_elem in paths_elem.findall("path"):
                                if path_elem.text:
                                    changed_paths.append(path_elem.text)

                        # Get diff for this revision (this is expensive, consider making optional)
                        diff = self.get_diff(repo, revision) or ""

                        yield CommitInfo(
                            revision=revision,
                            author=author,
                            date=date,
                            message=message,
                            changed_paths=changed_paths,
                            diff=diff,
                            repository_id=repo.id,
                            repository_name=repo.name
                        )

                except subprocess.CalledProcessError as e:
                    self.logger.error(f"Error getting batch commits for {repo.name}: {e}")
                    continue

        except Exception as e:
            self.logger.error(f"Unexpected error in batch commit processing for {repo.name}: {e}")

    def _is_consecutive_range(self, revisions: List[str]) -> bool:
        """Check if revisions form a consecutive range"""
        try:
            nums = [int(r) for r in revisions]
            nums.sort()
            for i in range(1, len(nums)):
                if nums[i] != nums[i-1] + 1:
                    return False
            return True
        except (ValueError, TypeError):
            return False

    def _get_single_commit(self, repo: RepositoryConfig, revision: str):
        """Get commit info for a single revision"""
        try:
            commit_info = self.get_commit_info(repo, revision)
            if commit_info:
                yield commit_info
        except Exception as e:
            self.logger.error(f"Error getting single commit {revision} for {repo.name}: {e}")

    def get_repository_statistics(self, repo: RepositoryConfig) -> Dict[str, Any]:
        """
        Get SVN repository statistics for historical scanning planning

        Args:
            repo: Repository configuration

        Returns:
            Dictionary with statistics like total_revisions, first_revision, etc.
        """
        try:
            # Get repository info
            info = self.get_repository_info(repo)
            latest_revision = info.get('revision')

            if not latest_revision:
                return {'error': 'Could not determine latest revision'}

            # Get first revision (usually 1 for SVN, but let's check)
            cmd = self.get_command_base(repo)
            cmd.extend([
                "log", repo.url,
                "--limit", "1",
                "-r", "1:HEAD",
                "--xml"
            ])

            try:
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                root = ET.fromstring(result.stdout)
                first_entry = root.find("logentry")
                first_revision = first_entry.get("revision") if first_entry is not None else "1"
            except Exception as e:
                self.logger.debug(f"Could not determine first revision: {e}")
                first_revision = "1"  # Default assumption for SVN

            # Calculate statistics
            if latest_revision and first_revision:
                total_revisions = int(latest_revision) - int(first_revision) + 1
            else:
                total_revisions = 0

            return {
                'backend_type': self.backend_type,
                'latest_revision': latest_revision,
                'first_revision': first_revision,
                'total_revisions': total_revisions,
                'repository_root': info.get('root', ''),
                'repository_uuid': info.get('uuid', ''),
                'url': info.get('url', repo.url)
            }

        except Exception as e:
            self.logger.error(f"Error getting repository statistics for {repo.name}: {e}")
            return {'error': str(e)}
