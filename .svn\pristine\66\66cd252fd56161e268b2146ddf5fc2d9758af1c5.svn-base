{% extends "base.html" %}

{% block title %}Configuration - Repository Monitor{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Configuration</h1>
            <p class="page-subtitle">Configure global settings for repository monitoring and AI integration</p>
        </div>
        <button type="button" class="btn btn-outline-secondary" onclick="testOllamaConnection()">
            <i class="fas fa-plug"></i> Test Ollama Connection
        </button>
    </div>
</div>

<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cog"></i>Configuration</h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('save_config') }}">
                    <!-- SVN Server Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <h6 class="mb-0"><i class="fas fa-server"></i> SVN Server Configuration</h6>
                            <a href="{{ url_for('repository_discovery_page') }}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-search"></i> Discover Repositories
                            </a>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="svn_server_url" class="form-label">SVN Server Base URL</label>
                                <input type="url" class="form-control" id="svn_server_url" name="svn_server_url"
                                       value="{{ config.svn_server_url or '' }}"
                                       placeholder="http://sundc:81/svn">
                                <div class="form-text">Base URL of your SVN server (used for repository discovery)</div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="svn_server_username" class="form-label">Server Username</label>
                                        <input type="text" class="form-control" id="svn_server_username" name="svn_server_username"
                                               value="{{ config.svn_server_username or '' }}"
                                               placeholder="Optional">
                                        <div class="form-text">Default username for server access</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="svn_server_password" class="form-label">Server Password</label>
                                        <input type="password" class="form-control" id="svn_server_password" name="svn_server_password"
                                               value="{{ config.svn_server_password or '' }}"
                                               placeholder="Optional">
                                        <div class="form-text">Default password for server access</div>
                                    </div>
                                </div>
                            </div>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Repository Management:</strong> Individual repositories are configured in the
                                <a href="{{ url_for('repositories_page') }}" class="alert-link">Repositories</a> section.
                                Use the "Discover Repositories" button above to automatically find and import repositories from your SVN server.
                            </div>
                        </div>
                    </div>

                    <!-- Ollama Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-robot"></i> Ollama AI</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="ollama_host" class="form-label">Ollama Host</label>
                                        <input type="text" class="form-control" id="ollama_host" name="ollama_host" 
                                               value="{{ config.ollama_host }}" required
                                               placeholder="http://ollama:11434">
                                        <div class="form-text">URL of the Ollama server</div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="ollama_model" class="form-label">Model</label>
                                        <div class="input-group">
                                            <select class="form-select" id="ollama_model" name="ollama_model">
                                                <option value="">
                                                    <i class="fas fa-spinner fa-spin"></i> Loading models...
                                                </option>
                                            </select>
                                            <button class="btn btn-outline-secondary" type="button" id="refresh-models" title="Refresh available models">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                        </div>
                                        <div class="form-text" id="model-status">Available models from your Ollama server</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Monitoring Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-clock"></i> Monitoring</h6>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="check_interval" class="form-label">Check Interval (seconds)</label>
                                <input type="number" class="form-control" id="check_interval" name="check_interval" 
                                       value="{{ config.check_interval }}" min="60" max="86400" required>
                                <div class="form-text">How often to check for new commits (60-86400 seconds)</div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="generate_docs" name="generate_docs" 
                                               {% if config.generate_docs %}checked{% endif %}>
                                        <label class="form-check-label" for="generate_docs">
                                            Generate Documentation
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="send_emails" name="send_emails" 
                                               {% if config.send_emails %}checked{% endif %}>
                                        <label class="form-check-label" for="send_emails">
                                            Send Email Notifications
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Configuration -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <h6 class="mb-0"><i class="fas fa-envelope"></i> Email Settings</h6>
                        </div>
                        <div class="card-body" id="email-settings-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="mb-3">
                                        <label for="smtp_host" class="form-label">SMTP Host</label>
                                        <input type="text" class="form-control" id="smtp_host" name="smtp_host" 
                                               value="{{ config.smtp_host }}"
                                               placeholder="smtp.gmail.com">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="mb-3">
                                        <label for="smtp_port" class="form-label">SMTP Port</label>
                                        <input type="number" class="form-control" id="smtp_port" name="smtp_port" 
                                               value="{{ config.smtp_port }}" min="1" max="65535">
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="smtp_username" class="form-label">SMTP Username</label>
                                        <input type="text" class="form-control" id="smtp_username" name="smtp_username" 
                                               value="{{ config.smtp_username or '' }}"
                                               placeholder="Optional">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="smtp_password" class="form-label">SMTP Password</label>
                                        <input type="password" class="form-control" id="smtp_password" name="smtp_password" 
                                               value="{{ config.smtp_password or '' }}"
                                               placeholder="Optional">
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="email_from" class="form-label">From Address</label>
                                <input type="email" class="form-control" id="email_from" name="email_from" 
                                       value="{{ config.email_from }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="email_recipients" class="form-label">Global Recipients</label>
                                <textarea class="form-control" id="email_recipients" name="email_recipients" rows="3"
                                          placeholder="<EMAIL>, <EMAIL>">{{ config.email_recipients | join(', ') }}</textarea>
                                <div class="form-text">Global email recipients who receive notifications for ALL repositories (comma-separated). Repository-specific recipients can be configured in the Repositories section.</div>
                            </div>
                        </div>
                    </div>

                    <!-- Web Interface Configuration -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5><i class="fas fa-globe"></i> Web Interface Settings</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="web_log_entries" class="form-label">Log Entries Display Count</label>
                                        <input type="number" class="form-control" id="web_log_entries" name="web_log_entries"
                                               value="{{ config.web_log_entries }}" min="50" max="1000" required>
                                        <div class="form-text">Number of recent log entries to display on the logs page (50-1000)</div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle"></i>
                                        <strong>Note:</strong> Higher values may slow down the logs page loading time.
                                        Recommended range: 100-500 entries.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('index') }}" class="btn btn-secondary me-md-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Save Configuration
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Load available Ollama models
function loadOllamaModels() {
    const modelSelect = document.getElementById('ollama_model');
    const refreshBtn = document.getElementById('refresh-models');
    const statusText = document.getElementById('model-status');
    const currentValue = '{{ config.ollama_model }}';

    // Show loading state
    refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    refreshBtn.disabled = true;
    statusText.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Loading models from Ollama server...';

    fetch('/api/ollama/models')
        .then(response => response.json())
        .then(data => {
            // Clear existing options
            modelSelect.innerHTML = '';

            if (data.connected && data.models.length > 0) {
                // Add available models
                data.models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    if (model === currentValue) {
                        option.selected = true;
                    }
                    modelSelect.appendChild(option);
                });

                // If current model is not in the list, add it as the first option
                if (currentValue && !data.models.includes(currentValue)) {
                    const option = document.createElement('option');
                    option.value = currentValue;
                    option.textContent = currentValue + ' (not found)';
                    option.selected = true;
                    modelSelect.insertBefore(option, modelSelect.firstChild);
                }

                statusText.innerHTML = `<i class="fas fa-check text-success"></i> Found ${data.models.length} available models`;
            } else if (!data.connected) {
                // Ollama not connected
                const option = document.createElement('option');
                option.value = currentValue || 'llama2';
                option.textContent = (currentValue || 'llama2') + ' (Ollama not connected)';
                option.selected = true;
                modelSelect.appendChild(option);

                statusText.innerHTML = '<i class="fas fa-exclamation-triangle text-warning"></i> Ollama server not connected';
            } else {
                // No models available
                const option = document.createElement('option');
                option.value = currentValue || '';
                option.textContent = 'No models available';
                modelSelect.appendChild(option);

                statusText.innerHTML = '<i class="fas fa-info-circle text-info"></i> No models found on Ollama server';
            }
        })
        .catch(error => {
            console.error('Error loading models:', error);
            // Fallback to server-side models or current model
            const serverModels = {{ available_models | tojson }};
            modelSelect.innerHTML = '';

            if (serverModels && serverModels.length > 0) {
                serverModels.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model;
                    option.textContent = model;
                    if (model === currentValue) {
                        option.selected = true;
                    }
                    modelSelect.appendChild(option);
                });
                statusText.innerHTML = `<i class="fas fa-check text-success"></i> Using cached models (${serverModels.length} available)`;
            } else {
                // Final fallback to current model
                const option = document.createElement('option');
                option.value = currentValue || 'llama2';
                option.textContent = (currentValue || 'llama2') + ' (error loading models)';
                option.selected = true;
                modelSelect.appendChild(option);
                statusText.innerHTML = '<i class="fas fa-exclamation-circle text-danger"></i> Error loading models from server';
            }
        })
        .finally(() => {
            // Reset refresh button
            refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i>';
            refreshBtn.disabled = false;
        });
}

// Load models on page load
document.addEventListener('DOMContentLoaded', loadOllamaModels);

// Refresh models button
document.getElementById('refresh-models').addEventListener('click', loadOllamaModels);

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const checkInterval = parseInt(document.getElementById('check_interval').value);

    if (checkInterval < 60 || checkInterval > 86400) {
        alert('Check interval must be between 60 and 86400 seconds');
        e.preventDefault();
        return;
    }
});

// Show/hide email settings based on checkbox
function toggleEmailSettings() {
    const sendEmailsCheckbox = document.getElementById('send_emails');
    const emailSettings = document.getElementById('email-settings-body');

    if (!sendEmailsCheckbox || !emailSettings) {
        console.error('Email settings elements not found');
        return;
    }

    try {
        if (sendEmailsCheckbox.checked) {
            emailSettings.style.opacity = '1';
            emailSettings.style.pointerEvents = 'auto';
            emailSettings.querySelectorAll('input, textarea').forEach(el => {
                el.disabled = false;
                el.style.cursor = 'auto';
            });
        } else {
            emailSettings.style.opacity = '0.5';
            emailSettings.style.pointerEvents = 'none';
            emailSettings.querySelectorAll('input, textarea').forEach(el => {
                el.disabled = true;
                el.style.cursor = 'not-allowed';
            });
        }
    } catch (error) {
        console.error('Error toggling email settings:', error);
    }
}

document.getElementById('send_emails').addEventListener('change', toggleEmailSettings);

// Initialize email settings visibility on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleEmailSettings();
});
</script>
{% endblock %}
