{% extends "base.html" %}

{% block title %}Repositories - Repository Monitor{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Repository Management</h1>
            <p class="page-subtitle">Configure and manage your repositories</p>
        </div>
        <div class="btn-group">
            <a href="{{ url_for('repository_discovery_page') }}" class="btn btn-outline-primary">
                <i class="fas fa-search"></i> Discover Repositories
            </a>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRepositoryModal">
                <i class="fas fa-plus"></i> Add Repository
            </button>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-code-branch"></i>Repositories</h5>
            </div>
            <div class="card-body">
                {% if repositories %}
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Name</th>
                                    <th>URL</th>
                                    <th>Status</th>
                                    <th>Last Revision</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for repo in repositories %}
                                <tr>
                                    <td>
                                        <strong>{{ repo.name }}</strong>
                                        {% if not repo.enabled %}
                                            <span class="badge bg-secondary ms-2">Disabled</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ repo.url }}</small>
                                    </td>
                                    <td>
                                        <span class="status-indicator {% if repo.enabled %}status-running{% else %}status-stopped{% endif %}"></span>
                                        {% if repo.enabled %}Enabled{% else %}Disabled{% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ repo.last_revision }}</span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary"
                                                onclick="editRepository('{{ repo.id }}', '{{ repo.name }}', '{{ repo.url }}', '{{ repo.username or '' }}', {{ repo.enabled|lower }}, '{{ (repo.email_recipients or [])|join(', ') }}')">
                                            <i class="fas fa-edit"></i> Edit
                                        </button>
                                        <form method="POST" action="{{ url_for('delete_repository', repo_id=repo.id) }}" 
                                              style="display: inline;" 
                                              onsubmit="return confirm('Are you sure you want to delete repository \'{{ repo.name }}\'?')">
                                            <button type="submit" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </form>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-code-branch fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No repositories configured</h5>
                        <p class="text-muted">Add your first SVN repository to start monitoring.</p>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRepositoryModal">
                            <i class="fas fa-plus"></i> Add Repository
                        </button>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add Repository Modal -->
<div class="modal fade" id="addRepositoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Repository</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_repository') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="add_name" class="form-label">Repository Name *</label>
                        <input type="text" class="form-control" id="add_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="add_url" class="form-label">Repository URL *</label>
                        <input type="url" class="form-control" id="add_url" name="url" required>
                        <div class="form-text">Example: https://svn.example.com/repo</div>
                    </div>
                    <div class="mb-3">
                        <label for="add_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="add_username" name="username">
                        <div class="form-text">Leave empty for anonymous access</div>
                    </div>
                    <div class="mb-3">
                        <label for="add_password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="add_password" name="password">
                    </div>
                    <div class="mb-3">
                        <label for="add_email_recipients" class="form-label">Email Recipients</label>
                        <textarea class="form-control" id="add_email_recipients" name="email_recipients" rows="3"
                                  placeholder="<EMAIL>, <EMAIL>"></textarea>
                        <div class="form-text">Repository-specific email recipients (comma-separated). These will be added to the global recipients.</div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="add_enabled" name="enabled" checked>
                        <label class="form-check-label" for="add_enabled">
                            Enable monitoring for this repository
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Repository</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Repository Modal -->
<div class="modal fade" id="editRepositoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Repository</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editRepositoryForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Repository Name *</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_url" class="form-label">Repository URL *</label>
                        <input type="url" class="form-control" id="edit_url" name="url" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="edit_username" name="username">
                    </div>
                    <div class="mb-3">
                        <label for="edit_password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="edit_password" name="password">
                        <div class="form-text">Leave empty to keep current password</div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_email_recipients" class="form-label">Email Recipients</label>
                        <textarea class="form-control" id="edit_email_recipients" name="email_recipients" rows="3"
                                  placeholder="<EMAIL>, <EMAIL>"></textarea>
                        <div class="form-text">Repository-specific email recipients (comma-separated). These will be added to the global recipients.</div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_enabled" name="enabled">
                        <label class="form-check-label" for="edit_enabled">
                            Enable monitoring for this repository
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Repository</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function editRepository(id, name, url, username, enabled, emailRecipients) {
    document.getElementById('edit_name').value = name;
    document.getElementById('edit_url').value = url;
    document.getElementById('edit_username').value = username;
    document.getElementById('edit_enabled').checked = enabled;
    document.getElementById('edit_email_recipients').value = emailRecipients || '';

    // Update form action
    document.getElementById('editRepositoryForm').action = '/repositories/' + id + '/edit';
    
    // Show modal
    new bootstrap.Modal(document.getElementById('editRepositoryModal')).show();
}
</script>
{% endblock %}
