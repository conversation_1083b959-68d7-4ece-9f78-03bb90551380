# RepoSense AI - Public Docker Image Deployment
# Use this to deploy RepoSense AI from public container registries

version: '3.8'

services:
  # RepoSense AI from Docker Hub
  reposense-ai:
    container_name: reposense-ai
    image: your-dockerhub-username/reposense-ai:latest  # Replace with your Docker Hub image
    # Alternative: GitHub Container Registry
    # image: ghcr.io/your-github-username/reposense-ai:latest
    restart: unless-stopped
    ports:
      - "5000:5000"
    volumes:
      # Persistent data storage
      - repository_data:/app/data
      - repository_logs:/app/logs
      
      # Configuration file (create this locally)
      - ./config.production.json:/app/data/config.json:ro
      
      # SVN credentials (if needed)
      # - ~/.subversion:/home/<USER>/.subversion:ro
      
      # Git credentials (if needed)
      # - ~/.gitconfig:/home/<USER>/.gitconfig:ro
      # - ~/.git-credentials:/home/<USER>/.git-credentials:ro
    
    environment:
      # Application configuration
      - REPOSENSE_AI_CONFIG=/app/data/config.json
      - REPOSENSE_AI_DATA_DIR=/app/data
      - REPOSENSE_AI_LOG_DIR=/app/logs
      
      # Flask configuration
      - FLASK_ENV=production
      - FLASK_DEBUG=false
      
      # Ollama configuration (adjust URL as needed)
      - OLLAMA_BASE_URL=http://ollama:11434  # If using with Ollama service
      # - OLLAMA_BASE_URL=http://host.docker.internal:11434  # If Ollama on host
      # - OLLAMA_BASE_URL=http://*************:11434  # If Ollama on different machine
      
      # Security settings
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
    
    # Resource limits for production
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
    networks:
      - repository-network

  # Optional: Local Ollama service (if not using external Ollama)
  ollama:
    image: ollama/ollama:latest
    container_name: reposense-ai-ollama
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    networks:
      - repository-network
    # Uncomment for GPU support
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  # Optional: Nginx reverse proxy with SSL
  nginx:
    image: nginx:alpine
    container_name: reposense-ai-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro  # SSL certificates
    depends_on:
      - reposense-ai
    networks:
      - repository-network

volumes:
  repository_data:
    driver: local
  repository_logs:
    driver: local
  ollama_data:
    driver: local

networks:
  repository-network:
    driver: bridge
