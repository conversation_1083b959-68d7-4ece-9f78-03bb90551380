{"users": [{"id": "admin-user", "username": "admin", "email": "<EMAIL>", "full_name": "Administrator", "role": "admin", "enabled": true, "receive_all_notifications": true, "repository_subscriptions": [], "phone": "", "department": "IT", "created_date": "2025-08-02T00:00:00Z", "last_modified": "2025-08-02T00:00:00Z"}], "repositories": [{"id": "svn-monitor-test-repo", "name": "SVN Monitor Test Repository", "url": "http://sundc:81/svn/svn_monitor_server_test_repo", "username": "fvaneijk", "password": "an<PERSON><PERSON><PERSON>", "last_revision": 3, "enabled": true, "branch_path": "trunk", "monitor_all_branches": false, "assigned_users": ["admin-user"], "email_recipients": ["<EMAIL>"]}], "ollama_host": "http://ollama-server-local:11434", "ollama_model": "llama3:latest", "check_interval": 300, "svn_server_url": "http://sundc:81/svn", "svn_server_username": "fvaneijk", "svn_server_password": "an<PERSON><PERSON><PERSON>", "smtp_host": "localhost", "smtp_port": 587, "smtp_username": null, "smtp_password": null, "email_from": "<EMAIL>", "email_recipients": [], "output_dir": "/app/data/output", "generate_docs": true, "send_emails": false, "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "fdc5419469ad8d3be8891f07662d8eaeaaabf7ab126be3d7f2e85fd3aeadea2c", "web_log_entries": 300}