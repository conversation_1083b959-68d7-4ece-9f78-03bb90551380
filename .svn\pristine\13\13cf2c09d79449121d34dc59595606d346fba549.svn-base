#!/usr/bin/env python3
"""
Main monitoring service that orchestrates all components
Coordinates repository monitoring, content generation, and notifications
"""

import logging
import sys
import threading
import time
from datetime import datetime
from pathlib import Path

from config_manager import ConfigManager
from email_service import EmailService
from file_manager import FileManager
from models import Config, RepositoryConfig
from ollama_client import OllamaClient
from repository_backends import get_backend_manager


class MonitorService:
    """Main service that orchestrates repository monitoring and processing"""
    
    def __init__(self, config_path: str = "/app/data/config.json"):
        self.config_path = config_path
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.load_config()
        
        self.setup_logging()
        
        # Initialize components
        self.backend_manager = get_backend_manager()
        self.ollama_client = OllamaClient(self.config)
        self.email_service = EmailService(self.config)
        self.file_manager = FileManager(self.config)
        
        # Setup directories
        self.file_manager.setup_directories()
        
        # Monitoring state
        self.running = False
        self.monitor_thread = None
        self.last_check_time = None

        # Cached status to avoid slow dashboard loads
        self.ollama_connected_cache = None
        self.ollama_cache_time = None
        self.ollama_cache_duration = 30  # Cache for 30 seconds

        # Cached models list
        self.ollama_models_cache = None
        self.ollama_models_cache_time = None
        self.ollama_models_cache_duration = 300  # Cache models for 5 minutes

        self.logger.info("Monitor service initialized")
    
    def setup_logging(self):
        """Setup logging configuration"""
        handlers = [logging.StreamHandler(sys.stdout)]

        # Try to add file handler, but don't fail if directory doesn't exist
        try:
            log_dir = Path('/app/data')
            log_dir.mkdir(parents=True, exist_ok=True)
            handlers.append(logging.FileHandler('/app/data/repository_monitor.log'))
        except (OSError, PermissionError):
            # Fallback to local directory or temp directory
            try:
                log_dir = Path('./data')
                log_dir.mkdir(parents=True, exist_ok=True)
                handlers.append(logging.FileHandler('./data/repository_monitor.log'))
            except (OSError, PermissionError):
                # Just use console logging if file logging fails
                pass

        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=handlers
        )
        self.logger = logging.getLogger(__name__)
    
    def reload_config(self):
        """Reload configuration and update components"""
        self.config = self.config_manager.load_config()

        # Update components with new config
        self.ollama_client.config = self.config
        self.email_service.config = self.config
        self.file_manager.config = self.config

        # Clear cached status to force refresh with new config
        self.ollama_connected_cache = None
        self.ollama_cache_time = None
        self.ollama_models_cache = None
        self.ollama_models_cache_time = None

        self.logger.info("Configuration reloaded")
    
    def save_config(self):
        """Save current configuration"""
        self.config_manager.save_config(self.config)
    
    def update_config(self, new_config: Config):
        """Update configuration and save it"""
        self.config = new_config
        self.save_config()
        self.reload_config()
    
    def process_commit(self, repo: 'RepositoryConfig', revision: str):
        """Process a single commit for a specific repository"""
        backend = self.backend_manager.get_backend_for_repository(repo, self.config)
        if not backend:
            self.logger.error(f"No backend available for repository {repo.name}")
            return

        commit = backend.get_commit_info(repo, revision)
        if not commit:
            self.logger.error(f"Could not get commit info for revision {revision} in {repo.name}")
            return

        self.logger.info(f"Processing commit {revision} by {commit.author} in {repo.name}")

        # Generate documentation
        if self.config.generate_docs:
            documentation = self.ollama_client.generate_documentation(commit)
            if documentation:
                self.file_manager.save_documentation(commit, documentation)

        # Generate and send email
        if self.config.send_emails and self.email_service.is_configured():
            subject, body = self.ollama_client.generate_email_content(commit)
            if subject and body:
                if self.email_service.send_email(subject, body, commit):
                    self.file_manager.save_email_copy(subject, body, commit)
    
    def check_for_new_commits(self):
        """Check for new commits and process them across all enabled repositories"""
        enabled_repos = self.config.get_enabled_repositories()

        if not enabled_repos:
            self.logger.warning("No enabled repositories to monitor")
            return

        for repo in enabled_repos:
            if not self.running:  # Check if we should stop
                break

            self.check_repository_for_new_commits(repo)

    def check_repository_for_new_commits(self, repo: RepositoryConfig):
        """Check for new commits in a specific repository"""
        try:
            backend = self.backend_manager.get_backend_for_repository(repo, self.config)
            if not backend:
                self.logger.error(f"No backend available for repository {repo.name}")
                return

            latest_revision_str = backend.get_latest_revision(repo)
            if not latest_revision_str:
                self.logger.warning(f"Could not get latest revision for {repo.name}")
                return

            # Convert to int for SVN (other backends may need different handling)
            try:
                latest_revision = int(latest_revision_str)
            except ValueError:
                self.logger.error(f"Invalid revision format for {repo.name}: {latest_revision_str}")
                return

            if repo.last_revision == 0:
                # First run for this repository, just set the current revision
                repo.last_revision = latest_revision
                self.save_config()
                self.logger.info(f"Initial setup for {repo.name}: setting last revision to {latest_revision}")
                return

            if latest_revision > repo.last_revision:
                new_commits = latest_revision - repo.last_revision
                self.logger.info(f"Found {new_commits} new commits in {repo.name}")

                # Process each new commit
                for revision in range(repo.last_revision + 1, latest_revision + 1):
                    if not self.running:  # Check if we should stop
                        break
                    self.process_commit(repo, str(revision))

                # Update last processed revision for this repository
                repo.last_revision = latest_revision
                self.save_config()

        except Exception as e:
            self.logger.error(f"Error checking repository {repo.name}: {e}")
    
    def run_once(self):
        """Run a single check cycle"""
        try:
            self.logger.info("Checking for new commits...")
            self.check_for_new_commits()
        except Exception as e:
            self.logger.error(f"Error during check cycle: {e}")
    
    def run_daemon(self):
        """Run continuously as a daemon"""
        self.logger.info(f"Starting SVN monitor daemon (checking every {self.config.check_interval} seconds)")
        
        while self.running:
            try:
                self.run_once()
                self.last_check_time = datetime.now().isoformat()
                time.sleep(self.config.check_interval)
            except Exception as e:
                self.logger.error(f"Unexpected error in daemon loop: {e}")
                time.sleep(60)  # Wait a minute before retrying
        
        self.logger.info("Daemon stopped")
    
    def start_monitoring(self):
        """Start the monitoring process in a separate thread"""
        if not self.running:
            self.running = True
            self.monitor_thread = threading.Thread(target=self.run_daemon, daemon=True)
            self.monitor_thread.start()
            self.logger.info("Monitoring started")
    
    def stop_monitoring(self):
        """Stop the monitoring process"""
        if self.running:
            self.running = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=5)
            self.logger.info("Monitoring stopped")
    
    def is_running(self):
        """Check if monitoring is currently running"""
        return self.running and self.monitor_thread and self.monitor_thread.is_alive()

    def get_ollama_connection_status(self):
        """Get Ollama connection status with caching to avoid slow dashboard loads"""
        current_time = time.time()

        # Return cached result if still valid
        if (self.ollama_connected_cache is not None and
            self.ollama_cache_time is not None and
            current_time - self.ollama_cache_time < self.ollama_cache_duration):
            return self.ollama_connected_cache

        # Test connection and cache result
        try:
            self.ollama_connected_cache = self.ollama_client.test_connection()
            self.ollama_cache_time = current_time
            return self.ollama_connected_cache
        except Exception as e:
            self.logger.warning(f"Error testing Ollama connection: {e}")
            self.ollama_connected_cache = False
            self.ollama_cache_time = current_time
            return False

    def get_available_models(self):
        """Get available Ollama models with caching"""
        current_time = time.time()

        # Return cached result if still valid
        if (self.ollama_models_cache is not None and
            self.ollama_models_cache_time is not None and
            current_time - self.ollama_models_cache_time < self.ollama_models_cache_duration):
            return self.ollama_models_cache

        # Get models and cache result
        try:
            models = self.ollama_client.get_available_models()
            self.ollama_models_cache = models
            self.ollama_models_cache_time = current_time
            return models
        except Exception as e:
            self.logger.warning(f"Error getting Ollama models: {e}")
            # Return cached result if available, otherwise empty list
            return self.ollama_models_cache if self.ollama_models_cache is not None else []
    
    def get_status(self):
        """Get current status information"""
        enabled_repos = self.config.get_enabled_repositories()

        # Repository status information
        repositories_status = []
        for repo in self.config.repositories:
            repositories_status.append({
                "id": repo.id,
                "name": repo.name,
                "url": repo.url,
                "enabled": repo.enabled,
                "last_revision": repo.last_revision
            })

        return {
            "running": self.is_running(),
            "repositories": repositories_status,
            "enabled_repositories_count": len(enabled_repos),
            "total_repositories_count": len(self.config.repositories),
            "config_valid": len(enabled_repos) > 0,
            "ollama_connected": self.get_ollama_connection_status(),
            "last_check": self.last_check_time,
            "email_configured": self.email_service.is_configured()
        }
