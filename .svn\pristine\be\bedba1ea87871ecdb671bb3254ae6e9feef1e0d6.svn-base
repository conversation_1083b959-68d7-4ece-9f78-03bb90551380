# Development Guide

This guide covers development setup, architecture understanding, and contribution guidelines for the RepoSense AI project.

## Development Environment Setup

### Prerequisites

#### General Requirements
- Python 3.8 or higher
- Git
- Repository client tools (SVN, Git)
- Docker (for containerized development)
- Code editor (VS Code recommended)

#### Windows-Specific Requirements
- **Windows 10/11** (Pro, Enterprise, or Education for Hyper-V)
- **Docker Desktop for Windows** (latest version)
- **Git for Windows** (for cloning the repository)
- **PowerShell 5.1+** or **Windows Terminal** (recommended)

#### Optional but Recommended
- **Visual Studio Code** with Docker extension
- **Windows Subsystem for Linux (WSL2)** for better Docker performance

### Local Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd repository-monitor
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # Linux/Mac
   source venv/bin/activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt  # Development dependencies
   ```

4. **Setup configuration**
   ```bash
   cp config.example.json config.json
   # Edit config.json for your development environment
   ```

5. **Run the application**
   ```bash
   python reposense_ai_app.py
   ```

### Development Dependencies

Create `requirements-dev.txt`:
```
pytest>=7.0.0
pytest-cov>=4.0.0
black>=22.0.0
flake8>=5.0.0
mypy>=1.0.0
pre-commit>=2.20.0
```

### Windows Development Quick Start

For Windows developers using Docker Desktop:

#### 1. Install Docker Desktop
1. Download Docker Desktop from [docker.com](https://www.docker.com/products/docker-desktop)
2. Install and start Docker Desktop
3. Ensure Docker Desktop is running (check system tray)
4. Verify installation:
   ```powershell
   docker --version
   docker-compose --version
   ```

#### 2. Clone and Setup Repository
```powershell
# Clone the repository
git clone <repository-url>
cd repository-monitor

# Run the Windows setup script
.\setup-dev-windows.ps1
```

#### 3. Start Development Environment
```powershell
# Start the development environment
.\start-repository-monitor.ps1

# Or use batch file
start-repository-monitor.bat

# Or manually with Docker Compose
docker-compose -f docker-compose.dev.yml up -d
```

#### 4. Access the Application
- **Web Interface**: http://localhost:5001
- **Logs**: `docker-compose -f docker-compose.dev.yml logs -f`

## Project Structure

```
repository-monitor/
├── reposense_ai_app.py   # Main application entry point
├── config_manager.py           # Configuration management
├── monitor_service.py          # Core monitoring service
├── user_management_service.py  # User management
├── repository_discovery_service.py  # Repository discovery
├── web_interface.py            # Flask web interface
├── email_service.py            # Email notifications
├── ollama_service.py           # AI integration
├── file_manager.py             # File operations
├── repository_backends/        # Repository backend plugins
│   ├── base.py                 # Abstract base class
│   ├── svn_backend.py          # SVN operations
│   └── git_backend.py          # Git operations (future)
├── models.py                   # Data models
├── templates/                  # Jinja2 templates
│   ├── base.html
│   ├── index.html
│   ├── users.html
│   ├── repositories.html
│   └── ...
├── static/                     # Static assets (if any)
├── docs/                       # Documentation
├── tests/                      # Test files
├── config.example.json         # Example configuration
├── requirements.txt            # Python dependencies
├── Dockerfile                  # Container definition
├── docker-compose.yml          # Development compose
└── README.md                   # Project overview
```

## Hot Reload Development Environment

The RepoSense AI includes a development environment with hot reload capabilities for efficient development.

### Hot Reload Features

| File Type | Reload Method | Restart Required |
|-----------|---------------|------------------|
| **Templates** (`*.html`) | Immediate | ❌ No |
| **Static Files** (`*.css`, `*.js`) | Immediate | ❌ No |
| **Python Files** (`*.py`) | Auto-restart | ⚡ Automatic |
| **Configuration** (`config.json`) | Dynamic | ❌ No |

### Quick Start Development

```powershell
# Start development environment with hot reload
docker-compose -f docker-compose.dev.yml up -d

# View logs
docker-compose -f docker-compose.dev.yml logs -f repository-monitor

# Access web interface at http://localhost:5000
```

### Development Workflow

#### Template Development
```bash
# Edit template
vim templates/index.html

# 🔥 Changes appear immediately!
# Just refresh browser - no restart needed
```

#### Python Code Development
```bash
# Edit Python file
vim web_interface.py

# ⚡ Flask automatically restarts
# Watch logs: "Restarting with stat"
docker-compose -f docker-compose.dev.yml logs -f repository-monitor
```

#### Static File Development
```bash
# Edit CSS/JS
vim static/css/style.css

# 🔥 Changes appear immediately!
# Just refresh browser
```

### Debug Features

#### Flask Debug Mode
- **Enabled**: `FLASK_DEBUG=1`
- **Features**:
  - Detailed error pages with stack traces
  - Interactive debugger in browser
  - Auto-reloader for Python files
  - Template auto-reload

#### Debug Information
```bash
# View debug logs
docker-compose -f docker-compose.dev.yml logs repository-monitor | grep -i debug

# Check debugger PIN (for interactive debugging)
docker-compose -f docker-compose.dev.yml logs repository-monitor | grep "Debugger PIN"
```

### API Development

#### Dynamic Model Selection API

The configuration interface dynamically queries your Ollama server for available models:

**Endpoint**: `GET /api/ollama/models`

**Response format**:
```json
{
  "connected": true,
  "count": 7,
  "models": ["deepseek-r1:1.5b", "gemma3:4b", "llama3.2:1b", ...]
}
```

**Features**:
1. **Automatic Model Discovery**: Queries `/api/tags` endpoint to get available models
2. **Real-time Updates**: Refresh button to reload models without page refresh
3. **Caching**: Models are cached for 5 minutes to improve performance
4. **Fallback Support**: Graceful handling when Ollama is not connected
5. **Visual Feedback**: Loading spinners and status messages

## Architecture Overview

### Service Layer Architecture

The application follows a service-oriented architecture with clear separation of concerns:

1. **Web Interface Layer**: Flask-based HTTP interface
2. **Service Layer**: Business logic and operations
3. **Data Layer**: Configuration and file management
4. **Integration Layer**: External service connections

### Key Design Patterns

**Service Pattern**
```python
class UserManagementService:
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def create_user(self, ...):
        # Business logic here
        pass
```

**Repository Pattern**
```python
class ConfigManager:
    def load_config(self) -> Config:
        # Data access logic
        pass
    
    def save_config(self, config: Config):
        # Persistence logic
        pass
```

## Development Workflow

### Code Style and Standards

**Python Code Style**
- Follow PEP 8 guidelines
- Use Black for code formatting
- Maximum line length: 88 characters
- Use type hints where appropriate

**Example formatting**:
```python
from typing import List, Optional, Tuple
from dataclasses import dataclass

@dataclass
class User:
    id: str
    username: str
    email: str
    enabled: bool = True

def create_user(
    username: str, 
    email: str, 
    role: UserRole = UserRole.DEVELOPER
) -> Tuple[bool, str, Optional[User]]:
    """Create a new user with validation."""
    # Implementation here
    pass
```

### Git Workflow

**Branch Strategy**
- `main`: Production-ready code
- `develop`: Integration branch
- `feature/*`: Feature development
- `bugfix/*`: Bug fixes
- `hotfix/*`: Critical production fixes

**Commit Messages**
```
feat: add user management service
fix: resolve email notification bug
docs: update configuration guide
refactor: improve repository discovery logic
test: add unit tests for user service
```

### Testing Strategy

**Unit Tests**
```python
# tests/test_user_service.py
import pytest
from user_management_service import UserManagementService
from models import Config, User, UserRole

class TestUserManagementService:
    def setup_method(self):
        self.config = Config()
        self.service = UserManagementService(self.config)
    
    def test_create_user_success(self):
        success, message, user = self.service.create_user(
            username="testuser",
            email="<EMAIL>",
            full_name="Test User"
        )
        
        assert success is True
        assert user is not None
        assert user.username == "testuser"
    
    def test_create_user_duplicate_email(self):
        # First user
        self.service.create_user("user1", "<EMAIL>", "User One")
        
        # Duplicate email
        success, message, user = self.service.create_user(
            "user2", "<EMAIL>", "User Two"
        )
        
        assert success is False
        assert "already exists" in message
        assert user is None
```

**Integration Tests**
```python
# tests/test_web_interface.py
import pytest
from web_interface import WebInterface
from monitor_service import MonitorService

class TestWebInterface:
    def setup_method(self):
        # Setup test configuration
        pass
    
    def test_users_page_loads(self):
        # Test web interface endpoints
        pass
```

**Running Tests**
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=. --cov-report=html

# Run specific test file
pytest tests/test_user_service.py

# Run with verbose output
pytest -v
```

### Code Quality Tools

**Pre-commit Hooks**
Create `.pre-commit-config.yaml`:
```yaml
repos:
  - repo: https://github.com/psf/black
    rev: 22.10.0
    hooks:
      - id: black
        language_version: python3.8

  - repo: https://github.com/pycqa/flake8
    rev: 5.0.4
    hooks:
      - id: flake8

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.0.0
    hooks:
      - id: mypy
```

**Setup pre-commit**:
```bash
pre-commit install
```

## Adding New Features

### Feature Development Process

1. **Create feature branch**
   ```bash
   git checkout -b feature/new-feature-name
   ```

2. **Implement feature**
   - Write tests first (TDD approach)
   - Implement functionality
   - Update documentation

3. **Test thoroughly**
   ```bash
   pytest
   black .
   flake8
   mypy .
   ```

4. **Create pull request**
   - Describe changes clearly
   - Include test results
   - Update documentation

### Adding a New Service

**1. Create service class**
```python
# new_service.py
import logging
from typing import List, Optional
from models import Config

class NewService:
    """Service for handling new functionality."""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    def new_operation(self, param: str) -> bool:
        """Perform new operation."""
        try:
            # Implementation here
            self.logger.info(f"Performed operation with {param}")
            return True
        except Exception as e:
            self.logger.error(f"Operation failed: {e}")
            return False
```

**2. Add to web interface**
```python
# In web_interface.py
from new_service import NewService

class WebInterface:
    def __init__(self, monitor_service: MonitorService):
        # ... existing code ...
        self.new_service = NewService(monitor_service.config)
    
    def setup_routes(self):
        # ... existing routes ...
        
        @self.app.route('/new-feature')
        def new_feature_page():
            return render_template('new_feature.html')
```

**3. Create template**
```html
<!-- templates/new_feature.html -->
{% extends "base.html" %}

{% block title %}New Feature - RepoSense AI{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">New Feature</h1>
    <p class="page-subtitle">Description of new feature</p>
</div>

<div class="card">
    <div class="card-body">
        <!-- Feature content here -->
    </div>
</div>
{% endblock %}
```

**4. Add tests**
```python
# tests/test_new_service.py
import pytest
from new_service import NewService
from models import Config

class TestNewService:
    def setup_method(self):
        self.config = Config()
        self.service = NewService(self.config)
    
    def test_new_operation(self):
        result = self.service.new_operation("test")
        assert result is True
```

### Adding New Models

**1. Define data model**
```python
# In models.py
@dataclass
class NewModel:
    """New data model."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    created_date: Optional[str] = None
    
    def __post_init__(self):
        if not self.created_date:
            self.created_date = datetime.now().isoformat()
```

**2. Update configuration**
```python
# In models.py Config class
@dataclass
class Config:
    # ... existing fields ...
    new_models: List[NewModel] = field(default_factory=list)
    
    def add_new_model(self, model: NewModel) -> bool:
        """Add new model to configuration."""
        self.new_models.append(model)
        return True
```

## Debugging and Troubleshooting

### Logging Configuration

**Development logging**:
```python
import logging

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug.log'),
        logging.StreamHandler()
    ]
)
```

### Common Development Issues

**Import Errors**
```bash
# Check Python path
python -c "import sys; print(sys.path)"

# Verify virtual environment
which python
pip list
```

**Configuration Issues**
```python
# Validate configuration
from config_manager import ConfigManager
config_manager = ConfigManager()
config = config_manager.load_config()
is_valid, errors = config_manager.validate_config(config)
print(f"Valid: {is_valid}, Errors: {errors}")
```

**Database/File Issues**
```bash
# Check file permissions
ls -la config.json data/

# Verify directory structure
find . -type d -name "data" -exec ls -la {} \;
```

## Contributing Guidelines

### Pull Request Process

1. **Fork the repository**
2. **Create feature branch** from `develop`
3. **Make changes** with tests
4. **Update documentation** as needed
5. **Submit pull request** with clear description

### Code Review Checklist

- [ ] Code follows style guidelines
- [ ] Tests are included and passing
- [ ] Documentation is updated
- [ ] No breaking changes (or properly documented)
- [ ] Performance impact considered
- [ ] Security implications reviewed

### Release Process

1. **Version bump** in appropriate files
2. **Update CHANGELOG.md**
3. **Create release branch**
4. **Final testing**
5. **Merge to main**
6. **Tag release**
7. **Deploy to production**

## Performance Considerations

### Profiling

```python
import cProfile
import pstats

def profile_function():
    # Code to profile
    pass

# Profile execution
cProfile.run('profile_function()', 'profile_stats')
stats = pstats.Stats('profile_stats')
stats.sort_stats('cumulative').print_stats(10)
```

### Memory Management

```python
import tracemalloc

# Start tracing
tracemalloc.start()

# Your code here

# Get memory usage
current, peak = tracemalloc.get_traced_memory()
print(f"Current memory usage: {current / 1024 / 1024:.1f} MB")
print(f"Peak memory usage: {peak / 1024 / 1024:.1f} MB")
```

## Security Considerations

### Input Validation

```python
from typing import Optional
import re

def validate_email(email: str) -> bool:
    """Validate email format."""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def sanitize_input(input_str: str) -> str:
    """Sanitize user input."""
    # Remove potentially dangerous characters
    return re.sub(r'[<>"\']', '', input_str.strip())
```

### Secure Configuration

```python
import os
from cryptography.fernet import Fernet

def encrypt_sensitive_data(data: str, key: bytes) -> str:
    """Encrypt sensitive configuration data."""
    f = Fernet(key)
    return f.encrypt(data.encode()).decode()
```

## Windows Development Specifics

### PowerShell Scripts

The repository includes Windows-specific PowerShell scripts for development:

#### setup-dev-windows.ps1
- Creates necessary directories
- Sets up configuration files
- Builds Docker images
- Starts development environment

#### start-repository-monitor.ps1
- Starts the development environment
- Shows status and access information
- Provides helpful commands

### Windows Troubleshooting

#### 1. PowerShell Execution Policy
```powershell
# Check current policy
Get-ExecutionPolicy

# Set policy to allow script execution
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 2. Docker Desktop Not Running
- Check system tray for Docker Desktop icon
- Start Docker Desktop from Start menu
- Wait for "Docker Desktop is running" status

#### 3. Port Conflicts
If ports 5001 or 11434 are in use:
```powershell
# Check what's using the port
netstat -ano | findstr :5001

# Kill process if needed (replace PID)
taskkill /PID <process_id> /F
```

#### 4. Volume Mounting Issues
```powershell
# Ensure file sharing is enabled in Docker Desktop
# Settings > Resources > File Sharing
# Add your project directory if not already shared
```

#### 5. WSL2 Integration
For better performance, enable WSL2:
1. Install WSL2 from Microsoft Store
2. Enable WSL2 integration in Docker Desktop
3. Settings > General > Use WSL 2 based engine

### IDE Setup for Windows

#### Visual Studio Code Extensions
- **Docker** - Container management
- **Python** - Python development
- **GitLens** - Git integration
- **Thunder Client** - API testing

#### VS Code Settings
```json
{
    "python.defaultInterpreterPath": "./venv/Scripts/python.exe",
    "docker.showStartPage": false,
    "files.watcherExclude": {
        "**/node_modules/**": true,
        "**/.git/objects/**": true,
        "**/data/**": true,
        "**/logs/**": true
    }
}
```

### Windows Performance Optimization

#### Docker Desktop Settings
1. **Resources > Advanced**:
   - Memory: 4GB minimum, 8GB recommended
   - CPUs: 2 minimum, 4 recommended
   - Swap: 1GB

2. **Resources > File Sharing**:
   - Only share necessary directories
   - Avoid sharing entire C: drive

3. **General**:
   - Enable "Use WSL 2 based engine"
   - Enable "Send usage statistics" (optional)

#### Windows-Specific Tips
1. **Antivirus Exclusions**: Add project directory to antivirus exclusions
2. **Windows Defender**: Exclude Docker directories
3. **File System**: Use NTFS for better performance
4. **Background Apps**: Close unnecessary applications during development

### Windows Backup and Recovery

#### Configuration Backup
```powershell
# Backup configuration
copy config.json config.backup.json
copy data\config.json data\config.backup.json
```

#### Container Reset
```powershell
# Complete reset
docker-compose -f docker-compose.dev.yml down -v
docker system prune -f
.\setup-dev-windows.ps1

def decrypt_sensitive_data(encrypted_data: str, key: bytes) -> str:
    """Decrypt sensitive configuration data."""
    f = Fernet(key)
    return f.decrypt(encrypted_data.encode()).decode()
```

## Documentation Standards

### Code Documentation

```python
def complex_function(param1: str, param2: Optional[int] = None) -> Tuple[bool, str]:
    """
    Perform a complex operation with detailed documentation.
    
    Args:
        param1: Description of first parameter
        param2: Optional second parameter with default None
        
    Returns:
        Tuple containing:
        - bool: Success status
        - str: Result message or error description
        
    Raises:
        ValueError: If param1 is empty
        ConnectionError: If external service is unavailable
        
    Example:
        >>> success, message = complex_function("test", 42)
        >>> print(f"Success: {success}, Message: {message}")
    """
    pass
```

### API Documentation

Use docstrings and type hints consistently throughout the codebase to maintain clear, self-documenting code that helps both developers and automated documentation tools.

## Recent Development Work (v2.1.0)

### Major Enhancements Implemented

#### 1. User Feedback System
The system now includes comprehensive user feedback capabilities:

**Database Schema Changes:**
- Added 12 new fields for user feedback tracking
- Code review status, comments, and reviewer tracking
- Documentation quality ratings (1-5 stars)
- Risk assessment overrides with justification

**API Endpoints:**
```python
POST /api/documents/<doc_id>/feedback/code-review
POST /api/documents/<doc_id>/feedback/documentation
POST /api/documents/<doc_id>/feedback/risk-assessment
```

**Testing:**
```bash
python test_user_feedback.py  # Test all feedback endpoints
```

#### 2. Advanced Diff Viewing System
Implemented on-demand diff generation with multiple format support:

**Key Components:**
- `DiffService` class for on-demand diff generation
- Repository metadata storage (URL, type) instead of large diff content
- Side-by-side HTML diff rendering with CSS styling
- Format switching (unified ↔ side-by-side) without page reloads

**Testing:**
```bash
python test_decode_method.py     # Test encoding handling
python test_revision_69_fix.py   # Test specific encoding fixes
```

#### 3. Encoding & Binary File Handling
Resolved critical UTF-8 encoding crashes:

**Problem Solved:**
- System crashed with "utf-8 codec can't decode byte 0xe2" on binary files
- PDF files and other binary content caused system failures

**Solution Implemented:**
- Multi-encoding detection (UTF-8, Latin-1, CP1252, ISO-8859-1)
- Binary content detection and appropriate messaging
- Graceful fallback handling without system crashes

**Key Methods:**
```python
def _decode_svn_output(self, output_bytes: bytes) -> str:
    """Decode SVN output with multiple encoding fallback"""

def _is_likely_binary_content(self, content: str) -> bool:
    """Detect binary content based on character analysis"""
```

#### 4. Progress Calculation Fix
Fixed critical progress tracking bug:

**Issue:** Progress showed incorrect percentages (e.g., 715%) when scanning revision ranges not starting from revision 1.

**Fix:** Updated both JavaScript frontend and Python backend to calculate progress relative to selected range.

**Testing:**
```bash
python test_progress_fix.py  # Test progress calculation accuracy
```

#### 5. Hybrid AI Analysis System
Enhanced metadata extraction with dual approach:

**Architecture:**
- Fast heuristic pattern matching for common cases
- LLM fallback when heuristics fail or return incomplete data
- JSON validation and parsing of LLM responses

**Benefits:**
- More consistent metadata extraction
- Better handling of edge cases
- Improved reliability across diverse commit types

### Development Testing Approach

#### Test-Driven Development
Recent development has emphasized comprehensive testing:

1. **Unit Tests:** Individual component testing
2. **Integration Tests:** Service interaction testing
3. **End-to-End Tests:** Complete workflow validation
4. **Error Scenario Tests:** Edge case and failure handling

#### Test Files Created
```
test_user_feedback.py       # User feedback API testing
test_decode_method.py       # Encoding handling testing
test_revision_69_fix.py     # Specific bug fix validation
test_progress_fix.py        # Progress calculation testing
test_hybrid_extraction.py   # AI analysis testing
test_encoding_fix.py        # Comprehensive encoding tests
```

#### Running Tests
```bash
# Run individual test suites
python test_user_feedback.py
python test_decode_method.py
python test_progress_fix.py

# Test specific functionality
python test_revision_69_fix.py  # Test encoding fix for problematic revision
python test_hybrid_extraction.py  # Test AI analysis improvements
```

### Debugging Recent Issues

#### Common Issues and Solutions

**1. UTF-8 Encoding Errors**
```
Error: 'utf-8' codec can't decode byte 0xe2 in position 278
Solution: Multi-encoding detection in DiffService._decode_svn_output()
```

**2. Progress Calculation Issues**
```
Problem: Progress showing 715% (72/10) instead of correct percentage
Solution: Use processed_revisions instead of current_revision for calculation
```

**3. SVN Authentication for Diff**
```
Problem: Diff service couldn't access SVN repositories
Solution: Pass config_manager to DiffService for credential access
```

**4. Database Schema Mismatches**
```
Problem: Container crashes due to missing database columns
Solution: Recreate database for pre-production development
```

### Performance Considerations

#### Recent Optimizations
- **On-Demand Diff Generation:** Reduced database storage by generating diffs when needed
- **Hybrid AI Analysis:** Fast heuristics with LLM fallback only when necessary
- **Efficient Progress Tracking:** Accurate progress calculation without performance impact
- **Binary File Detection:** Early detection prevents unnecessary processing

#### Memory Management
- Repository metadata storage instead of large diff content
- Efficient encoding detection with early binary file identification
- Proper cleanup of subprocess resources in diff generation

### Future Development Guidelines

#### Code Quality Standards
1. **Comprehensive Testing:** All new features must include test coverage
2. **Error Handling:** Graceful degradation and meaningful error messages
3. **Documentation:** Update docs with all significant changes
4. **Performance:** Consider memory and processing impact of new features

#### Architecture Principles
1. **Service Separation:** Keep services modular and loosely coupled
2. **Configuration Management:** Use centralized config management
3. **Error Recovery:** Design for graceful failure handling
4. **User Experience:** Prioritize clear, helpful user interfaces

This recent development work demonstrates the project's commitment to robustness, user experience, and maintainable architecture while addressing real-world usage challenges.
