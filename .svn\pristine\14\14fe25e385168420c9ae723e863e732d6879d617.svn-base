"""
Multi-level Cache Manager for RepoSense AI

Provides high-performance caching with memory and file-based storage,
smart invalidation, and automatic cleanup for optimal performance.
"""

import os
import json
import time
import pickle
import hashlib
import logging
import threading
from pathlib import Path
from typing import Any, Optional, Dict, List, Union
from datetime import datetime, timedelta


class CacheManager:
    """Multi-level cache with memory and file storage"""
    
    def __init__(self, cache_dir: str = "/app/data/cache", 
                 memory_ttl: int = 300,  # 5 minutes
                 file_ttl: int = 3600,   # 1 hour
                 max_memory_items: int = 1000):
        self.cache_dir = Path(cache_dir)
        self.memory_ttl = memory_ttl
        self.file_ttl = file_ttl
        self.max_memory_items = max_memory_items
        self.logger = logging.getLogger(__name__)
        
        # Ensure cache directory exists
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # Memory cache
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        self._memory_lock = threading.RLock()
        
        # Cache statistics
        self.stats = {
            'memory_hits': 0,
            'file_hits': 0,
            'misses': 0,
            'evictions': 0,
            'invalidations': 0
        }
        
        # Start cleanup thread
        self._cleanup_thread = threading.Thread(target=self._cleanup_loop, daemon=True)
        self._cleanup_running = True
        self._cleanup_thread.start()
    
    def get(self, key: str, namespace: str = "default") -> Optional[Any]:
        """Get cached value with multi-level lookup"""
        cache_key = f"{namespace}:{key}"
        
        # Try memory cache first
        with self._memory_lock:
            if cache_key in self._memory_cache:
                entry = self._memory_cache[cache_key]
                if time.time() - entry['timestamp'] < self.memory_ttl:
                    self.stats['memory_hits'] += 1
                    return entry['data']
                else:
                    # Expired, remove from memory
                    del self._memory_cache[cache_key]
        
        # Try file cache
        file_path = self._get_cache_file_path(cache_key)
        if file_path.exists():
            try:
                with open(file_path, 'rb') as f:
                    entry = pickle.load(f)
                
                if time.time() - entry['timestamp'] < self.file_ttl:
                    # Valid file cache, promote to memory
                    self._set_memory_cache(cache_key, entry['data'])
                    self.stats['file_hits'] += 1
                    return entry['data']
                else:
                    # Expired file cache
                    file_path.unlink(missing_ok=True)
            
            except Exception as e:
                self.logger.warning(f"Error reading cache file {file_path}: {e}")
                file_path.unlink(missing_ok=True)
        
        # Cache miss
        self.stats['misses'] += 1
        return None
    
    def set(self, key: str, value: Any, namespace: str = "default", 
            memory_only: bool = False) -> bool:
        """Set cached value in memory and optionally file storage"""
        cache_key = f"{namespace}:{key}"
        
        try:
            # Always set in memory cache
            self._set_memory_cache(cache_key, value)
            
            # Set in file cache unless memory_only
            if not memory_only:
                self._set_file_cache(cache_key, value)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error setting cache for {cache_key}: {e}")
            return False
    
    def invalidate(self, key: str, namespace: str = "default") -> bool:
        """Invalidate specific cache entry"""
        cache_key = f"{namespace}:{key}"
        
        try:
            # Remove from memory
            with self._memory_lock:
                if cache_key in self._memory_cache:
                    del self._memory_cache[cache_key]
            
            # Remove from file
            file_path = self._get_cache_file_path(cache_key)
            file_path.unlink(missing_ok=True)
            
            self.stats['invalidations'] += 1
            return True
            
        except Exception as e:
            self.logger.error(f"Error invalidating cache for {cache_key}: {e}")
            return False
    
    def invalidate_namespace(self, namespace: str) -> int:
        """Invalidate all entries in a namespace"""
        invalidated = 0
        prefix = f"{namespace}:"
        
        try:
            # Remove from memory
            with self._memory_lock:
                keys_to_remove = [k for k in self._memory_cache.keys() if k.startswith(prefix)]
                for key in keys_to_remove:
                    del self._memory_cache[key]
                    invalidated += 1
            
            # Remove from file cache
            for cache_file in self.cache_dir.glob("*.cache"):
                try:
                    # Decode filename to check namespace
                    filename = cache_file.stem
                    if filename.startswith(self._encode_key(prefix)):
                        cache_file.unlink()
                        invalidated += 1
                except Exception:
                    continue
            
            self.stats['invalidations'] += invalidated
            return invalidated
            
        except Exception as e:
            self.logger.error(f"Error invalidating namespace {namespace}: {e}")
            return invalidated
    
    def clear_all(self) -> bool:
        """Clear all cached data"""
        try:
            # Clear memory cache
            with self._memory_lock:
                self._memory_cache.clear()
            
            # Clear file cache
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink(missing_ok=True)
            
            self.logger.info("All cache data cleared")
            return True
            
        except Exception as e:
            self.logger.error(f"Error clearing cache: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._memory_lock:
            memory_size = len(self._memory_cache)
        
        file_count = len(list(self.cache_dir.glob("*.cache")))
        
        total_requests = sum([
            self.stats['memory_hits'],
            self.stats['file_hits'], 
            self.stats['misses']
        ])
        
        hit_rate = 0.0
        if total_requests > 0:
            hits = self.stats['memory_hits'] + self.stats['file_hits']
            hit_rate = (hits / total_requests) * 100
        
        return {
            **self.stats,
            'memory_size': memory_size,
            'file_count': file_count,
            'hit_rate_percent': round(hit_rate, 2),
            'total_requests': total_requests
        }
    
    def _set_memory_cache(self, cache_key: str, value: Any):
        """Set value in memory cache with eviction"""
        with self._memory_lock:
            # Check if we need to evict
            if len(self._memory_cache) >= self.max_memory_items:
                self._evict_oldest_memory_entry()
            
            self._memory_cache[cache_key] = {
                'data': value,
                'timestamp': time.time()
            }
    
    def _set_file_cache(self, cache_key: str, value: Any):
        """Set value in file cache"""
        file_path = self._get_cache_file_path(cache_key)
        
        try:
            entry = {
                'data': value,
                'timestamp': time.time()
            }
            
            with open(file_path, 'wb') as f:
                pickle.dump(entry, f)
                
        except Exception as e:
            self.logger.warning(f"Error writing cache file {file_path}: {e}")
    
    def _get_cache_file_path(self, cache_key: str) -> Path:
        """Get file path for cache key"""
        # Encode key to make it filesystem-safe
        encoded_key = self._encode_key(cache_key)
        return self.cache_dir / f"{encoded_key}.cache"
    
    def _encode_key(self, key: str) -> str:
        """Encode cache key for filesystem safety"""
        return hashlib.md5(key.encode()).hexdigest()
    
    def _evict_oldest_memory_entry(self):
        """Evict oldest entry from memory cache"""
        if not self._memory_cache:
            return
        
        oldest_key = min(self._memory_cache.keys(), 
                        key=lambda k: self._memory_cache[k]['timestamp'])
        del self._memory_cache[oldest_key]
        self.stats['evictions'] += 1
    
    def _cleanup_loop(self):
        """Background cleanup of expired cache entries"""
        while self._cleanup_running:
            try:
                self._cleanup_expired_files()
                time.sleep(300)  # Run every 5 minutes
            except Exception as e:
                self.logger.error(f"Error in cache cleanup: {e}")
                time.sleep(60)  # Wait 1 minute on error
    
    def _cleanup_expired_files(self):
        """Remove expired file cache entries"""
        current_time = time.time()
        removed_count = 0
        
        for cache_file in self.cache_dir.glob("*.cache"):
            try:
                # Check file modification time first (quick check)
                if current_time - cache_file.stat().st_mtime > self.file_ttl:
                    cache_file.unlink()
                    removed_count += 1
                    continue
                
                # Check actual cache entry timestamp
                with open(cache_file, 'rb') as f:
                    entry = pickle.load(f)
                
                if current_time - entry['timestamp'] > self.file_ttl:
                    cache_file.unlink()
                    removed_count += 1
                    
            except Exception:
                # Remove corrupted cache files
                cache_file.unlink(missing_ok=True)
                removed_count += 1
        
        if removed_count > 0:
            self.logger.debug(f"Cleaned up {removed_count} expired cache files")
    
    def stop(self):
        """Stop background cleanup thread"""
        self._cleanup_running = False
        if self._cleanup_thread.is_alive():
            self._cleanup_thread.join(timeout=5.0)
