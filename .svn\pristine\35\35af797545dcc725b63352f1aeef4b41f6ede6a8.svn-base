#!/bin/bash

# SVN Monitor Docker Setup Script
# This script helps set up the SVN Monitor application with Docker

set -e

echo "🚀 SVN Monitor Docker Setup"
echo "=========================="

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p data
mkdir -p config

# Check if Docker Compose is available
if command -v docker-compose &> /dev/null; then
    DOCKER_COMPOSE="docker-compose -f docker_compose.yml"
elif docker compose version &> /dev/null; then
    DOCKER_COMPOSE="docker compose -f docker_compose.yml"
else
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Build and start the services
echo "🔨 Building and starting services..."
$DOCKER_COMPOSE up -d --build

echo ""
echo "⏳ Waiting for services to be ready..."
sleep 10

# Wait for <PERSON>lla<PERSON> to be ready
echo "🤖 Waiting for Ollama to be ready..."
timeout=120
counter=0
while ! curl -f http://localhost:11434/api/tags > /dev/null 2>&1; do
    sleep 5
    counter=$((counter + 5))
    if [ $counter -ge $timeout ]; then
        echo "⚠️  Timeout waiting for Ollama to start (this is normal on first run)"
        echo "   Ollama will continue downloading the model in the background"
        break
    fi
    echo "   Waiting... ($counter/$timeout seconds)"
done

if [ $counter -lt $timeout ]; then
    echo "✅ Ollama is ready!"

    # Pull the default model if Ollama is ready
    echo "📥 Pulling default model (llama2)..."
    curl -X POST http://localhost:11434/api/pull -d '{"name":"llama2"}' > /dev/null 2>&1 || echo "   Model will be downloaded on first use"
fi

# Create initial configuration if it doesn't exist
if [ ! -f data/config.json ]; then
    echo "📝 Creating sample configuration..."
    # Create minimal configuration
    cat > data/config.json << 'EOF'
{
  "repositories": [],
  "ollama_host": "http://ollama:11434",
  "ollama_model": "llama2",
  "check_interval": 300,
  "smtp_host": "localhost",
  "smtp_port": 587,
  "smtp_username": null,
  "smtp_password": null,
  "email_from": "<EMAIL>",
  "email_recipients": [],
  "output_dir": "/app/data",
  "generate_docs": true,
  "send_emails": false,
  "web_enabled": true,
  "web_port": 5000,
  "web_host": "0.0.0.0",
  "web_secret_key": "change-this-secret-key-in-production"
}
EOF
        echo "✅ Sample configuration created at data/config.json"
    fi
    echo "💡 See config.example.json for detailed configuration options"
else
    echo "ℹ️  Configuration file already exists at data/config.json"
fi

echo ""
echo "✅ Setup complete!"
echo ""
echo "🌐 Web interface will be available at: http://localhost:5000"
echo "🤖 Ollama API will be available at: http://localhost:11434"
echo ""
echo "📋 Next steps:"
echo "1. Wait for Ollama to download the model (this may take a few minutes)"
echo "2. Open http://localhost:5000 in your browser"
echo "3. Go to the 'Repositories' section to add your SVN repositories"
echo "4. Configure email settings in the 'Configuration' section if needed"
echo "5. Start monitoring from the dashboard"
echo ""
echo "📊 To check the status:"
echo "   $DOCKER_COMPOSE logs -f"
echo ""
echo "🛑 To stop the services:"
echo "   $DOCKER_COMPOSE down"
echo ""
echo "📁 Data is stored in the './data' directory"
echo "   - Configuration: ./data/config.json"
echo "   - Logs: ./data/svn_monitor.log"
echo "   - Repository outputs: ./data/repositories/"
