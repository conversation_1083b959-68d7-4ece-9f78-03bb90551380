#!/usr/bin/env python3
"""
Test script to verify the encoding fix specifically for revision 69
"""

import requests
import json

def test_revision_69_fix():
    """Test the encoding fix for the specific revision 69 that was causing issues"""
    
    base_url = "http://localhost:5001"
    
    # Correct document ID for revision 69 from the vex repository
    doc_id = "3de22d15-a6d4-4c92-bdeb-db44dc5ffef9_69"
    
    print("🧪 Testing Encoding Fix for Revision 69")
    print("=" * 50)
    
    # Test 1: Try to get unified diff for revision 69
    print(f"\n1. Testing unified diff for revision 69:")
    try:
        response = requests.get(f"{base_url}/api/documents/{doc_id}/diff?format=unified")
        if response.status_code == 200:
            result = response.json()
            diff_content = result.get('diff', '')
            print(f"   ✅ Status: {response.status_code}")
            print(f"   ✅ Diff length: {len(diff_content)} characters")
            
            # Check if we got the old UTF-8 decode error
            if "Error running SVN diff: 'utf-8' codec can't decode" in diff_content:
                print(f"   ❌ Still getting UTF-8 decode error!")
                print(f"   ❌ Error: {diff_content}")
            elif "Binary file content detected" in diff_content:
                print(f"   ✅ Properly detected binary content (PDF file)")
                print(f"   ✅ Message: {diff_content}")
            elif "No differences found" in diff_content:
                print(f"   ✅ No differences found (expected for some revisions)")
            elif diff_content.startswith("Index:") or diff_content.startswith("==="):
                print(f"   ✅ Got valid diff content")
                print(f"   ✅ First 200 chars: {diff_content[:200]}...")
            else:
                print(f"   ℹ️  Got response: {diff_content}")
                
        else:
            print(f"   ❌ Error: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 2: Try side-by-side diff format
    print(f"\n2. Testing side-by-side diff for revision 69:")
    try:
        response = requests.get(f"{base_url}/api/documents/{doc_id}/diff?format=side-by-side")
        if response.status_code == 200:
            result = response.json()
            diff_content = result.get('diff', '')
            format_type = result.get('format', 'unknown')
            print(f"   ✅ Status: {response.status_code}")
            print(f"   ✅ Format: {format_type}")
            
            # Check the response
            if "Error running SVN diff: 'utf-8' codec can't decode" in diff_content:
                print(f"   ❌ Still getting UTF-8 decode error!")
            elif "Binary file content detected" in diff_content:
                print(f"   ✅ Properly detected binary content")
            elif "<table" in diff_content and "diff-table" in diff_content:
                print(f"   ✅ Got valid side-by-side HTML diff")
                print(f"   ✅ HTML length: {len(diff_content)} characters")
            else:
                print(f"   ℹ️  Response: {diff_content[:200]}...")
                
        else:
            print(f"   ❌ Error: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 3: Test document view page
    print(f"\n3. Testing document view page for revision 69:")
    try:
        response = requests.get(f"{base_url}/documents/{doc_id}")
        if response.status_code == 200:
            content = response.text
            print(f"   ✅ Status: {response.status_code}")
            print(f"   ✅ Page loaded successfully")
            
            # Check for the old error in the HTML
            if "'utf-8' codec can't decode" in content:
                print(f"   ❌ UTF-8 decode error still present in page")
            else:
                print(f"   ✅ No encoding errors in document view page")
            
            # Check if it contains the expected document info
            if "revision_69.md" in content:
                print(f"   ✅ Contains expected document filename")
            if "particle filter tutorial.pdf" in content:
                print(f"   ✅ Contains expected commit message about PDF")
                
        else:
            print(f"   ❌ Error: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Revision 69 Encoding Fix Test Complete!")
    print("\nThe fix should handle the PDF file properly by:")
    print("- Detecting binary content and showing appropriate message")
    print("- Not crashing with UTF-8 decode errors")
    print("- Gracefully handling the encoding issues")

if __name__ == "__main__":
    test_revision_69_fix()
