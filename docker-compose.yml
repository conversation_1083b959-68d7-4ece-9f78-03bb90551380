services:
  # RepoSense AI
  reposense-ai:
    build:
      context: .
      dockerfile: Dockerfile.dev
    image: reposense-ai:latest
    container_name: reposense-ai
    restart: unless-stopped
    ports:
      - "5000:5000"  # RepoSense AI web interface
    volumes:
      # Configuration file
      - ./config.json:/app/config.json:ro
      # Data persistence
      - ./data:/app/data
      # Logs directory
      - ./logs:/app/logs
      # Source code for development
      - .:/app
      # Exclude unnecessary directories
      - /app/node_modules
      - /app/.git
      - /app/__pycache__
      - /app/.pytest_cache
      # SVN credentials (Windows)
      - ${APPDATA}/Subversion:/host-svn-credentials:ro
    environment:
      # Development overrides - remove for production
      - REPOSENSE_AI_ENV=development
      - FLASK_DEBUG=1
      - REPOSENSE_AI_LOG_LEVEL=DEBUG
      - PYTHONUNBUFFERED=1
      # Ollama configuration - adjust for your environment
      - OLLAMA_BASE_URL=http://************:11434
      - OLLAMA_MODEL=codeqwen:7b-chat-v1.5-q8_0
      # SVN server configuration
      - SVN_SERVER_URL=http://***********:81/svn
      # Web interface
      - REPOSENSE_AI_WEB_HOST=0.0.0.0
      - REPOSENSE_AI_WEB_PORT=5000
    networks:
      - reposense-ai-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "lserver2.TMW.local:************"
      - "sundc:***********"
    healthcheck:
      test: ["CMD", "python", "config_summary.py"]
      interval: 60s
      timeout: 15s
      retries: 3
      start_period: 60s

# Network
networks:
  reposense-ai-network:
    driver: bridge
