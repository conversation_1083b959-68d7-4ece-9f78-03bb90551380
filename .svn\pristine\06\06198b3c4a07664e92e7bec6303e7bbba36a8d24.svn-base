# RepoSense AI - Build and Deployment Makefile

.PHONY: help clean build build-binary build-docker test package deploy-dev deploy-prod

# Default target
help:
	@echo "RepoSense AI - Build and Deployment Commands"
	@echo "=================================================="
	@echo ""
	@echo "Development:"
	@echo "  dev-setup     - Set up development environment"
	@echo "  dev-run       - Run development server"
	@echo "  test          - Run test suite"
	@echo ""
	@echo "Building:"
	@echo "  clean         - Clean build artifacts"
	@echo "  build-binary  - Build standalone binary"
	@echo "  build-docker  - Build Docker images"
	@echo "  package       - Create distribution package"
	@echo ""
	@echo "Deployment:"
	@echo "  deploy-dev    - Deploy development environment"
	@echo "  deploy-prod   - Deploy production environment"
	@echo "  deploy-binary - Deploy binary distribution"
	@echo ""
	@echo "Maintenance:"
	@echo "  logs          - View application logs"
	@echo "  backup        - Backup application data"
	@echo "  restore       - Restore application data"

# Development setup
dev-setup:
	@echo "🔧 Setting up development environment..."
	pip install -r requirements.txt
	pip install pyinstaller pytest
	@echo "✅ Development environment ready"

dev-run:
	@echo "🚀 Starting development server..."
	python reposense_ai_app.py

# Testing
test:
	@echo "🧪 Running test suite..."
	python -m pytest test_*.py -v
	@echo "✅ Tests completed"

# Cleaning
clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -rf build/ dist/ __pycache__/ *.pyc *.pyo
	rm -rf .pytest_cache/ *.egg-info/
	find . -name "*.pyc" -delete
	find . -name "__pycache__" -type d -exec rm -rf {} +
	@echo "✅ Clean completed"

# Binary building
build-binary: clean
	@echo "🔨 Building standalone binary..."
	python build-binary.py
	@echo "✅ Binary build completed"

# Docker building
build-docker:
	@echo "🐳 Building Docker images..."
	docker build -f Dockerfile.production -t repository-monitor:latest .
	docker build -f Dockerfile.dev -t repository-monitor:dev .
	@echo "✅ Docker images built"

# Package creation
package: build-binary
	@echo "📦 Creating distribution package..."
	@echo "✅ Package created in dist/repository-monitor-package/"

# Development deployment
deploy-dev:
	@echo "🚀 Deploying development environment..."
	docker-compose -f docker-compose.dev.yml up -d
	@echo "✅ Development environment deployed"
	@echo "Access at: http://localhost:5000"

# Production deployment
deploy-prod:
	@echo "🚀 Deploying production environment..."
	docker-compose -f docker-compose.production.yml up -d
	@echo "✅ Production environment deployed"
	@echo "Access at: http://localhost:5000"

# Binary deployment
deploy-binary: package
	@echo "🚀 Deploying binary distribution..."
	cd dist/repository-monitor-package && ./start-repository-monitor.sh
	@echo "✅ Binary deployment started"

# Maintenance commands
logs:
	@echo "📋 Viewing application logs..."
	docker-compose logs -f repository-monitor

backup:
	@echo "💾 Creating backup..."
	mkdir -p backups
	docker run --rm -v repository-monitor_repository_data:/data -v $(PWD)/backups:/backup ubuntu tar czf /backup/repository-monitor-backup-$(shell date +%Y%m%d-%H%M%S).tar.gz -C /data .
	@echo "✅ Backup created in backups/"

restore:
	@echo "🔄 Restoring from backup..."
	@echo "Available backups:"
	@ls -la backups/
	@echo "To restore, run: docker run --rm -v repository-monitor_repository_data:/data -v $(PWD)/backups:/backup ubuntu tar xzf /backup/BACKUP_FILE.tar.gz -C /data"

# Docker management
docker-stop:
	@echo "🛑 Stopping Docker containers..."
	docker-compose -f docker-compose.production.yml down
	docker-compose -f docker-compose.dev.yml down

docker-clean:
	@echo "🧹 Cleaning Docker resources..."
	docker system prune -f
	docker volume prune -f

# Release management
release: clean test build-binary build-docker
	@echo "🎉 Creating release..."
	@echo "✅ Release artifacts ready:"
	@echo "  - Binary: dist/repository-monitor-package/"
	@echo "  - Docker: repository-monitor:latest"

# Quick commands
run: deploy-dev
stop: docker-stop
restart: docker-stop deploy-dev

# Platform-specific builds
build-linux:
	@echo "🐧 Building for Linux..."
	docker run --rm -v $(PWD):/app -w /app python:3.11-slim bash -c "apt-get update && apt-get install -y gcc && pip install -r requirements.txt && python build-binary.py"

build-windows:
	@echo "🪟 Building for Windows (requires Windows or Wine)..."
	python build-binary.py

build-macos:
	@echo "🍎 Building for macOS..."
	python build-binary.py
