#!/bin/bash
# RepoSense AI Startup Script

set -e

# Get script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# Create data directory if it doesn't exist
mkdir -p data logs

# Initialize configuration if not exists
if [ ! -f data/config.json ]; then
    echo "Creating minimal configuration..."
    cat > data/config.json << 'EOF'
{
  "repositories": [],
  "ollama_host": "http://localhost:11434",
  "ollama_model": "llama2",
  "check_interval": 300,
  "smtp_host": "localhost",
  "smtp_port": 587,
  "smtp_username": null,
  "smtp_password": null,
  "email_from": "svn-monitor@localhost",
  "email_recipients": [],
  "output_dir": "/app/data",
  "generate_docs": true,
  "send_emails": false,
  "web_enabled": true,
  "web_port": 5000,
  "web_host": "0.0.0.0",
  "web_secret_key": "change-this-secret-key-in-production",
  "users": []
}
EOF
    echo "Configuration initialized. Please customize data/config.json"
    echo "Press Enter to continue..."
    read
fi

# Set environment variables
export REPOSENSE_AI_CONFIG="$SCRIPT_DIR/data/config.json"
export REPOSENSE_AI_DATA_DIR="$SCRIPT_DIR/data"
export REPOSENSE_AI_LOG_DIR="$SCRIPT_DIR/logs"

# Start RepoSense AI
echo "Starting RepoSense AI..."
echo "Access the web interface at: http://localhost:5000"
echo "Press Ctrl+C to stop"

./reposense-ai
