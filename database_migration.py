"""
Database Migration System for RepoSense AI

Handles database schema creation, migrations, and version management.
Ensures smooth upgrades and initialization for new installations.
"""

import os
import sqlite3
import logging
from pathlib import Path
from typing import List, Dict, Optional, Any
from datetime import datetime


class DatabaseMigration:
    """Database migration and schema management system"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)
        
        # Migration definitions
        self.migrations = {
            1: self._migration_001_initial_schema,
            2: self._migration_002_add_indexes,
            3: self._migration_003_add_metadata_fields,
            4: self._migration_004_add_diff_field,
            5: self._migration_005_add_timestamps,
            6: self._migration_006_replace_diff_with_metadata,
            7: self._migration_007_add_changed_paths,
        }
    
    def initialize_database(self) -> bool:
        """Initialize database with latest schema"""
        try:
            # Ensure directory exists
            db_dir = Path(self.db_path).parent
            db_dir.mkdir(parents=True, exist_ok=True)
            
            # Check if database exists
            db_exists = Path(self.db_path).exists()
            
            if not db_exists:
                self.logger.info("Creating new database with latest schema")
                return self._create_fresh_database()
            else:
                self.logger.info("Database exists, checking for migrations")
                return self.migrate_database()
                
        except Exception as e:
            self.logger.error(f"Error initializing database: {e}")
            return False
    
    def migrate_database(self) -> bool:
        """Apply pending migrations to existing database"""
        try:
            current_version = self._get_current_version()
            latest_version = max(self.migrations.keys())
            
            if current_version >= latest_version:
                self.logger.info(f"Database is up to date (version {current_version})")
                return True
            
            self.logger.info(f"Migrating database from version {current_version} to {latest_version}")
            
            # Apply migrations in order
            for version in range(current_version + 1, latest_version + 1):
                if version in self.migrations:
                    self.logger.info(f"Applying migration {version}")
                    if not self._apply_migration(version):
                        self.logger.error(f"Migration {version} failed")
                        return False
                    self._update_version(version)
            
            self.logger.info("Database migration completed successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error during database migration: {e}")
            return False
    
    def _create_fresh_database(self) -> bool:
        """Create a fresh database with the latest schema"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Create migration tracking table first
                conn.execute("""
                    CREATE TABLE IF NOT EXISTS schema_migrations (
                        version INTEGER PRIMARY KEY,
                        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        description TEXT
                    )
                """)
                
                # Apply all migrations in order
                for version in sorted(self.migrations.keys()):
                    migration_func = self.migrations[version]
                    migration_func(conn)
                    
                    # Record migration
                    conn.execute(
                        "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
                        (version, migration_func.__name__)
                    )
                
                conn.commit()
                self.logger.info(f"Fresh database created with version {max(self.migrations.keys())}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error creating fresh database: {e}")
            return False
    
    def _get_current_version(self) -> int:
        """Get current database schema version"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                # Check if migration table exists
                cursor = conn.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='schema_migrations'
                """)
                
                if not cursor.fetchone():
                    return 0  # No migration table = version 0
                
                # Get latest version
                cursor = conn.execute("SELECT MAX(version) FROM schema_migrations")
                result = cursor.fetchone()
                return result[0] if result[0] is not None else 0
                
        except Exception as e:
            self.logger.error(f"Error getting current version: {e}")
            return 0
    
    def _apply_migration(self, version: int) -> bool:
        """Apply a specific migration"""
        try:
            migration_func = self.migrations[version]
            
            with sqlite3.connect(self.db_path) as conn:
                migration_func(conn)
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Error applying migration {version}: {e}")
            return False
    
    def _update_version(self, version: int):
        """Update migration version in database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                migration_func = self.migrations[version]
                conn.execute(
                    "INSERT INTO schema_migrations (version, description) VALUES (?, ?)",
                    (version, migration_func.__name__)
                )
                conn.commit()
        except Exception as e:
            self.logger.error(f"Error updating version to {version}: {e}")
    
    # Migration definitions
    
    def _migration_001_initial_schema(self, conn: sqlite3.Connection):
        """Migration 001: Create initial documents table"""
        conn.execute("""
            CREATE TABLE IF NOT EXISTS documents (
                id TEXT PRIMARY KEY,
                repository_id TEXT NOT NULL,
                repository_name TEXT NOT NULL,
                revision INTEGER NOT NULL,
                date TIMESTAMP NOT NULL,
                filename TEXT NOT NULL,
                filepath TEXT NOT NULL,
                size INTEGER NOT NULL,
                author TEXT NOT NULL,
                commit_message TEXT NOT NULL,
                file_modified_time REAL,
                processed_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
    
    def _migration_002_add_indexes(self, conn: sqlite3.Connection):
        """Migration 002: Add performance indexes"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_documents_repository_id ON documents(repository_id)",
            "CREATE INDEX IF NOT EXISTS idx_documents_revision ON documents(revision)",
            "CREATE INDEX IF NOT EXISTS idx_documents_date ON documents(date DESC)",
            "CREATE INDEX IF NOT EXISTS idx_documents_processed_time ON documents(processed_time DESC)",
            "CREATE INDEX IF NOT EXISTS idx_documents_repo_revision ON documents(repository_id, revision)"
        ]
        
        for index_sql in indexes:
            conn.execute(index_sql)
    
    def _migration_003_add_metadata_fields(self, conn: sqlite3.Connection):
        """Migration 003: Add LLM analysis metadata fields"""
        metadata_fields = [
            "ALTER TABLE documents ADD COLUMN code_review_recommended INTEGER",
            "ALTER TABLE documents ADD COLUMN code_review_priority TEXT",
            "ALTER TABLE documents ADD COLUMN documentation_impact INTEGER",
            "ALTER TABLE documents ADD COLUMN risk_level TEXT"
        ]
        
        for field_sql in metadata_fields:
            try:
                conn.execute(field_sql)
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e).lower():
                    raise  # Re-raise if it's not a duplicate column error
        
        # Add indexes for new fields
        new_indexes = [
            "CREATE INDEX IF NOT EXISTS idx_documents_code_review ON documents(code_review_recommended)",
            "CREATE INDEX IF NOT EXISTS idx_documents_doc_impact ON documents(documentation_impact)",
            "CREATE INDEX IF NOT EXISTS idx_documents_risk_level ON documents(risk_level)"
        ]
        
        for index_sql in new_indexes:
            conn.execute(index_sql)

    def _migration_004_add_diff_field(self, conn: sqlite3.Connection):
        """Migration 004: Add diff field for storing SCM diff content"""
        try:
            conn.execute("ALTER TABLE documents ADD COLUMN diff TEXT")
        except sqlite3.OperationalError as e:
            if "duplicate column name" not in str(e).lower():
                raise  # Re-raise if it's not a duplicate column error

    def _migration_005_add_timestamps(self, conn: sqlite3.Connection):
        """Migration 005: Add created_at and updated_at timestamp fields"""
        timestamp_fields = [
            "ALTER TABLE documents ADD COLUMN created_at TEXT DEFAULT CURRENT_TIMESTAMP",
            "ALTER TABLE documents ADD COLUMN updated_at TEXT DEFAULT CURRENT_TIMESTAMP"
        ]

        for field_sql in timestamp_fields:
            try:
                conn.execute(field_sql)
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e).lower():
                    raise  # Re-raise if it's not a duplicate column error

    def _migration_006_replace_diff_with_metadata(self, conn: sqlite3.Connection):
        """Migration 006: Replace diff column with repository metadata for on-demand diff generation"""
        metadata_fields = [
            "ALTER TABLE documents ADD COLUMN repository_url TEXT",
            "ALTER TABLE documents ADD COLUMN repository_type TEXT"
        ]

        for field_sql in metadata_fields:
            try:
                conn.execute(field_sql)
            except sqlite3.OperationalError as e:
                if "duplicate column name" not in str(e).lower():
                    raise  # Re-raise if it's not a duplicate column error

        # Note: We keep the diff column for backward compatibility but won't use it going forward
        # In a future migration, we could drop it after ensuring all systems are updated

    def _migration_007_add_changed_paths(self, conn: sqlite3.Connection):
        """Migration 007: Add changed_paths field to store list of files changed in commit"""
        conn.execute("""
            ALTER TABLE documents
            ADD COLUMN changed_paths TEXT
        """)
        self.logger.info("Added changed_paths column to documents table")

    def get_migration_status(self) -> Dict[str, Any]:
        """Get current migration status and information"""
        try:
            current_version = self._get_current_version()
            latest_version = max(self.migrations.keys())
            
            # Get applied migrations
            applied_migrations = []
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.execute("""
                        SELECT version, applied_at, description 
                        FROM schema_migrations 
                        ORDER BY version
                    """)
                    applied_migrations = [
                        {
                            'version': row[0],
                            'applied_at': row[1],
                            'description': row[2]
                        }
                        for row in cursor.fetchall()
                    ]
            except Exception:
                pass  # Migration table might not exist
            
            return {
                'current_version': current_version,
                'latest_version': latest_version,
                'needs_migration': current_version < latest_version,
                'applied_migrations': applied_migrations,
                'pending_migrations': [
                    v for v in self.migrations.keys() 
                    if v > current_version
                ]
            }
            
        except Exception as e:
            self.logger.error(f"Error getting migration status: {e}")
            return {
                'current_version': 0,
                'latest_version': max(self.migrations.keys()),
                'needs_migration': True,
                'applied_migrations': [],
                'pending_migrations': list(self.migrations.keys()),
                'error': str(e)
            }
