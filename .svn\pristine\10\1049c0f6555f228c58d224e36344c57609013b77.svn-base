# Repository Monitor - Pre-built Binary Deployment
# Minimal container for pre-built Linux binary

FROM ubuntu:22.04

# Install minimal runtime dependencies
RUN apt-get update && apt-get install -y \
    subversion \
    git \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user
RUN groupadd -r repository-monitor && \
    useradd -r -g repository-monitor -d /app -s /bin/bash repository-monitor

# Set working directory
WORKDIR /app

# Copy pre-built binary and files
COPY dist/linux-package/repository-monitor /app/repository-monitor
COPY dist/linux-package/config.example.json /app/config.example.json
COPY dist/linux-package/start-repository-monitor.sh /app/start-repository-monitor.sh

# Set permissions
RUN chmod +x /app/repository-monitor /app/start-repository-monitor.sh && \
    mkdir -p /app/data /app/logs && \
    chown -R repository-monitor:repository-monitor /app

# Switch to non-root user
USER repository-monitor

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Environment variables
ENV REPOSITORY_MONITOR_CONFIG=/app/data/config.json
ENV REPOSITORY_MONITOR_DATA_DIR=/app/data
ENV REPOSITORY_MONITOR_LOG_DIR=/app/logs

# Start the application
CMD ["/app/start-repository-monitor.sh"]
